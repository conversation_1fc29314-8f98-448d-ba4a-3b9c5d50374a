'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { Search, X, Users, MessageCircle } from 'lucide-react';
import { searchUsers } from '@/lib/actions/messaging/messaging-actions';
import { toast } from 'sonner';

interface User {
  id: string;
  full_name: string | null;
  avatar_url: string | null;
  email: string | null;
}

interface NewConversationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateConversation: (participantIds: string[], name?: string) => Promise<string>;
  currentUserId: string;
}

export function NewConversationDialog({
  open,
  onOpenChange,
  onCreateConversation,
  currentUserId: _currentUserId,
}: NewConversationDialogProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [conversationName, setConversationName] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  // Kullanıcı ara
  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);
      const results = await searchUsers(query);
      setSearchResults(results);
    } catch (error) {
      console.error('Kullanıcı arama hatası:', error);
      toast.error('Kullanıcı arama sırasında hata oluştu');
    } finally {
      setIsSearching(false);
    }
  };

  // Arama sorgusu değiştiğinde ara
  useEffect(() => {
    const timer = setTimeout(() => {
      handleSearch(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Kullanıcı seç/seçimi kaldır
  const toggleUserSelection = (user: User) => {
    setSelectedUsers(prev => {
      const isSelected = prev.some(u => u.id === user.id);
      if (isSelected) {
        return prev.filter(u => u.id !== user.id);
      } else {
        return [...prev, user];
      }
    });
  };

  // Seçili kullanıcıyı kaldır
  const removeSelectedUser = (userId: string) => {
    setSelectedUsers(prev => prev.filter(u => u.id !== userId));
  };

  // Konuşma oluştur
  const handleCreateConversation = async () => {
    if (selectedUsers.length === 0) {
      toast.error('En az bir kullanıcı seçmelisiniz');
      return;
    }

    try {
      setIsCreating(true);
      const participantIds = selectedUsers.map(u => u.id);
      const name = selectedUsers.length > 1 ? conversationName.trim() || undefined : undefined;
      
      await onCreateConversation(participantIds, name);
      
      // Dialog'u kapat ve formu temizle
      onOpenChange(false);
      resetForm();
      
      toast.success('Konuşma oluşturuldu');
    } catch (error) {
      console.error('Konuşma oluşturma hatası:', error);
      toast.error('Konuşma oluşturulamadı');
    } finally {
      setIsCreating(false);
    }
  };

  // Formu temizle
  const resetForm = () => {
    setSearchQuery('');
    setSearchResults([]);
    setSelectedUsers([]);
    setConversationName('');
  };

  // Dialog kapandığında formu temizle
  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  // Kullanıcı adını al
  const getUserName = (user: User) => {
    return user.full_name || user.email || 'Bilinmeyen Kullanıcı';
  };

  // Kullanıcı başlangıç harflerini al
  const getUserInitials = (user: User) => {
    const name = getUserName(user);
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Yeni Konuşma</DialogTitle>
          <DialogDescription>
            Mesajlaşmak istediğiniz kişileri seçin
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Kullanıcı arama */}
          <div className="space-y-2">
            <Label htmlFor="search">Kullanıcı Ara</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                placeholder="İsim veya e-posta ile ara..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>

          {/* Seçili kullanıcılar */}
          {selectedUsers.length > 0 && (
            <div className="space-y-2">
              <Label>Seçili Kullanıcılar</Label>
              <div className="flex flex-wrap gap-2">
                {selectedUsers.map(user => (
                  <Badge
                    key={user.id}
                    variant="secondary"
                    className="flex items-center gap-2 pr-1"
                  >
                    <Avatar className="h-4 w-4">
                      <AvatarImage src={user.avatar_url || undefined} />
                      <AvatarFallback className="text-xs">
                        {getUserInitials(user)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-xs">{getUserName(user)}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                      onClick={() => removeSelectedUser(user.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Grup konuşması adı */}
          {selectedUsers.length > 1 && (
            <div className="space-y-2">
              <Label htmlFor="conversation-name">Grup Adı (İsteğe Bağlı)</Label>
              <Input
                id="conversation-name"
                placeholder="Grup konuşması adı..."
                value={conversationName}
                onChange={(e) => setConversationName(e.target.value)}
              />
            </div>
          )}

          {/* Arama sonuçları */}
          <div className="space-y-2">
            <Label>Arama Sonuçları</Label>
            <ScrollArea className="h-48 border rounded-md">
              {isSearching ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">Aranıyor...</p>
                  </div>
                </div>
              ) : searchResults.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center p-4">
                    <MessageCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      {searchQuery ? 'Kullanıcı bulunamadı' : 'Aramaya başlamak için yazın'}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="p-2">
                  {searchResults.map((user, index) => {
                    const isSelected = selectedUsers.some(u => u.id === user.id);
                    
                    return (
                      <div key={user.id}>
                        <div
                          className={cn(
                            'flex items-center gap-3 p-2 rounded-md cursor-pointer hover:bg-muted/50 transition-colors',
                            isSelected && 'bg-primary/10'
                          )}
                          onClick={() => toggleUserSelection(user)}
                        >
                          <Checkbox
                            checked={isSelected}
                            onChange={() => toggleUserSelection(user)}
                          />
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={user.avatar_url || undefined} />
                            <AvatarFallback className="text-xs">
                              {getUserInitials(user)}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">
                              {getUserName(user)}
                            </p>
                            {user.email && (
                              <p className="text-xs text-muted-foreground truncate">
                                {user.email}
                              </p>
                            )}
                          </div>
                        </div>
                        {index < searchResults.length - 1 && (
                          <Separator className="my-1" />
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </ScrollArea>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isCreating}
          >
            İptal
          </Button>
          <Button
            onClick={handleCreateConversation}
            disabled={selectedUsers.length === 0 || isCreating}
          >
            {isCreating ? (
              <>
                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2" />
                Oluşturuluyor...
              </>
            ) : (
              <>
                <Users className="h-4 w-4 mr-2" />
                Konuşma Oluştur
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
