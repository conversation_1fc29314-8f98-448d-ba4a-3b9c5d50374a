"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Users, Dumbbell, Crown } from "lucide-react";

export function RoleFeatures() {
  const roles = [
    {
      id: "member",
      title: "<PERSON><PERSON>",
      description:
        "<PERSON>lara katıl, antrenmanlarını planla ve ilerlemeni takip et.",
      icon: Users,
      badge: { label: "Ücretsiz", variant: "secondary" as const },
      features: [
        "Salon bul, filtrele (şehir, özellik, puan)",
        "Üyelik başvurusu ve davet yönetimi",
        "<PERSON><PERSON><PERSON> ve ders rezervasyonu",
        "Antrenman ve hedef takibi",
        "<PERSON><PERSON><PERSON><PERSON>, ölçüm ve raporlar",
        "Paketleri görüntüleme ve yenileme",
        "Değerlendirme ve yorum bırakma",
        "<PERSON><PERSON><PERSON><PERSON> terc<PERSON> (e‑posta/push)",
      ],
      cta: { label: "<PERSON><PERSON> olarak keşfet", href: "/features" },
    },
    {
      id: "trainer",
      title: "<PERSON>trenö<PERSON>",
      description:
        "Müşterilerini yönet, programlar oluştur ve gelirini takip et.",
      icon: Dumbbell,
      badge: { label: "Popüler", variant: "default" as const },
      features: [
        "Müşteri ve randevu yönetimi",
        "Kişiye özel program ve ders planları",
        "Çakışma önleme ve hatırlatmalar",
        "Paket/oturum bazlı gelir takibi",
        "Değerlendirme ve geri bildirim",
        "Salon(lar) ile çalışma ve davet/başvuru",
        "Haftalık/aylık performans istatistikleri",
        "İzin tabanlı işlem yetkileri",
      ],
      cta: { label: "Antrenör özellikleri", href: "/features" },
    },
    {
      id: 'manager',
      title: "Yönetici",
      description:
        "Şubenizi uçtan uca yönetin, ekip ve finansı tek panelden kontrol edin.",
      icon: Crown,
      badge: { label: "Premium", variant: "destructive" as const },
      features: [
        "Çoklu şube ve merkezi rol yönetimi",
        "Üyelik ve paket süreçleri (yönetimsel)",
        "Gelişmiş analitik (gelir, doluluk, performans)",
        "Randevu politikaları ve kaynak planlama",
        "Güvenlik, RLS ve KVKK uyumu",
        "İş zekâsı panoları ve dışa aktarma (CSV)",
        "Otomasyon ve bildirim akışları",
      ],
      cta: { label: "Yönetici özellikleri", href: "/features" },
    },
  ];

  return (
    <section className="py-20 lg:py-28">
      <div className="container mx-auto px-4">
        <div className="mx-auto mb-10 max-w-2xl text-center">
          <Badge variant="outline" className="mb-3">Roller</Badge>
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            Her rol için net faydalar
          </h2>
          <p className="text-muted-foreground mt-3 text-base">
            Üye, Antrenör ve Yönetici deneyimleri ayrı ayrı optimize edildi. İhtiyacınıza uygun
            akışları keşfedin.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {roles.map((role) => {
            const Icon = role.icon;
            return (
              <Card key={role.id} className="h-full">
                <CardHeader>
                  <div className="mb-2 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="inline-flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                        <Icon className="h-5 w-5 text-primary" />
                      </span>
                      <CardTitle className="text-lg">{role.title}</CardTitle>
                    </div>
                    <Badge variant={role.badge.variant}>{role.badge.label}</Badge>
                  </div>
                  <p className="text-muted-foreground text-sm">{role.description}</p>
                </CardHeader>
                <CardContent className="space-y-3">
                  <ul className="space-y-2">
                    {role.features.map((f, idx) => (
                      <li key={idx} className="text-sm">
                        <span className="text-primary mr-2">•</span>
                        {f}
                      </li>
                    ))}
                  </ul>
                  <Button asChild variant="outline" className="mt-2 w-full">
                    <Link href={role.cta.href}>{role.cta.label}</Link>
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
}


