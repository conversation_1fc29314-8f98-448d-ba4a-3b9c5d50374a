'use client';

import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Inbox, Send } from 'lucide-react';
import { InvitationList } from './invitation-list';
import { isInvitationExpired } from './types';
import type { InvitationTabsProps } from './types';

export function InvitationTabs({
  incomingInvitations,
  outgoingInvitations,
  onAction,
  userType,
}: InvitationTabsProps) {
  
  // Count pending invitations
  const pendingIncoming = incomingInvitations.filter(inv => 
    inv.status === 'pending' && !isInvitationExpired(inv)
  ).length;
  
  const pendingOutgoing = outgoingInvitations.filter(inv => 
    inv.status === 'pending' && !isInvitationExpired(inv)
  ).length;

  // Get labels based on user type
  const getLabels = () => {
    switch (userType) {
      case 'member':
        return {
          incoming: '<PERSON><PERSON><PERSON>',
          outgoing: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
          incomingEmpty: 'Henüz size gönderilen davet bulunmuyor',
          incomingEmptyDesc: 'Salon yöneticilerinden gelen davetler burada görünecektir.',
          outgoingEmpty: 'Henüz salon talebiniz bulunmuyor',
          outgoingEmptyDesc: 'Salonlara gönderdiğiniz üyelik talepleri burada görünecektir.',
        };
      case 'trainer':
        return {
          incoming: 'Gelen Davetler',
          outgoing: 'Gönderilen Talepler',
          incomingEmpty: 'Henüz size gönderilen davet bulunmuyor',
          incomingEmptyDesc: 'Salon yöneticilerinden gelen antrenörlük davetleri burada görünecektir.',
          outgoingEmpty: 'Henüz salon talebiniz bulunmuyor',
          outgoingEmptyDesc: 'Salonlara gönderdiğiniz antrenörlük talepleri burada görünecektir.',
        };
      case 'gym':
        return {
          incoming: 'Gelen Talepler',
          outgoing: 'Gönderilen Davetler',
          incomingEmpty: 'Henüz talep bulunmuyor',
          incomingEmptyDesc: 'Üye ve antrenörlerden gelen talepler burada görünecektir.',
          outgoingEmpty: 'Henüz davet gönderilmemiş',
          outgoingEmptyDesc: 'Gönderdiğiniz davetler burada görünecektir.',
        };
      default:
        return {
          incoming: 'Gelen',
          outgoing: 'Gönderilen',
          incomingEmpty: 'Henüz davet bulunmuyor',
          incomingEmptyDesc: 'Davetler burada görünecektir.',
          outgoingEmpty: 'Henüz davet bulunmuyor',
          outgoingEmptyDesc: 'Davetler burada görünecektir.',
        };
    }
  };

  const labels = getLabels();

  return (
    <Tabs defaultValue="incoming" className="space-y-6">
      <TabsList className="grid w-full grid-cols-2 bg-muted/50 dark:bg-muted/30">
        <TabsTrigger
          value="incoming"
          className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm duration-300"
        >
          <Inbox className="h-4 w-4" />
          {labels.incoming}
          {pendingIncoming > 0 && (
            <Badge variant="secondary" className="ml-1 text-xs">
              {pendingIncoming}
            </Badge>
          )}
        </TabsTrigger>
        
        <TabsTrigger
          value="outgoing"
          className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm duration-300"
        >
          <Send className="h-4 w-4" />
          {labels.outgoing}
          {pendingOutgoing > 0 && (
            <Badge variant="secondary" className="ml-1 text-xs">
              {pendingOutgoing}
            </Badge>
          )}
        </TabsTrigger>
      </TabsList>

      <TabsContent value="incoming" className="space-y-6">
        <InvitationList
          invitations={incomingInvitations}
          onAction={onAction}
          emptyMessage={labels.incomingEmpty}
          emptyDescription={labels.incomingEmptyDesc}
          showActions={true}
          groupByStatus={true}
        />
      </TabsContent>

      <TabsContent value="outgoing" className="space-y-6">
        <InvitationList
          invitations={outgoingInvitations}
          onAction={onAction}
          emptyMessage={labels.outgoingEmpty}
          emptyDescription={labels.outgoingEmptyDesc}
          showActions={true}
          groupByStatus={true}
        />
      </TabsContent>
    </Tabs>
  );
}
