'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Check, CheckCircle2 } from 'lucide-react';
import { changeCompanyPackage } from '@/lib/actions/business/change-company-package';
import type { PlatformPackages } from '@/types/database/tables';
import { cn } from '@/lib/utils';

interface PackageUpgradeStepProps {
  packages: PlatformPackages[];
  currentPackageId: string | null;
  onSuccess: () => void;
}

export function PackageUpgradeStep({
  packages,
  currentPackageId,
  onSuccess,
}: PackageUpgradeStepProps) {
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [successPkg, setSuccessPkg] = useState<PlatformPackages | null>(null);

  const handleUpdate = async () => {
    if (!selectedId) return;
    setIsLoading(true);
    try {
      const result = await changeCompanyPackage(selectedId);
      if (result.success) {
        const pkg = packages.find(p => p.id === selectedId) || null;
        setSuccessPkg(pkg);
        toast.success(result.message || 'Paket güncellendi');
        onSuccess();
      } else {
        toast.error(result.error || 'Paket güncellenemedi');
      }
    } catch (e) {
      toast.error(e instanceof Error ? e.message : 'Beklenmeyen hata');
    } finally {
      setIsLoading(false);
    }
  };

  if (successPkg) {
    return (
      <Card className="border-green-200 bg-green-50/60 dark:border-green-900/40 dark:bg-green-950/30">
        <CardHeader>
          <div className="flex items-center gap-3">
            <CheckCircle2 className="h-6 w-6 text-green-600" />
            <CardTitle>Paketiniz güncellendi!</CardTitle>
          </div>
          <CardDescription className="mt-2">
            Yeni paketiniz: <strong>{successPkg.name}</strong>
          </CardDescription>
        </CardHeader>
        <CardFooter>
          <Button onClick={onSuccess}>Sayfayı Yenile</Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        {packages.map(pkg => {
          const isSelected = selectedId === pkg.id;
          const isCurrent = currentPackageId === pkg.id;
          return (
            <Card
              key={pkg.id}
              className={cn(
                'relative cursor-pointer transition-shadow',
                isSelected ? 'ring-primary shadow-lg ring-2' : 'hover:shadow',
                isCurrent && 'pointer-events-none opacity-60'
              )}
              onClick={() => !isCurrent && setSelectedId(pkg.id)}
            >
              {isCurrent && (
                <span className="bg-primary absolute top-2 right-2 rounded px-2 py-0.5 text-xs text-white">
                  Mevcut
                </span>
              )}
              <CardHeader>
                <CardTitle>{pkg.name}</CardTitle>
                <CardDescription>
                  {pkg.duration === 'yearly'
                    ? 'Yıllık'
                    : pkg.duration === 'monthly'
                      ? 'Aylık'
                      : 'Ücretsiz'}{' '}
                  • {pkg.price}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {Array.isArray(pkg.features) && (
                  <ul className="space-y-2 text-sm">
                    {(pkg.features as unknown[]).map((f, idx) => (
                      <li key={idx} className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-green-600" /> {String(f)}
                      </li>
                    ))}
                  </ul>
                )}
              </CardContent>
              <CardFooter>
                <Button
                  variant={isSelected ? 'default' : 'outline'}
                  disabled={isCurrent}
                  className="w-full"
                >
                  {isCurrent
                    ? 'Mevcut Paket'
                    : isSelected
                      ? 'Seçildi'
                      : 'Bu Paketi Seç'}
                </Button>
              </CardFooter>
            </Card>
          );
        })}
      </div>
      <Button
        onClick={handleUpdate}
        disabled={!selectedId || isLoading}
        className="w-full"
      >
        {isLoading ? 'Güncelleniyor...' : 'Paketi Güncelle'}
      </Button>
    </div>
  );
}
