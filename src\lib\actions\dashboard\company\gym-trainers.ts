'use server';
import { ApiResponse } from '@/types';
import { createAction } from '../../core/core';

/**
 * Trainer'ı salondan çıkarır
 */
export async function removeTrainerFromGym(
  trainerId: string,
  gymId: string
): Promise<ApiResponse<void>> {
  return await createAction<void>(async (_, supabase, userId) => {
    // Önce mevcut kaydı kontrol et
    const { data: existingRecord, error: selectError } = await supabase
      .from('gym_trainers')
      .select('*')
      .eq('trainer_profile_id', trainerId)
      .eq('gym_id', gymId)
      .eq('status', 'active')
      .maybeSingle();

    if (selectError) {
      console.error('❌ Kayıt kontrol hatası:', selectError);
      throw new Error('Kayıt kontrol edilirken hata: ' + selectError.message);
    }

    if (!existingRecord) {
      console.warn('⚠️ Aktif trainer kaydı bulunamadı');
      throw new Error('Aktif trainer kaydı bulunamadı');
    }

    // Manager yet<PERSON>ini kontrol et (company üzerinden)
    const { data: gymData, error: gymError } = await supabase
      .from('gyms')
      .select(
        `
        id,
        company:companies!inner(
          id,
          manager_profile_id
        )
      `
      )
      .eq('id', gymId)
      .single();

    if (gymError) {
      console.error('❌ Gym kontrol hatası:', gymError);
      throw new Error('Salon bilgisi alınamadı: ' + gymError.message);
    }

    if ((gymData.company as any).manager_profile_id !== userId) {
      console.error('❌ Yetki hatası - kullanıcı manager değil');
      throw new Error('Bu işlem için yetkiniz yok');
    }

    // Update işlemini gerçekleştir
    const updateData = {
      status: 'left',
      left_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const { data: updateResult, error: updateError } = await supabase
      .from('gym_trainers')
      .update(updateData)
      .eq('trainer_profile_id', trainerId)
      .eq('gym_id', gymId)
      .eq('status', 'active')
      .select('*');

    if (updateError) {
      console.error('❌ Update hatası:', updateError);
      throw new Error('Trainer salondan çıkarılamadı: ' + updateError.message);
    }

    if (!updateResult || updateResult.length === 0) {
      console.warn('⚠️ Hiçbir kayıt güncellenmedi');
      throw new Error('Güncelleme işlemi başarısız - hiçbir kayıt etkilenmedi');
    }
  });
}
