'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Crown,
  ArrowRight,
  CheckCircle,
  Star,
  Zap,
  Users,
  TrendingUp,
  Shield,
} from 'lucide-react';
import Link from 'next/link';

export function FeaturesCTA() {
  const benefits = [
    { icon: Users, text: 'Binlerce salon tarafından tercih ediliyor' },
    { icon: TrendingUp, text: '%40 daha hızlı büyüme sağlayın' },
    { icon: Shield, text: 'Güvenli ve güvenilir altyapı' },
    { icon: Star, text: 'Premium destek ve eğitim' },
  ];

  const features = [
    'Sın<PERSON>rs<PERSON>z üye kaydı',
    'Gelişmiş analitik raporlar',
    'Mobil uygulama desteği',
    '7/24 teknik destek',
    'Özelleştirilebilir arayüz',
    'Güvenli ödeme sistemi',
  ];

  return (
    <section className="from-primary/5 via-background to-primary/5 relative overflow-hidden bg-gradient-to-br py-20 lg:py-32">
      {/* Background Elements */}
      <div className="bg-grid-pattern absolute inset-0 opacity-5"></div>
      <div className="bg-primary/10 absolute top-10 right-10 h-64 w-64 rounded-full blur-3xl"></div>
      <div className="bg-primary/5 absolute bottom-10 left-10 h-80 w-80 rounded-full blur-3xl"></div>

      <div className="relative z-10 container mx-auto px-4">
        <div className="mx-auto max-w-4xl">
          {/* Main CTA Section */}
          <div className="bg-background/80 border-border/50 mb-12 rounded-3xl border p-8 text-center shadow-2xl backdrop-blur-sm md:p-12">
            <Badge
              variant="outline"
              className="mb-6 px-4 py-2 text-sm font-medium"
            >
              <Zap className="mr-2 h-4 w-4" />
              Hemen Başlayın
            </Badge>

            <h2 className="text-primary mb-6 text-3xl font-bold md:text-4xl">
              Spor Salonu İşletmeciliğinde
              <span className="text-foreground block">
                Dijital Dönüşümü Yaşayın
              </span>
            </h2>

            <p className="text-muted-foreground mx-auto mb-8 max-w-2xl text-lg leading-relaxed">
              Sportiva ile işletmenizi modernleştirin, verimliliği artırın ve
              rekabette öne geçin. Profesyonel spor salonu yönetimi artık çok
              daha kolay.
            </p>

            {/* Benefits Grid */}
            <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-2">
              {benefits.map((benefit, index) => {
                const IconComponent = benefit.icon;
                return (
                  <div
                    key={index}
                    className="bg-muted/30 border-border/50 flex items-center gap-3 rounded-xl border p-4"
                  >
                    <div className="bg-primary/10 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-lg">
                      <IconComponent className="text-primary h-5 w-5" />
                    </div>
                    <span className="text-foreground text-sm font-medium">
                      {benefit.text}
                    </span>
                  </div>
                );
              })}
            </div>

            {/* CTA Buttons */}
            <div className="mb-8 flex flex-col justify-center gap-4 sm:flex-row">
              <Button asChild size="lg" className="shadow-lg">
                <Link href="/auth/register">
                  <Crown className="mr-2 h-5 w-5" />
                  Ücretsiz Denemeyi Başlatın
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/onboarding">
                  Fiyatları İnceleyin
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="text-muted-foreground flex flex-wrap items-center justify-center gap-6 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle className="text-primary h-4 w-4" />
                <span>Kredi kartı gerekmez</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="text-primary h-4 w-4" />
                <span>Anında kurulum</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="text-primary h-4 w-4" />
                <span>İstediğiniz zaman iptal</span>
              </div>
            </div>
          </div>

          {/* Features List */}
          <div className="bg-muted/30 border-border rounded-2xl border p-8 md:p-12">
            <div className="mb-8 text-center">
              <h3 className="text-primary mb-4 text-2xl font-bold">
                Tüm Bu Özellikler Dahil
              </h3>
              <p className="text-muted-foreground">
                Sportiva ile spor salonu yönetiminin tüm ihtiyaçlarınızı
                karşılayın
              </p>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  <CheckCircle className="text-primary h-5 w-5 flex-shrink-0" />
                  <span className="text-foreground text-sm">{feature}</span>
                </div>
              ))}
            </div>

            <div className="mt-8 text-center">
              <Button asChild variant="outline" size="lg">
                <Link href="/about">
                  Daha Fazla Bilgi Alın
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
