import { ManagerInformation } from '@/components/profile-settings/ManagerInformation';
import { getManagerDetail } from '@/lib/actions/user/manager-actions';
import { ManagerSubWithPackageDetails } from '@/lib/actions/user/profile-actions';
import { getUserRoles } from '@/lib/auth/server-auth';
import { redirect } from 'next/navigation';

export default async function CompanySettingsPage() {
  // Kullanıcının manager rolü olup olma<PERSON>ını kontrol et
  const userRoles = await getUserRoles();
  const isManager = userRoles.includes('manager');

  // Manager değilse profil sayfasına yönlendir
  if (!isManager) {
    redirect('/profile/settings/profile');
  }

  // Server action ile manager details verilerini çek
  const managerDetailsResult = await getManagerDetail();

  const managerDetails: ManagerSubWithPackageDetails | null =
    managerDetailsResult.success && managerDetailsResult.data
      ? managerDetailsResult.data
      : null;

  // Hata durumunda konsola log
  if (!managerDetailsResult.success) {
    console.warn('Manager details not found:', managerDetailsResult.error);
  }

  return (
    <div className="flex-1 space-y-6 p-6 lg:p-8">
      {/* Sayfa başlığı */}
      <div className="space-y-2">
        <h1 className="text-2xl font-semibold tracking-tight">
          Yönetici Bilgileri
        </h1>
        <p className="text-muted-foreground">
          Abonelik durumunuzu ve paket özelliklerinizi görüntüleyin
        </p>
      </div>

      {/* Yönetici bilgileri formu */}
      <div className="space-y-6">
        <ManagerInformation managerDetails={managerDetails} />
      </div>
    </div>
  );
}
