'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON><PERSON>,
  Users,
  CreditCard,
  BarChart4,
  Calendar,
  ArrowRight,
  Star,
  Zap,
} from 'lucide-react';
import Link from 'next/link';

export function FeaturesHero() {
  const highlights = [
    { icon: Users, text: '<PERSON><PERSON>' },
    { icon: CreditCard, text: '<PERSON>de<PERSON>' },
    { icon: BarChart4, text: 'Analitik Raporlar' },
    { icon: Calendar, text: '<PERSON><PERSON><PERSON>' },
  ];

  return (
    <section className="from-background via-muted/30 to-background relative overflow-hidden bg-gradient-to-br py-20 lg:py-32">
      {/* Background Pattern */}
      <div className="bg-grid-pattern absolute inset-0 opacity-5"></div>

      {/* Gradient Orbs */}
      <div className="bg-primary/10 absolute top-20 left-10 h-72 w-72 rounded-full blur-3xl"></div>
      <div className="bg-primary/5 absolute right-10 bottom-20 h-96 w-96 rounded-full blur-3xl"></div>

      <div className="relative z-10 container mx-auto px-4">
        <div className="mx-auto max-w-4xl text-center">
          {/* Badge */}
          <Badge
            variant="outline"
            className="mb-6 px-4 py-2 text-sm font-medium"
          >
            <Zap className="mr-2 h-4 w-4" />
            Güçlü Özellikler
          </Badge>

          {/* Main Heading */}
          <h1 className="text-primary mb-6 text-4xl leading-tight font-bold md:text-5xl lg:text-6xl">
            Spor Salonu Yönetiminde
            <span className="text-foreground block">Yeni Nesil Çözümler</span>
          </h1>

          {/* Description */}
          <p className="text-muted-foreground mx-auto mb-8 max-w-3xl text-lg leading-relaxed md:text-xl">
            Sportiva ile spor salonu işletmeciliğinin her alanında dijital
            dönüşümü yaşayın. Modern araçlar, akıllı analizler ve kullanıcı
            dostu arayüzle işletmenizi zirveye taşıyın.
          </p>

          {/* Feature Highlights */}
          <div className="mb-10 flex flex-wrap items-center justify-center gap-4">
            {highlights.map((highlight, index) => {
              const IconComponent = highlight.icon;
              return (
                <div
                  key={index}
                  className="bg-muted/50 border-border/50 flex items-center gap-2 rounded-full border px-4 py-2"
                >
                  <IconComponent className="text-primary h-4 w-4" />
                  <span className="text-foreground text-sm font-medium">
                    {highlight.text}
                  </span>
                </div>
              );
            })}
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col justify-center gap-4 sm:flex-row">
            <Button asChild size="lg" className="shadow-lg">
              <Link href="/auth/register">
                <Dumbbell className="mr-2 h-5 w-5" />
                Ücretsiz Başlayın
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/onboarding">
                Fiyatları İnceleyin
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="text-muted-foreground mt-10 flex flex-wrap items-center justify-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <Star className="text-primary h-4 w-4" />
              <span>7/24 Destek</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="text-primary h-4 w-4" />
              <span>Güvenli Altyapı</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="text-primary h-4 w-4" />
              <span>Kolay Kurulum</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
