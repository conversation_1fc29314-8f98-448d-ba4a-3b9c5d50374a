'use client';

import { CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useEffect, useState } from 'react';

interface SuccessAnimationProps {
  show: boolean;
  message?: string;
  className?: string;
  onComplete?: () => void;
}

export function SuccessAnimation({
  show,
  message = 'Başarılı!',
  className,
  onComplete,
}: SuccessAnimationProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (show) {
      setIsVisible(true);
      const timer = setTimeout(() => {
        setIsVisible(false);
        onComplete?.();
      }, 2000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [show, onComplete]);

  if (!isVisible) return null;

  return (
    <div
      className={cn(
        'fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm',
        className
      )}
    >
      <div className="animate-in zoom-in-95 transform rounded-2xl bg-white p-8 shadow-2xl duration-300 dark:bg-gray-800">
        <div className="space-y-4 text-center">
          <div className="relative">
            <CheckCircle className="mx-auto h-16 w-16 animate-pulse text-green-500" />
            <div className="absolute inset-0 mx-auto h-16 w-16 animate-ping rounded-full bg-green-500/20" />
          </div>
          <p className="text-foreground text-lg font-semibold">{message}</p>
        </div>
      </div>
    </div>
  );
}
