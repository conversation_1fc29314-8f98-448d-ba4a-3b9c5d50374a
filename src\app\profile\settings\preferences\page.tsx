import { ThemeSettings } from '@/components/profile-settings/ThemeSettings';
import { NotificationSettings } from '@/components/profile-settings/NotificationSettings';
import { PreferenceSettings } from '@/components/profile-settings/PreferenceSettings';
import { getUserSettings } from '@/lib/actions/user/user-settings-actions';
import { UserSettings } from '@/types/database/tables';

export default async function PreferencesSettingsPage() {
  // Server action ile user settings verilerini çek
  const userSettingsResult = await getUserSettings();

  const userSettings: UserSettings | null =
    userSettingsResult.success && userSettingsResult.data
      ? userSettingsResult.data
      : null;

  // Hata durumunda konsola log
  if (!userSettingsResult.success) {
    console.warn('User settings not found:', userSettingsResult.error);
  }

  return (
    <div className="flex-1 space-y-6 p-6 lg:p-8">
      {/* <PERSON><PERSON> ba<PERSON>lığı */}
      <div className="space-y-2">
        <h1 className="text-2xl font-semibold tracking-tight">
          Kişiselleştirme
        </h1>
        <p className="text-muted-foreground">
          Görünüm, bildirimler ve diğer tercihlerinizi yönetin
        </p>
      </div>

      {/* Tercihler içeriği */}
      {userSettings ? (
        <div className="space-y-6">
          <ThemeSettings />
          <NotificationSettings settings={userSettings} />
          <PreferenceSettings settings={userSettings} />
        </div>
      ) : (
        <div className="py-8 text-center">
          <p className="text-muted-foreground">
            Ayarlar yüklenirken hata oluştu. Lütfen sayfayı yenileyin.
          </p>
        </div>
      )}
    </div>
  );
}
