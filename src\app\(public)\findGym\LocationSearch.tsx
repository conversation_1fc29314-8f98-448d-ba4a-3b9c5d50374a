'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { MapPin, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { CITIES } from '@/lib/constants';

interface LocationOption {
  type: 'city' | 'district';
  cityId: string;
  cityName: string;
  district?: string;
  displayText: string;
}

interface LocationSearchProps {
  selectedCity: string;
  selectedDistrict: string;
  onLocationChange: (city: string, district: string) => void;
}

export function LocationSearch({
  selectedCity,
  selectedDistrict,
  onLocationChange,
}: LocationSearchProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [options, setOptions] = useState<LocationOption[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Seçili konumu göster
  const getDisplayValue = () => {
    if (selectedCity === 'all') return '';

    const city = CITIES.find(c => c.id.toString() === selectedCity);
    if (!city) return '';

    if (selectedDistrict === 'all') {
      return city.name;
    }

    return `${selectedDistrict}, ${city.name}`;
  };

  // Türkçe karakter normalizasyon fonksiyonu
  const normalizeText = (text: string): string => {
    return text
      .toLowerCase()
      .replace(/ğ/g, 'g')
      .replace(/ü/g, 'u')
      .replace(/ş/g, 's')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ç/g, 'c')
      .replace(/İ/g, 'i')
      .replace(/i̇/g, 'i') // Noktalı i'yi normal i'ye çevir
      .replace(/I/g, 'i'); // Büyük I'yı da i'ye çevir
  };

  // Arama seçeneklerini oluştur
  const generateOptions = useCallback((term: string): LocationOption[] => {
    if (!term.trim()) return [];

    const searchTerms = term.trim().toLowerCase().split(/\s+/);
    const results: LocationOption[] = [];

    CITIES.forEach(city => {
      const cityNameNormalized = normalizeText(city.name);

      // Şehir adı kontrolü - tüm arama terimlerinin şehir adında olup olmadığını kontrol et
      const cityMatches = searchTerms.every(searchTerm => {
        const normalizedSearchTerm = normalizeText(searchTerm);
        return cityNameNormalized.includes(normalizedSearchTerm);
      });

      if (cityMatches) {
        // Şehir eşleşti, şehri ve tüm ilçelerini ekle
        results.push({
          type: 'city',
          cityId: city.id.toString(),
          cityName: city.name,
          displayText: `${city.name}, Türkiye`,
        });

        city.districts.forEach(district => {
          results.push({
            type: 'district',
            cityId: city.id.toString(),
            cityName: city.name,
            district,
            displayText: `${district}, ${city.name}, Türkiye`,
          });
        });
      } else {
        // Şehir eşleşmedi, ilçelerde ara
        city.districts.forEach(district => {
          const districtNormalized = normalizeText(district);
          const fullLocationNormalized = `${districtNormalized} ${cityNameNormalized}`;

          // Çok kelimeli arama için: tüm arama terimlerinin ilçe+şehir kombinasyonunda olup olmadığını kontrol et
          const districtMatches = searchTerms.every(searchTerm => {
            const normalizedSearchTerm = normalizeText(searchTerm);
            return fullLocationNormalized.includes(normalizedSearchTerm);
          });

          if (districtMatches) {
            results.push({
              type: 'district',
              cityId: city.id.toString(),
              cityName: city.name,
              district,
              displayText: `${district}, ${city.name}, Türkiye`,
            });
          }
        });
      }
    });

    // Sonuçları öncelik sırasına göre sırala
    results.sort((a, b) => {
      // Şehirler önce
      if (a.type === 'city' && b.type === 'district') return -1;
      if (a.type === 'district' && b.type === 'city') return 1;

      // Aynı tipte ise alfabetik sırala
      return a.displayText.localeCompare(b.displayText, 'tr');
    });

    return results.slice(0, 15);
  }, []);

  // Arama terimi değiştiğinde seçenekleri güncelle
  useEffect(() => {
    const newOptions = generateOptions(searchTerm);
    setOptions(newOptions);
    setIsOpen(newOptions.length > 0 && searchTerm.length > 0);
  }, [searchTerm, generateOptions]);

  // Dışarı tıklandığında kapat
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleOptionSelect = (option: LocationOption) => {
    if (option.type === 'city') {
      onLocationChange(option.cityId, 'all');
    } else {
      onLocationChange(option.cityId, option.district || 'all');
    }
    setSearchTerm('');
    setIsOpen(false);
    inputRef.current?.blur();
  };

  const handleClear = () => {
    onLocationChange('all', 'all');
    setSearchTerm('');
    setIsOpen(false);
  };

  const displayValue = getDisplayValue();

  return (
    <div className="relative">
      <div className="relative">
        <MapPin className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={displayValue || 'Şehir veya ilçe ara...'}
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          onFocus={() => {
            if (searchTerm) {
              setIsOpen(options.length > 0);
            }
          }}
          className="bg-background border-border/50 hover:border-primary/50 h-10 pr-10 pl-10 transition-colors duration-300"
        />
        {displayValue && (
          <button
            type="button"
            onClick={handleClear}
            className="text-muted-foreground hover:text-foreground absolute top-1/2 right-3 -translate-y-1/2"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Dropdown */}
      {isOpen && options.length > 0 && (
        <div
          ref={dropdownRef}
          className="bg-background absolute top-full right-0 left-0 z-50 mt-1 max-h-60 overflow-auto rounded-md border shadow-lg"
        >
          {options.map((option, index) => (
            <button
              key={`${option.cityId}-${option.district || 'city'}-${index}`}
              type="button"
              onClick={() => handleOptionSelect(option)}
              className="hover:bg-muted flex w-full items-center gap-2 px-3 py-2 text-left text-sm transition-colors"
            >
              <MapPin className="text-muted-foreground h-4 w-4 flex-shrink-0" />
              <span className="truncate">{option.displayText}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
