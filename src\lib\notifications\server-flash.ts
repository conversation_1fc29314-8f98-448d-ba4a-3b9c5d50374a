'use server';

import { cookies } from 'next/headers';

export type FlashToastPayload = {
  type?: 'success' | 'error' | 'info';
  message: string;
};

/**
 * Server Actions veya route handlers içinde kullanın.
 * <PERSON><PERSON><PERSON> sayfa yüklemesinde global toast olarak bir kez gösterilir.
 */
export async function enqueueFlashToast(payload: FlashToastPayload) {
  const store = await cookies();
  store.set('flash_toast', JSON.stringify(payload), {
    path: '/',
    httpOnly: false, // client tarafında okunacak
    sameSite: 'lax',
    maxAge: 60, // 1 dakika
  });
}
