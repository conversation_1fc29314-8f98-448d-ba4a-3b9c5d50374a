'use server';

import {
  G<PERSON><PERSON>ardData,
  GymSearchFilters,
  GymSearchResult,
  GymStats,
} from '@/types/business/gym';
import { getSupabaseAdmin } from '../../supabase/admin';
import { createAction, createActionPublic } from '../core/core';
import { ApiResponse } from '@/types/global/api';
import { Gyms } from '@/types/database/tables';
import { getCityNameById } from '@/lib/constants';
import {
  getSortConfig,
  transformToGymCard,
  validateSlug,
  validateSearchQuery,
  sanitizeSearchQuery,
  createSearchVector,
  roundRating,
} from './utils';

/**
 * Comprehensive gym membership status
 */
export interface ComprehensiveGymMembershipStatus {
  hasActiveMembership: boolean;
  isGymOwner: boolean;
  pendingInvitation: any | null;
  membershipStatus: 'none' | 'pending' | 'active';
}

// ============================================================================
// CONSTANTS
// ============================================================================

const DEFAULT_SEARCH_LIMIT = 20;
const MAX_SEARCH_LIMIT = 100;
const SIMILARITY_THRESHOLD = 0.3;

// Select field constants
const GYM_BASE_FIELDS =
  'slug, name, city, district, cover_image_url, average_rating, review_count, created_at';
const GYM_DETAIL_FIELDS = '*';

// Sort configurations are now centralized in './utils'

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================
// Utility helpers are imported from './utils' to keep this file focused on application logic.

// ============================================================================
// CORE SEARCH FUNCTIONS
// ============================================================================

/**
 * Advanced gym search with PostgreSQL Full-Text Search and similarity matching
 */
async function performGymSearch(filters: GymSearchFilters): Promise<{
  gyms: GymCardData[];
  totalCount: number;
  searchMeta: GymSearchResult['searchMeta'];
}> {
  const startTime = Date.now();
  const supabase = getSupabaseAdmin();

  const {
    query,
    city,
    district,
    features,
    page = 1,
    limit = DEFAULT_SEARCH_LIMIT,
    sortBy = 'rating',
    minRating,
    minReviewCount,
    searchType = 'basic',
  } = filters;

  // Validate inputs
  if (page < 1) throw new Error("Sayfa numarası 1'den küçük olamaz.");
  if (limit < 1 || limit > MAX_SEARCH_LIMIT)
    throw new Error(`Limit 1-${MAX_SEARCH_LIMIT} arasında olmalıdır.`);

  const offset = (page - 1) * limit;
  let searchTypeUsed: 'basic' | 'fulltext' | 'similarity' = 'basic';
  const hasValidQuery = Boolean(query && validateSearchQuery(query));

  // Determine search strategy
  if (hasValidQuery) {
    searchTypeUsed = searchType === 'similarity' ? 'similarity' : 'fulltext';
  }

  let queryBuilder = supabase
    .from('gyms')
    .select(
      `
      ${GYM_BASE_FIELDS},
      company:companies(logo_url, name)
    `,
      { count: 'exact' }
    )
    .eq('status', 'active');

  // Apply text search
  if (hasValidQuery) {
    queryBuilder = applyTextSearch(queryBuilder, query!, searchTypeUsed);
  }

  // Apply filters
  queryBuilder = applySearchFilters(queryBuilder, {
    city,
    district,
    features,
    minRating,
    minReviewCount,
  });

  // Apply sorting
  if (sortBy !== 'relevance' || !query) {
    const sortConfig = getSortConfig(sortBy);
    sortConfig.forEach(sort => {
      queryBuilder = queryBuilder.order(sort.column, {
        ascending: sort.ascending ?? false,
        nullsFirst: sort.nullsFirst ?? false,
      });
    });
  }

  // Apply pagination
  queryBuilder = queryBuilder.range(offset, offset + limit - 1);

  const { data: gyms, error, count } = await queryBuilder;

  if (error) {
    throw new Error(`Salon arama sırasında hata oluştu: ${error.message}`);
  }

  const processingTime = Date.now() - startTime;
  const gymCards = (gyms || []).map(gym => transformToGymCard(gym));

  return {
    gyms: gymCards,
    totalCount: count || 0,
    searchMeta: {
      searchType: searchTypeUsed,
      processingTime,
      ...(hasValidQuery && { suggestions: generateSearchSuggestions(query!) }),
    },
  };
}

/**
 * Apply text search using PostgreSQL FTS or similarity
 */
function applyTextSearch(
  queryBuilder: any,
  query: string,
  searchType: 'basic' | 'fulltext' | 'similarity'
): any {
  const sanitized = sanitizeSearchQuery(query);

  if (searchType === 'fulltext') {
    // PostgreSQL Full-Text Search
    const tsquery = createSearchVector(sanitized);
    if (tsquery) {
      queryBuilder = queryBuilder.or(
        `to_tsvector('turkish', name || ' ' || coalesce(description, '')).@@.to_tsquery('turkish', '${tsquery}')`
      );
    }
  } else if (searchType === 'similarity') {
    // Trigram similarity search
    queryBuilder = queryBuilder.or(
      `name.ilike.%${sanitized}%,description.ilike.%${sanitized}%,similarity(name, '${sanitized}').gte.${SIMILARITY_THRESHOLD}`
    );
  } else {
    // Basic ILIKE search
    queryBuilder = queryBuilder.or(
      `name.ilike.%${sanitized}%,description.ilike.%${sanitized}%`
    );
  }

  return queryBuilder;
}

/**
 * Apply search filters
 */
function applySearchFilters(
  queryBuilder: any,
  filters: {
    city?: string;
    district?: string;
    features?: string[];
    minRating?: number;
    minReviewCount?: number;
  }
): any {
  const { city, district, features, minRating, minReviewCount } = filters;

  if (city && city !== 'all') {
    // gyms.city alanı text olup ID ve ek numara içerebilir; ID ile başlayan değerleri yakalamak için prefix match kullanıyoruz
    queryBuilder = queryBuilder.like('city', `${city}%`);
  }

  if (district && district !== 'all') {
    queryBuilder = queryBuilder.eq('district', district);
  }

  if (features && features.length > 0) {
    queryBuilder = queryBuilder.overlaps('features', features);
  }

  if (minRating && minRating > 0) {
    queryBuilder = queryBuilder.gte('average_rating', minRating);
  }

  if (minReviewCount && minReviewCount > 0) {
    queryBuilder = queryBuilder.gte('review_count', minReviewCount);
  }

  return queryBuilder;
}

/**
 * Generate search suggestions based on query
 */
function generateSearchSuggestions(query: string): string[] {
  // This could be enhanced with a proper suggestion algorithm
  // For now, return basic suggestions
  const suggestions: string[] = [];

  if (query.length < 3) {
    suggestions.push('En az 3 karakter giriniz');
  }

  return suggestions;
}

// ============================================================================
// PUBLIC API FUNCTIONS
// ============================================================================

/**
 * Unified gym search function with advanced PostgreSQL features
 * Replaces multiple separate functions with a single, powerful search API
 */
export async function searchGyms(
  filters: GymSearchFilters = {}
): Promise<ApiResponse<GymSearchResult>> {
  const result = await performGymSearch(filters);

  return {
    success: true,
    data: {
      gyms: result.gyms,
      totalCount: result.totalCount,
      hasMore:
        result.totalCount >
        (filters.page || 1) * (filters.limit || DEFAULT_SEARCH_LIMIT),
      currentPage: filters.page || 1,
      searchMeta: result.searchMeta,
    },
    error: undefined,
  };
}

// ============================================================================
// GYM DETAIL FUNCTIONS
// ============================================================================

export async function getGymBySlug(
  slug: string
): Promise<ApiResponse<Gyms & { company?: any }>> {
  if (!validateSlug(slug)) {
    return { success: false, error: "Geçerli bir salon slug'ı gereklidir." };
  }

  return await createActionPublic<Gyms & { company?: any }>(async () => {
    const adminClient = getSupabaseAdmin();
    const { data: gym, error } = await adminClient
      .from('gyms')
      .select(
        `
          ${GYM_DETAIL_FIELDS},
          company:companies(
            id,
            name,
            logo_url,
            phone,
            email,
            social_links
          )
        `
      )
      .eq('slug', slug)
      .eq('status', 'active')
      .single();

    if (error || !gym) {
      throw new Error('Salon bulunamadı veya aktif değil.');
    }

    return {
      ...gym,
      average_rating: roundRating(gym.average_rating),
      review_count: gym.review_count || 0,
    };
  });
}

// ============================================================================
// STATISTICS FUNCTIONS
// ============================================================================

/**
 * Get comprehensive gym statistics
 */
export async function getGymStats(): Promise<ApiResponse<GymStats>> {
  return await createActionPublic<GymStats>(async (_, supabase) => {
    const { data: gyms, error } = await supabase
      .from('gyms')
      .select('city, district, average_rating')
      .eq('status', 'active');

    if (error) {
      throw new Error(
        `Salon istatistikleri getirilirken hata oluştu: ${error.message}`
      );
    }

    if (!gyms) {
      return {
        totalGyms: 0,
        totalCities: 0,
        totalDistricts: 0,
        averageRating: 0,
      };
    }

    // Calculate statistics
    const totalGyms = gyms.length;
    // cities set'ini şehir isimlerine göre oluştur (ID metinlerini isimle dönüştür)
    const cities = new Set(
      gyms
        .map(g => getCityNameById(g.city as any) || (g.city as any))
        .filter(Boolean)
    );
    const districts = new Set(gyms.map(g => g.district).filter(Boolean));

    const ratingsWithValues = gyms.filter(g => g.average_rating !== null);
    const averageRating =
      ratingsWithValues.length > 0
        ? ratingsWithValues.reduce(
            (sum, g) => sum + (g.average_rating || 0),
            0
          ) / ratingsWithValues.length
        : 0;

    return {
      totalGyms,
      totalCities: cities.size,
      totalDistricts: districts.size,
      averageRating: roundRating(averageRating) || 0,
    };
  });
}

/**
 * Kullanıcının belirli bir gym'deki kapsamlı üyelik durumunu kontrol eder
 * Gym owner kontrolü, aktif üyelik, bekleyen davet ve gönderilen istek kontrollerini içerir
 */
export async function getComprehensiveGymMembershipStatus(
  gymId: string
): Promise<ApiResponse<ComprehensiveGymMembershipStatus>> {
  return await createAction<ComprehensiveGymMembershipStatus>(
    async (_, supabase, userId) => {
      // Kullanıcı oturumu yoksa gereksiz sorguları çalıştırmayalım
      if (!userId) {
        return {
          hasActiveMembership: false,
          isGymOwner: false,
          pendingInvitation: null,
          membershipStatus: 'none' as const,
        };
      }

      // user_roles üzerinden hızlı erişim
      const { data: roles } = await supabase
        .from('user_roles')
        .select('member_gymids')
        .eq('profile_id', userId)
        .maybeSingle();

      // Gym owner kontrolü: kullanıcının company'si gym'in owner'ı mı?
      let isGymOwner = false;
      const { data: gymData } = await supabase
        .from('gyms')
        .select('id, owner_profile_id')
        .eq('id', gymId)
        .single();

      // Company kontrolü
      const { data: company } = await supabase
        .from('companies')
        .select('id, status')
        .eq('manager_profile_id', userId)
        .eq('status', 'active')
        .maybeSingle();

      if (company?.id && gymData?.owner_profile_id) {
        isGymOwner = company.id === gymData.owner_profile_id;
      }

      if (isGymOwner) {
        return {
          hasActiveMembership: false,
          isGymOwner: true,
          pendingInvitation: null,
          membershipStatus: 'none' as const,
        };
      }

      // Paralel olarak: pending davet kontrolü, diğer yandan hızlı üyelik kontrolü user_roles üzerinden
      const [{ data: pendingInvitation }] = await Promise.all([
        supabase
          .from('gym_invitations')
          .select('id, status')
          .eq('profile_id', userId)
          .eq('gym_id', gymId)
          .eq('status', 'pending')
          .eq('role', 'member')
          .maybeSingle(),
        // ileride başka paralel işler eklenebilir
      ]);

      const hasActiveMembership = Array.isArray(roles?.member_gymids)
        ? roles!.member_gymids!.includes(gymId)
        : false;

      // Membership status belirleme
      let membershipStatus: 'none' | 'pending' | 'active' | 'suspended' =
        'none';

      if (hasActiveMembership) {
        membershipStatus = 'active';
      } else if (pendingInvitation) {
        membershipStatus = 'pending';
      }

      return {
        hasActiveMembership,
        isGymOwner,
        pendingInvitation,
        membershipStatus,
      };
    }
  );
}

/**
 * Kullanıcının bir salonda antrenör durumunu kontrol eder
 */
export async function getTrainerStatus(gymId: string): Promise<
  ApiResponse<{
    isTrainer: boolean;
    hasPendingRequest: boolean;
    trainerStatus: 'none' | 'pending' | 'active';
  }>
> {
  return await createAction(async (_, supabase, userId) => {
    if (!userId) {
      return {
        isTrainer: false,
        hasPendingRequest: false,
        trainerStatus: 'none' as const,
      };
    }

    // Paralel olarak kontrolleri yap
    // user_roles üzerinden hızlı kontrol + pending request kontrolü
    const [{ data: roles }, { data: pendingRequest }] = await Promise.all([
      supabase
        .from('user_roles')
        .select('trainer_gymids')
        .eq('profile_id', userId)
        .maybeSingle(),
      supabase
        .from('gym_invitations')
        .select('id, status')
        .eq('profile_id', userId)
        .eq('gym_id', gymId)
        .eq('status', 'pending')
        .eq('type', 'join_request')
        .eq('role', 'trainer')
        .maybeSingle(),
    ]);

    const isTrainer = Array.isArray(roles?.trainer_gymids)
      ? roles!.trainer_gymids!.includes(gymId)
      : false;
    const hasPendingRequest = !!pendingRequest;

    // Trainer status belirleme
    let trainerStatus: 'none' | 'pending' | 'active' = 'none';

    if (isTrainer) {
      trainerStatus = 'active';
    } else if (hasPendingRequest) {
      trainerStatus = 'pending';
    }

    return {
      isTrainer,
      hasPendingRequest,
      trainerStatus,
    };
  });
}
