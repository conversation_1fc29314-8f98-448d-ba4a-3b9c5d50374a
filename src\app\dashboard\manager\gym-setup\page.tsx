import { LimitReachedScreen } from './components/LimitReachedScreen';
import { SuccessScreen } from './components/SuccessScreen';
import { GymSetupForm } from './components/GymSetupForm';
import { SubscriptionRequiredScreen } from './components/SubscriptionRequiredScreen';
import { getManagerGymLimit } from '@/lib/actions/business/subscription-actions';
import { getManagerGymNames } from '@/lib/actions/dashboard/company/dashboard-actions';
import { getManagerStatus } from '@/lib/actions/user/manager-actions';

// Force dynamic rendering due to auth checks

type SubscriptionTier = 'free' | 'professional' | 'enterprise';

interface GymSetupPageProps {
  searchParams: Promise<{ success?: string }>;
}

export default async function GymSetupPage({
  searchParams,
}: GymSetupPageProps) {
  const params = await searchParams;

  // Active subscription kontrolü
  const managerStatus = await getManagerStatus();
  if (
    !managerStatus.success ||
    !managerStatus.data?.isManager ||
    managerStatus.data?.status !== 'active'
  ) {
    return <SubscriptionRequiredScreen />;
  }

  // Middleware tarafından auth kontrolü yapıldı, manager subscription kontrolü yap
  const { data: subscriptionData } = await getManagerGymLimit();

  const subscriptionTier: SubscriptionTier =
    (subscriptionData?.tier as SubscriptionTier) || 'free';
  const maxGyms = subscriptionData?.maxGyms ?? null; // null means unlimited

  // Get current gym count
  const gymsResult = await getManagerGymNames();
  const currentGymCount =
    gymsResult.success && gymsResult.data ? gymsResult.data.length : 0;

  // Check if user can create more gyms
  const canCreateGym = maxGyms === null || currentGymCount < maxGyms;

  // Show success screen if redirected from successful creation
  if (params.success === 'true') {
    return <SuccessScreen />;
  }

  // Show limit reached screen if user cannot create more gyms
  if (!canCreateGym) {
    return (
      <LimitReachedScreen
        subscriptionTier={subscriptionTier}
        maxGyms={maxGyms}
        currentGymCount={currentGymCount}
      />
    );
  }

  // Show main gym setup form
  return <GymSetupForm />;
}
