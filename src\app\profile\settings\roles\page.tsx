import { RoleManagement } from '@/components/profile-settings/RoleManagement';
import { getUserRoles } from '@/lib/auth/server-auth';

export default async function RolesSettingsPage() {
  // Server action ile user roles verilerini çek
  const userRoles = await getUserRoles();

  return (
    <div className="flex-1 space-y-6 p-6 lg:p-8">
      {/* <PERSON><PERSON> başlığı */}
      <div className="space-y-2">
        <h1 className="text-2xl font-semibold tracking-tight">Rol Yönetimi</h1>
        <p className="text-muted-foreground">
          Hesap rollerinizi görüntüleyin ve yeni roller edinin
        </p>
      </div>

      {/* Rol yönetimi içeriği */}
      <div className="space-y-6">
        <RoleManagement userRoles={userRoles} />
      </div>
    </div>
  );
}
