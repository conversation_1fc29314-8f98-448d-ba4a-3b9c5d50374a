'use client';

import { useFormStatus } from 'react-dom';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface SubmitButtonProps {
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

export function SubmitButton({
  children,
  className = '',
  disabled = false,
}: SubmitButtonProps) {
  const { pending } = useFormStatus();
  const isDisabled = pending || disabled;

  return (
    <Button
      type="submit"
      disabled={isDisabled}
      className={`bg-primary hover:bg-primary/90 text-primary-foreground hover:shadow-primary/25 h-12 w-full text-base font-semibold shadow-lg transition-all duration-300 ease-out hover:scale-[1.02] hover:shadow-xl active:scale-[0.98] disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 ${pending ? 'animate-pulse' : ''} ${className} `}
      aria-describedby={pending ? 'submit-status' : undefined}
    >
      {pending && (
        <Loader2
          className="mr-3 h-5 w-5 animate-spin text-current"
          aria-hidden="true"
        />
      )}
      {pending ? 'İşleniyor...' : children}
      {pending && (
        <span id="submit-status" className="sr-only">
          Form gönderiliyor, lütfen bekleyin
        </span>
      )}
    </Button>
  );
}
