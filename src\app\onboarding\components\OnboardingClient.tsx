'use client';

import { useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useOnboarding } from '@/hooks/use-onboarding';
import { PlatformRoles } from '@/types/database/enums';
import { Profiles } from '@/types/database/tables';

import { WelcomeStep } from './WelcomeStep';
import { RoleSelectionStep } from './steps/RoleSelectionStep';
import { FormsStep } from './steps/FormsStep';
import { ReviewStep } from './ReviewStep';
import { ErrorAlert } from './shared/ErrorAlert';

interface OnboardingClientProps {
  currentRoles: PlatformRoles[];
  userProfile: Profiles | null;
  shouldRedirectToDashboard?: boolean;
}

export function OnboardingClient({
  currentRoles,
  userProfile,
  shouldRedirectToDashboard,
}: OnboardingClientProps) {
  const router = useRouter();

  useEffect(() => {
    if (shouldRedirectToDashboard) {
      router.push('/dashboard');
    }
  }, [shouldRedirectToDashboard, router]);

  const hasFullName = !!userProfile?.full_name?.trim();

  const onboarding = useOnboarding({ currentRoles, hasFullName });
  const currentRolesSet = useMemo(() => new Set(currentRoles), [currentRoles]);

  const renderStep = () => {
    switch (onboarding.step) {
      case 'welcome':
        return (
          <WelcomeStep onContinue={() => onboarding.navigateToStep('roles')} />
        );
      case 'roles':
        return (
          <RoleSelectionStep
            selectedRoles={onboarding.selectedRoles}
            isRoleSelectable={onboarding.isRoleSelectable}
            getRoleWarningMessage={onboarding.getRoleWarningMessage}
            validateRoleCombination={onboarding.validateRoleCombination}
            handleRoleToggle={onboarding.handleRoleToggle}
            onContinue={onboarding.handleContinueToForms}
            currentRolesSet={currentRolesSet}
          />
        );
      case 'forms':
        return (
          <FormsStep
            selectedRoles={onboarding.selectedRoles}
            hasFullName={hasFullName}
            personalData={onboarding.personalData}
            memberData={onboarding.memberData}
            trainerData={onboarding.trainerData}
            managerData={onboarding.managerData}
            fieldErrors={onboarding.fieldErrors}
            isSubmitting={onboarding.isSubmitting}
            isFormValid={onboarding.isFormValid}
            onPersonalDataChange={onboarding.setPersonalData}
            onMemberDataChange={onboarding.setMemberData}
            onTrainerDataChange={onboarding.setTrainerData}
            onManagerDataChange={onboarding.setManagerData}
            onBack={() => onboarding.navigateToStep('roles')}
            onContinue={() => onboarding.navigateToStep('review')}
          />
        );
      case 'review':
        return (
          <ReviewStep
            hasFullName={hasFullName}
            personalData={onboarding.personalData}
            memberData={onboarding.memberData}
            trainerData={onboarding.trainerData}
            managerData={onboarding.managerData}
            selectedRoles={onboarding.selectedRoles}
            isSubmitting={onboarding.isSubmitting}
            onBack={() => onboarding.navigateToStep('forms')}
            onSubmit={onboarding.handleSubmit}
          />
        );
      default:
        return (
          <WelcomeStep onContinue={() => onboarding.navigateToStep('roles')} />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 px-4 py-10 sm:px-6 lg:px-8 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
      <div className="mx-auto max-w-4xl">
        {onboarding.error && <ErrorAlert message={onboarding.error} />}
        {renderStep()}
      </div>
    </div>
  );
}
