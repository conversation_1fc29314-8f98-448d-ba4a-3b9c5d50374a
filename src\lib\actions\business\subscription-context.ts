'use server';

import { createAction } from '../core';
import { ApiResponse } from '@/types/global/api';
import { PlatformPackages } from '@/types/database/tables';

/**
 * Manager i<PERSON>in abonelik bağlamı: şirket & paket durumu
 */
export async function getSubscriptionContext(): Promise<
  ApiResponse<{
    companyId: string | null;
    subscriptionStatus: string | null;
    platformPackageId: string | null;
    companyActive: boolean;
    packageData: Pick<
      PlatformPackages,
      'id' | 'name' | 'tier' | 'duration' | 'price' | 'features'
    > | null;
  }>
> {
  return createAction(async (_, supabase, userId) => {
    if (!userId) {
      throw new Error('G<PERSON>ş gerekli.');
    }
    // Şirket kaydı
    const { data: company, error: companyErr } = await supabase
      .from('companies')
      .select(
        `id, status, platform_package_id, platform_packages!inner(id, name, tier, duration, price, features)`
      )
      .eq('manager_profile_id', userId)
      .maybeSingle();

    if (companyErr) {
      throw new Error('Şirket bilgileri alınamadı: ' + companyErr.message);
    }

    if (!company) {
      return {
        companyId: null,
        subscriptionStatus: null,
        platformPackageId: null,
        companyActive: false,
        packageData: null,
      };
    }

    const pkg: any = company.platform_packages || null;

    return {
      companyId: company.id,
      subscriptionStatus: company.status,
      platformPackageId: company.platform_package_id,
      companyActive: company.status === 'active',
      packageData: pkg
        ? {
            id: pkg.id,
            name: pkg.name,
            tier: pkg.tier,
            duration: pkg.duration,
            price: pkg.price,
            features: pkg.features,
          }
        : null,
    };
  });
}
