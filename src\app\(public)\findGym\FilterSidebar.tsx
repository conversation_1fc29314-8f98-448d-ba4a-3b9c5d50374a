'use client';

import { MapPin, Search, Star, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTransition, useState, useEffect, useCallback } from 'react';

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { FEATURE_GROUPS } from '@/lib/constants';
import { LocationSearch } from './LocationSearch';

// Backward compatibility export
export const FilterSidebar = FilterHeader;

export function FilterHeader() {
  const searchParams = useSearchParams();
  const currentQuery = searchParams.get('q') || '';
  const currentCity = searchParams.get('city') || 'all';
  const currentDistrict = searchParams.get('district') || 'all';
  const currentFeatures =
    searchParams.get('features')?.split(',').filter(Boolean) || [];
  const hasActiveFilters = !!(
    currentQuery ||
    (currentCity && currentCity !== 'all') ||
    (currentDistrict && currentDistrict !== 'all') ||
    currentFeatures.length > 0
  );
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  // Avoid recreating handlers on every render
  const buildQueryString = useCallback(
    (query?: string, city?: string, district?: string, features?: string[]) => {
      const params = new URLSearchParams();
      if (query) params.set('q', query);
      if (city && city !== 'all') params.set('city', city);
      if (district && district !== 'all') params.set('district', district);
      if (features && features.length > 0)
        params.set('features', features.join(','));
      return params.toString();
    },
    []
  );

  // Local state for form values
  const [city, setCity] = useState(currentCity);
  const [district, setDistrict] = useState(currentDistrict);

  // Features state - form içinde seçilen özellikler
  const [selectedFeatures, setSelectedFeatures] =
    useState<string[]>(currentFeatures);
  const toggleFeature = useCallback((feature: string, checked: boolean) => {
    setSelectedFeatures(prev =>
      checked
        ? prev.includes(feature)
          ? prev
          : [...prev, feature]
        : prev.filter(f => f !== feature)
    );
  }, []);

  // URL parametreleri değiştiğinde selectedFeatures'ı senkronize et
  useEffect(() => {
    const urlFeatures =
      searchParams.get('features')?.split(',').filter(Boolean) || [];
    // Only update if changed to avoid extra renders
    setSelectedFeatures(prev => {
      const a = prev.join(',');
      const b = urlFeatures.join(',');
      return a === b ? prev : urlFeatures;
    });
  }, [searchParams]);

  // Handler for LocationSearch
  const handleLocationChange = (newCity: string, newDistrict: string) => {
    setCity(newCity);
    setDistrict(newDistrict);
  };

  const handleSearch = (formData: FormData) => {
    startTransition(() => {
      const query = (formData.get('query') as string)?.trim();
      const queryString = buildQueryString(
        query,
        city,
        district,
        selectedFeatures
      );
      router.push(queryString ? `/findGym?${queryString}` : '/findGym');
      setIsFiltersOpen(false);
    });
  };

  const handleReset = () => {
    startTransition(() => {
      setSelectedFeatures([]);
      router.push('/findGym');
      setIsFiltersOpen(false);
    });
  };

  // Get location display text
  const getLocationText = () => {
    if (currentCity === 'all') return null;
    const cityName = currentCity;
    if (currentDistrict === 'all') return cityName;
    return `${currentDistrict}, ${cityName}`;
  };

  const locationText = getLocationText();

  return (
    <div className="bg-background/95 sticky top-14 z-40 shadow-sm backdrop-blur-sm">
      <div className="container mx-auto px-4 py-4">
        <form
          data-search-form
          onSubmit={e => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            handleSearch(formData);
          }}
          className="space-y-4"
        >
          {/* Search Inputs Row */}
          <div className="flex items-center gap-4">
            {/* Salon Name Search */}
            <div className="max-w-md flex-1">
              <div className="relative">
                <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
                <Input
                  name="query"
                  type="text"
                  placeholder="Salon adı, açıklama..."
                  defaultValue={currentQuery}
                  className="pr-4 pl-10"
                />
              </div>
            </div>

            {/* Location Search */}
            <div className="max-w-md flex-1">
              <LocationSearch
                selectedCity={city}
                selectedDistrict={district}
                onLocationChange={handleLocationChange}
              />
            </div>

            {/* Features Button */}
            <Popover open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline" className="relative gap-2">
                  <Star className="h-4 w-4" />
                  Özellikler
                  {selectedFeatures.length > 0 && (
                    <Badge
                      variant="destructive"
                      className="absolute -top-2 -right-2 flex h-5 w-5 items-center justify-center p-0 text-xs"
                    >
                      {selectedFeatures.length}
                    </Badge>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-0" align="end">
                <div className="space-y-4 p-4">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                    <h3 className="text-primary text-lg font-bold">
                      Özellikler
                    </h3>
                    <div className="flex items-center gap-2">
                      {selectedFeatures.length > 0 && (
                        <Button
                          onClick={() => {
                            setSelectedFeatures([]);
                          }}
                          variant="outline"
                          size="sm"
                          disabled={isPending}
                        >
                          Temizle
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsFiltersOpen(false)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Features List */}
                  <div className="bg-muted/20 max-h-60 overflow-y-auto rounded-lg p-2">
                    <div className="space-y-1">
                      {Object.values(FEATURE_GROUPS)
                        .flatMap(group => group.features)
                        .map((feature: string) => (
                          <div
                            key={feature}
                            className="hover:bg-background/50 flex items-center space-x-2 rounded-md p-1.5 transition-colors duration-300"
                          >
                            <Checkbox
                              id={feature}
                              checked={selectedFeatures.includes(feature)}
                              onCheckedChange={checked => {
                                // shadcn Checkbox onCheckedChange param can be boolean | "indeterminate"
                                const isChecked = checked === true;
                                toggleFeature(feature, isChecked);
                              }}
                              className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                            />
                            <label
                              htmlFor={feature}
                              className="flex-1 cursor-pointer text-sm leading-none font-medium"
                            >
                              {feature}
                            </label>
                          </div>
                        ))}
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            {/* Search Button */}
            <Button
              type="submit"
              disabled={isPending}
              className="px-6"
              aria-busy={isPending}
            >
              {isPending ? 'Aranıyor...' : 'Ara'}
            </Button>

            {/* Reset Button */}
            {hasActiveFilters && (
              <Button
                type="button"
                onClick={handleReset}
                variant="outline"
                disabled={isPending}
              >
                Temizle
              </Button>
            )}
          </div>

          {/* Active Filters Display */}
          {hasActiveFilters && (
            <div className="flex flex-wrap items-center gap-2">
              {currentQuery && (
                <Badge variant="secondary" className="gap-1">
                  <Search className="h-3 w-3" />
                  {currentQuery}
                </Badge>
              )}
              {locationText && (
                <Badge variant="secondary" className="gap-1">
                  <MapPin className="h-3 w-3" />
                  {locationText}
                </Badge>
              )}
              {currentFeatures.map(feature => (
                <Badge key={feature} variant="secondary" className="gap-1">
                  <Star className="h-3 w-3" />
                  {feature}
                </Badge>
              ))}
            </div>
          )}
        </form>
      </div>
    </div>
  );
}
