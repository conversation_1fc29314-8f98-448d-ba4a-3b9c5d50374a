import { fetchAllPlatformPackages } from "@/lib/actions/business/platform-packages"
import { Check, Crown } from "lucide-react"
import type { PlatformPackages } from "@/types/database/tables"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"

interface PricingModernProps {
  showHeader?: boolean
  compact?: boolean
  packages?: PlatformPackages[]
}

export async function PricingModern({
  showHeader = true,
  compact = false,
  packages: incomingPackages,
}: PricingModernProps) {
  let packages: PlatformPackages[] = Array.isArray(incomingPackages) ? incomingPackages : []
  let errorMsg: string | null = null
  if (!incomingPackages) {
    const packagesResult = await fetchAllPlatformPackages()
    if (packagesResult.success && packagesResult.data) {
      packages = packagesResult.data
    } else {
      errorMsg = packagesResult.error || "Paketler yüklenirken bir hata o<PERSON>."
    }
  }

  const groupedPackages = packages.reduce(
    (acc, pkg) => {
      if (!acc[pkg.tier]) acc[pkg.tier] = []
      acc[pkg.tier].push(pkg)
      return acc
    },
    {} as Record<string, PlatformPackages[]>,
  )

  // Helper functions
  const formatFeatures = (features: unknown): string[] => {
    // Array<string>
    if (Array.isArray(features)) {
      return features.filter((item): item is string => typeof item === "string")
    }
    // JSON string fallback
    if (typeof features === "string") {
      try {
        const parsed = JSON.parse(features)
        return formatFeatures(parsed)
      } catch {
        return []
      }
    }
    // Object form: { perks: string[]; ... }
    if (typeof features === "object" && features !== null) {
      const obj = features as Record<string, unknown>
      if (Array.isArray(obj.perks)) {
        return (obj.perks as unknown[]).filter((v): v is string => typeof v === "string")
      }
      // Direct string values in object
      const directStrings = Object.values(obj).filter((v): v is string => typeof v === "string")
      if (directStrings.length) return directStrings
      // Nested arrays of strings
      const nested = Object.values(obj)
        .flatMap((v) => (Array.isArray(v) ? v : []))
        .filter((v): v is string => typeof v === "string")
      return nested
    }
    return []
  }

  // Feature text prettifier to improve readability
  const prettifyFeature = (text: string): string => {
    try {
      let t = (text || "").toString().trim()
      if (!t) return t
      // Common replacements
      t = t.replace(/^Maks\s/i, "Maksimum ")
      t = t.replace(/\bmax\b/i, "Maksimum")
      t = t.replace(/uye/gi, "üye")
      // Ensure first letter uppercase
      t = t.charAt(0).toUpperCase() + t.slice(1)
      return t
    } catch {
      return text as string
    }
  }

  // New helpers for redesigned UI
  const POPULAR_TIER = "professional" as const
  // Order ascending by typical price so the middle option (starter) sits in the center
  const TIER_ORDER: Record<string, number> = { free: 0, starter: 1, professional: 2 }
  const tiersSorted = Object.keys(groupedPackages).sort((a, b) => (TIER_ORDER[a] ?? 99) - (TIER_ORDER[b] ?? 99))

  const tl = (n: number) => n.toLocaleString("tr-TR")

  // Calculate monthly price from yearly
  const getMonthlyPrice = (yearlyPrice: number) => Math.round(yearlyPrice / 10)
  const DISCOUNT_RATE = 0.65 // referans görseldeki indirim rozeti

  // Build human-friendly features from table columns when not provided
  const buildAutoFeatures = (pkg: PlatformPackages): string[] => {
    const items: string[] = []
    const num = (v?: number | null) => (typeof v === "number" && Number.isFinite(v) ? v : null)
    const label = (count: number | null, finiteText: string, unlimitedText: string) =>
      count === null ? unlimitedText : `${tl(count)} ${finiteText}`

    // Gyms
    items.push(label(num(pkg.max_gyms), "spor salonu", "Sınırsız spor salonu"))
    // Members
    items.push(label(num(pkg.max_members), "üye kapasitesi", "Sınırsız üye"))
    // Trainers
    items.push(label(num(pkg.max_trainers), "antrenör", "Sınırsız antrenör"))
    // Staff
    items.push(label(num(pkg.max_staff), "personel", "Sınırsız personel"))
    // Appointments
    items.push(label(num(pkg.max_monthly_appointments), "aylık randevu", "Sınırsız randevu"))

    // Add a couple of generic perks commonly present
    items.push("Gelişmiş raporlama")
    items.push("Öncelikli destek")
    return items
  }

  if (packages.length === 0) {
    return (
      <section
        id={compact ? "pricing" : "pricing-plans"}
        className={`relative isolate w-full ${compact ? "py-10" : "py-16 md:py-24 lg:py-32"}`}
        aria-label="Fiyatlandırma paketi yükleme hatası"
      >
        <div className="container px-4 md:px-6">
          <div className="mx-auto ">
            <div
              role="alert"
              aria-live="assertive"
              className="rounded-2xl border border-white/15 bg-white/5 p-6 text-center shadow-xl backdrop-blur supports-[backdrop-filter]:bg-white/10"
            >
              <div className="text-destructive text-lg font-semibold">
                ⚠️ {errorMsg || "Paketler yüklenirken bir hata oluştu."}
              </div>
              <p className="mt-2 text-sm text-muted-foreground">
                Lütfen daha sonra tekrar deneyin veya destek ekibimizle iletişime geçin.
              </p>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section
      id={compact ? "pricing" : "pricing-plans"}
      className={`relative isolate w-full bg-background ${compact ? "py-16" : "py-20 md:py-28 lg:py-32"}`}
    >
      {/* Background elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-[radial-gradient(40%_60%_at_50%_0%,hsl(var(--primary)/0.08),transparent_70%)]" />
        <div className="absolute inset-0 bg-grid-pattern opacity-10" />
      </div>

      <div className="container px-4 mx-auto md:px-6">
        {showHeader && (
          <div className="mx-auto mb-16 max-w-4xl text-center md:mb-20">
            <div className="mb-6 inline-flex items-center gap-2 rounded-full border border-primary/30 bg-primary/10 px-4 py-2 text-sm font-medium text-primary">
              <Crown className="h-4 w-4" />
              <span>Fiyatlandırma</span>
            </div>
            <h2 className="text-balance text-4xl font-bold md:text-5xl text-foreground mb-6">
              İşletmenize Uygun Esnek Planlar
            </h2>
            <p className="mx-auto mt-6 max-w-3xl text-xl text-muted-foreground leading-relaxed">
              Büyüme hedeflerinize göre ölçeklenen şeffaf fiyatlandırma. Beta döneminde denemesi ücretsiz.
            </p>
          </div>
        )}

        <div id="pricing-list" className="grid gap-8 lg:grid-cols-3 max-w-6xl mx-auto">
          {tiersSorted.map((tier) => {
            const tierPackages = groupedPackages[tier] ?? []
            // Support lifetime packages (duration is null)
            const isLifetime = (d: string | null | undefined) =>
              d == null || d === "lifetime" || d === "free" || d === ""
            const yearlyPkg = tierPackages.find((p) => p.duration === "yearly")
            const lifetimePkg = tierPackages.find((p) => isLifetime(p.duration as any))
            if (!yearlyPkg && !lifetimePkg && tierPackages.length === 0) return null

            const currentPkg = lifetimePkg ?? yearlyPkg ?? tierPackages[0]
            const explicitFeatures = formatFeatures(currentPkg.features)
            const auto = buildAutoFeatures(currentPkg)
            // Merge explicit with auto, prefer explicit order and remove duplicates
            const mergedSet = new Set<string>([...explicitFeatures, ...auto])
            const features = Array.from(mergedSet).slice(0, 20)
            const displayName = currentPkg.name
            const isPopular = tier === POPULAR_TIER
            const monthlyPrice = getMonthlyPrice(currentPkg.price)
            const oldMonthlyPrice = Math.round(monthlyPrice / (1 - DISCOUNT_RATE))

            return (
              <article
                key={tier}
                className={`relative rounded-2xl border bg-card p-8 shadow-sm transition-all duration-200 h-full flex flex-col ${
                  isPopular ? "border-primary/60 shadow-[0_0_0_1px_hsl(var(--primary)/0.3)]" : "border-border"
                } ${isPopular ? "lg:translate-y-0" : "lg:translate-y-1"}`}
              >
                {/* Popular badge at top center */}
                {isPopular && (
                  <div className="absolute -top-3 left-1/2 -translate-x-1/2 z-10">
                    <Badge className="bg-primary text-primary-foreground px-3 py-1 text-[11px] font-semibold rounded-full shadow">
                      En Popüler
                    </Badge>
                  </div>
                )}

                {/* Corner discount badge */}
                <div className="absolute right-3 top-3">
                  <Badge className="rounded-full bg-primary/15 text-primary border border-primary/30 px-3 py-0.5 text-[11px] font-semibold">
                    %65 İndirim
                  </Badge>
                </div>

                {/* Package name - centered like reference */}
                <div className="text-center mb-6 pt-4">
                  <h3 className="text-2xl font-bold text-card-foreground mb-2">{displayName}</h3>
                  <p className="text-sm text-muted-foreground leading-relaxed px-2">
                    {tier === "starter" && "Küçük işletmeler için ideal başlangıç paketi"}
                    {tier === "professional" && "Büyüyen işletmeler için sınırsız özellikler"}
                    {tier === "enterprise" &&
                      "Birden fazla salona sahip  şirketler için"}
                  </p>
                </div>

                {/* Pricing section - centered like reference */}
                <div className="text-center mb-8">
                  <div className="mb-1 text-sm text-muted-foreground line-through">₺{tl(oldMonthlyPrice)}/ay</div>
                  <div className="flex items-end justify-center gap-2 mb-2">
                    <span className="text-5xl font-extrabold text-primary tracking-tight">₺{tl(monthlyPrice)}</span>
                    <span className="pb-1 text-base text-muted-foreground">/ay</span>
                  </div>
                  <div className="text-xs md:text-sm text-muted-foreground">
                    Yıllık ₺{tl(currentPkg.price)} olarak faturalandırılır
                  </div>
                </div>

                {/* Features list - left aligned */}
                <div className="mb-8">
                  <ul className="space-y-3">
                    {features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <div className="mt-0.5 flex-shrink-0">
                          <Check className="h-4 w-4 text-emerald-500" />
                        </div>
                        <span className="text-sm text-card-foreground leading-relaxed">{prettifyFeature(feature)}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* CTA Button */}
                <div className="mt-auto">
                  <Button
                    asChild
                    size="lg"
                    className={`w-full h-12 font-semibold transition-all duration-200 ${
                      isPopular
                        ? "bg-primary hover:bg-primary/90 text-primary-foreground"
                        : "bg-transparent text-primary border border-primary/60 hover:bg-primary/10"
                    }`}
                  >
                    <Link href={`/onboarding`} aria-label={`${displayName} paketini ilk ay ücretsiz dene`}>
                      İlk ay ücretsiz dene
                    </Link>
                  </Button>
                </div>
              </article>
            )
          })}
        </div>
      </div>
    </section>
  )
}
