import { Metadata } from 'next';
import { UseGymManagerCodeClient } from './components/UseGymManagerCodeClient';
import { getUserRoles } from '@/lib/auth/server-auth';

export const metadata: Metadata = {
  title: 'Salon Yöneticisi Davet Kodu | Sportiva',
  description: 'Salon yöneticisi davet kodunuzu kullanarak sisteme katılın.',
};

export default async function UseGymManagerCodePage() {
  const roles = await getUserRoles();
  return <UseGymManagerCodeClient currentRoles={roles} />;
}
