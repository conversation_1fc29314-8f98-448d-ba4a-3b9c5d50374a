/**
 * Silinmiş salon/şirket bilgilerini güvenli şekilde göstermek için yardımcı fonksiyonlar
 */

export function getGymDisplayName(gymName: string | null): string {
  return gymName || 'Silinmiş Salon';
}

export function getCompanyDisplayName(companyName: string | null): string {
  return companyName || 'Silinmiş Şirket';
}

export function getTrainerDisplayName(trainerName: string | null): string {
  return trainerName || 'Silinmiş Antrenör';
}

// Gyms.city alanı ID veya ID+ek taşıyabildiği için güvenli şehir adı çözümleyici
// Örn: "34-2" veya "6" -> isim; aksi halde gelen değeri döndürür
import { getCityNameById } from '@/lib/constants';

export function resolveCityName(
  city: string | number | null | undefined
): string | null {
  if (city == null) return null;
  const raw = String(city);
  // Baştaki sayısal ID kısmını ayıkla (örn: "34-2" => 34)
  const match = raw.match(/^\s*(\d+)/);
  const id = match ? match[1] : raw;
  return getCityNameById(id) || raw;
}
