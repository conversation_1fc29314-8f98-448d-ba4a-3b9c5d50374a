/**
 * Constants for gym page operations
 * Following Clean Code principles - centralized constants to avoid magic numbers and strings
 */

// ============================================================================
// VALIDATION CONSTANTS
// ============================================================================

export const RATING_VALIDATION = {
  MIN_RATING: 1,
  MAX_RATING: 5,
} as const;

export const REVIEW_VALIDATION = {
  MAX_COMMENT_LENGTH: 1000,
  MIN_COMMENT_LENGTH: 10,
} as const;

// ============================================================================
// ERROR MESSAGES
// ============================================================================

export const GYM_ERROR_MESSAGES = {
  // General errors
  GYM_ID_REQUIRED: "Salon ID'si gereklidir",
  GYM_NOT_FOUND: 'Salon bulunamadı',

  // Package errors
  PACKAGES_FETCH_FAILED: 'Paketler alınırken hata oluştu',
  PACKAGE_NOT_FOUND: 'Paket bulunamadı',

  // Review errors
  INVALID_REVIEW_DATA: 'Geçersiz değerlendirme verisi',
  REVIEW_SAVE_FAILED: 'Değerlendirme kaydedilirken bir hata oluştu',
  REVIEW_UPDATE_FAILED: 'Değerlendirme güncellenirken bir hata oluştu',
  REVIEW_DELETE_FAILED: 'Değerlendirme silinirken bir hata oluştu',
  REVIEW_FETCH_FAILED: 'Değerlendirmeler alınırken hata oluştu',
  REVIEW_STATS_FETCH_FAILED:
    'Değerlendirme istatistikleri alınırken hata oluştu',
  REVIEW_ALREADY_EXISTS: 'Bu salon için zaten değerlendirme yapmışsınız',

  // Rating validation
  INVALID_RATING: 'Geçersiz puan. 1-5 arasında bir değer giriniz',
  RATING_REQUIRED: 'Puan vermeniz gereklidir',

  // Comment validation
  COMMENT_TOO_SHORT: `Yorum en az ${REVIEW_VALIDATION.MIN_COMMENT_LENGTH} karakter olmalıdır`,
  COMMENT_TOO_LONG: `Yorum en fazla ${REVIEW_VALIDATION.MAX_COMMENT_LENGTH} karakter olmalıdır`,

  // Membership errors
  MEMBERSHIP_REQUIRED: 'Bu işlem için salon üyeliği gereklidir',
  MEMBERSHIP_NOT_ACTIVE: 'Aktif salon üyeliğiniz bulunmamaktadır',
} as const;

// ============================================================================
// SUCCESS MESSAGES
// ============================================================================

export const GYM_SUCCESS_MESSAGES = {
  REVIEW_SUBMITTED: 'Değerlendirmeniz başarıyla kaydedildi',
  REVIEW_UPDATED: 'Değerlendirmeniz başarıyla güncellendi',
  REVIEW_DELETED: 'Değerlendirmeniz başarıyla silindi',
} as const;

// ============================================================================
// DATABASE DEFAULTS
// ============================================================================

export const GYM_DEFAULTS = {
  NULL_VALUE: null,
  EMPTY_STRING: '',
  DEFAULT_RATING: 0,
  IS_ACTIVE: true,
} as const;

// ============================================================================
// QUERY CONFIGURATION
// ============================================================================

export const QUERY_CONFIG = {
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100,
  REVIEWS_PER_PAGE: 20,
  PACKAGES_ORDER_BY: 'price',
  REVIEWS_ORDER_BY: 'created_at',
  ORDER_ASCENDING: true,
  ORDER_DESCENDING: false,
} as const;
