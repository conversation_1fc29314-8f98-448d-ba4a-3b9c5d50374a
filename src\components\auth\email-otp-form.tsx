'use client';

import { useState, useEffect } from 'react';
import { RotateCcw } from 'lucide-react';
import { SubmitButton } from './submit-button';
import { EnhancedAuthError } from './enhanced-auth-error';
import { Input } from '@/components/ui/input';

interface EmailOTPFormProps {
  onVerifyOTP: (formData: FormData) => Promise<void>;
  onResendOTP: (formData: FormData) => Promise<void>;
  error?: string;
  resent?: string;
}

export function EmailOTPForm({
  onVerifyOTP,
  onResendOTP,
  error,
  resent,
}: EmailOTPFormProps) {
  const [otpCode, setOtpCode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [canResend, setCanResend] = useState(true);

  // Countdown timer for resend button
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
    return undefined;
  }, [countdown]);

  // Start countdown when email is resent
  useEffect(() => {
    if (resent === 'true') {
      setCountdown(60); // 60 seconds countdown
      setCanResend(false);
    }
  }, [resent]);

  const handleVerifySubmit = async (formData: FormData) => {
    setIsVerifying(true);
    try {
      await onVerifyOTP(formData);
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResendSubmit = async (formData: FormData) => {
    setIsResending(true);
    setCountdown(60);
    setCanResend(false);
    try {
      await onResendOTP(formData);
    } finally {
      setIsResending(false);
    }
  };

  const handleOtpChange = (value: string) => {
    // Only allow numbers and limit to 6 digits
    const numericValue = value.replace(/\D/g, '').slice(0, 6);
    setOtpCode(numericValue);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-4">
      {/* Enhanced Error Message */}
      {error && (
        <EnhancedAuthError
          error={error}
          context={{
            action: 'verify_email',
            method: 'email',
            stage: 'verification',
          }}
          onRetry={() => window.location.reload()}
        />
      )}

      <form action={handleVerifySubmit} className="space-y-6">
        <div>
          <Input
            name="token"
            type="text"
            placeholder="6 haneli doğrulama kodu"
            value={otpCode}
            onChange={e => handleOtpChange(e.target.value)}
            required
            className="border-input focus:border-primary focus:ring-primary/20 h-12 text-center font-mono text-2xl tracking-widest transition-colors"
            maxLength={6}
            pattern="[0-9]{6}"
            autoComplete="one-time-code"
            autoFocus
            disabled={isVerifying}
          />
        </div>

        <SubmitButton
          disabled={otpCode.length !== 6 || isVerifying}
          className="bg-primary hover:bg-primary/90 text-primary-foreground h-12 w-full transform text-base font-medium shadow-lg transition-all duration-200 hover:scale-[1.02] hover:shadow-xl disabled:transform-none disabled:cursor-not-allowed disabled:opacity-50"
        >
          {isVerifying ? 'Doğrulanıyor...' : 'Doğrula'}
        </SubmitButton>
      </form>

      {/* Resend Button */}
      <div className="mt-4">
        <form action={handleResendSubmit}>
          <SubmitButton
            disabled={!canResend || isResending}
            className="bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-10 w-full border text-sm shadow-xs disabled:cursor-not-allowed disabled:opacity-50"
          >
            <RotateCcw className="mr-2 h-4 w-4" />
            {isResending
              ? 'Gönderiliyor...'
              : !canResend
                ? `Kodu Yeniden Gönder (${formatTime(countdown)})`
                : 'Kodu Yeniden Gönder'}
          </SubmitButton>
        </form>
      </div>

      {/* Spam Warning */}
      <div className="mt-4 rounded-lg border border-amber-500/20 bg-amber-500/10 p-4 dark:border-amber-400/20 dark:bg-amber-400/10">
        <h3 className="mb-1 text-sm font-medium text-amber-800 dark:text-amber-300">
          E-posta gelmedi mi?
        </h3>
        <p className="text-xs text-amber-700 dark:text-amber-400">
          Spam/Junk klasörünüzü kontrol edin. Bazen doğrulama e-postaları oraya
          düşebilir.
        </p>
      </div>
    </div>
  );
}
