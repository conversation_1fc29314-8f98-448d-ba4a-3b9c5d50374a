'use server';

/**
 * Utility functions for core action functionality
 * Following Clean Code principles - small, focused, single-responsibility functions
 */

import { revalidatePath } from 'next/cache';
import { createClient } from '@/lib/supabase/server';
import { logger } from '@/lib/logger';
import { ErrorType, AppError } from '@/lib/utils/error-types';
import { ApiResponse } from '@/types/global/api';
import { z } from 'zod';

import { DATABASE_ERROR_CODES, CORE_ERROR_MESSAGES } from './core-constants';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface RevalidationConfig {
  revalidatePaths?: string[];
  revalidatePathsWithType?: Array<{
    path: string;
    type: 'page' | 'layout';
  }>;
}

export interface ActionOptions extends RevalidationConfig {
  requireAuth?: boolean;
}

// ============================================================================
// USER AUTHENTICATION
// ============================================================================

/**
 * Get user ID with optional authentication requirement
 * Following Clean Code principles - single responsibility, clear naming
 */
export async function getUserId(): Promise<string | undefined> {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  return user?.id;
}

// ============================================================================
// CACHE REVALIDATION
// ============================================================================

/**
 * Perform cache revalidation for specified paths
 * Following Clean Code principles - focused on single responsibility
 */
export async function performRevalidation(
  config: RevalidationConfig
): Promise<void> {
  const { revalidatePaths, revalidatePathsWithType } = config;
  const revalidationPromises: Promise<void>[] = [];

  if (revalidatePaths) {
    revalidationPromises.push(
      ...revalidatePaths.map(path => Promise.resolve(revalidatePath(path)))
    );
  }

  if (revalidatePathsWithType) {
    revalidationPromises.push(
      ...revalidatePathsWithType.map(({ path, type }) =>
        Promise.resolve(revalidatePath(path, type))
      )
    );
  }

  // Run all revalidation operations in parallel
  if (revalidationPromises.length > 0) {
    await Promise.allSettled(revalidationPromises);
  }
}

// ============================================================================
// ERROR HANDLING
// ============================================================================

/**
 * Check if error is a Supabase database error
 */
function isSupabaseError(
  error: unknown
): error is { code: string; message: string } {
  return (
    error !== null &&
    typeof error === 'object' &&
    'code' in error &&
    typeof (error as any).code === 'string'
  );
}

/**
 * Check if error is a validation error (Zod, etc.)
 */
function isValidationError(error: unknown): error is { issues: any[] } {
  return (
    error !== null &&
    typeof error === 'object' &&
    'issues' in error &&
    Array.isArray((error as any).issues)
  );
}

/**
 * Handle Supabase database errors
 * Following Clean Code principles - focused error handling with clear mapping
 */
export async function handleSupabaseError(error: {
  code: string;
  message: string;
}): Promise<ApiResponse<never>> {
  switch (error.code) {
    case DATABASE_ERROR_CODES.NO_ROWS_RETURNED:
      return {
        success: false,
        error: CORE_ERROR_MESSAGES.RECORD_NOT_FOUND,
        errorType: ErrorType.NOT_FOUND,
      };

    case DATABASE_ERROR_CODES.UNIQUE_VIOLATION:
      return {
        success: false,
        error: CORE_ERROR_MESSAGES.RECORD_ALREADY_EXISTS,
        errorType: ErrorType.CONFLICT,
      };

    case DATABASE_ERROR_CODES.FOREIGN_KEY_VIOLATION:
      return {
        success: false,
        error: CORE_ERROR_MESSAGES.FOREIGN_KEY_CONSTRAINT,
        errorType: ErrorType.CONFLICT,
      };

    case DATABASE_ERROR_CODES.INSUFFICIENT_PRIVILEGE:
      return {
        success: false,
        error: CORE_ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS,
        errorType: ErrorType.AUTHORIZATION,
      };

    case DATABASE_ERROR_CODES.RLS_POLICY_VIOLATION:
      // RLS hatalarını daha spesifik mesajlara çevir
      const rlsMessage = mapRLSErrorToUserMessage(error.message);
      return {
        success: false,
        error: rlsMessage,
        errorType: ErrorType.AUTHORIZATION,
      };

    default:
      return {
        success: false,
        error: CORE_ERROR_MESSAGES.DATABASE_OPERATION_FAILED,
        errorType: ErrorType.DATABASE,
      };
  }
}

/**
 * Map RLS error messages to user-friendly Turkish messages
 * Following Clean Code principles - specific error mapping for better UX
 */
function mapRLSErrorToUserMessage(errorMessage: string): string {
  const message = errorMessage.toLowerCase();

  // Gym erişim hataları
  if (message.includes('gym') || message.includes('salon')) {
    if (message.includes('subscription') || message.includes('abonelik')) {
      return CORE_ERROR_MESSAGES.SUBSCRIPTION_passive;
    }
    return CORE_ERROR_MESSAGES.GYM_RLS_ACCESS_DENIED;
  }

  // Trainer yetki hataları
  if (message.includes('trainer') || message.includes('antrenör')) {
    return CORE_ERROR_MESSAGES.TRAINER_PERMISSION_DENIED;
  }

  // Member erişim hataları
  if (message.includes('member') || message.includes('üye')) {
    return CORE_ERROR_MESSAGES.MEMBER_ACCESS_DENIED;
  }

  // Package erişim hataları
  if (message.includes('package') || message.includes('paket')) {
    return CORE_ERROR_MESSAGES.PACKAGE_ACCESS_DENIED;
  }

  // Appointment erişim hataları
  if (message.includes('appointment') || message.includes('randevu')) {
    return CORE_ERROR_MESSAGES.APPOINTMENT_ACCESS_DENIED;
  }

  // Staff erişim hataları
  if (message.includes('staff') || message.includes('personel')) {
    return CORE_ERROR_MESSAGES.STAFF_ACCESS_DENIED;
  }

  // Subscription hataları
  if (message.includes('subscription') || message.includes('abonelik')) {
    return CORE_ERROR_MESSAGES.SUBSCRIPTION_passive;
  }

  // Genel RLS hatası
  return CORE_ERROR_MESSAGES.RLS_POLICY_DENIED;
}

/**
 * Handle validation errors
 * Following Clean Code principles - focused validation error handling
 */
export async function handleValidationError(error: {
  issues: any[];
}): Promise<ApiResponse<never>> {
  const firstIssue = error.issues?.[0];
  const message = firstIssue?.message || CORE_ERROR_MESSAGES.VALIDATION_FAILED;

  return {
    success: false,
    error: message,
    errorType: ErrorType.VALIDATION,
  };
}

/**
 * Handle AppError instances
 * Following Clean Code principles - specific error type handling
 */
export async function handleAppError(
  error: AppError
): Promise<ApiResponse<never>> {
  return {
    success: false,
    error: error.message,
    errorType: error.type,
  };
}

/**
 * Handle generic Error instances
 * Following Clean Code principles - fallback error handling
 */
export async function handleGenericError(
  error: Error
): Promise<ApiResponse<never>> {
  return {
    success: false,
    error: error.message || CORE_ERROR_MESSAGES.UNEXPECTED_ERROR,
    errorType: ErrorType.INTERNAL,
  };
}

/**
 * Handle unknown errors
 * Following Clean Code principles - safe fallback for unknown error types
 */
export async function handleUnknownError(): Promise<ApiResponse<never>> {
  return {
    success: false,
    error: CORE_ERROR_MESSAGES.UNEXPECTED_ERROR,
    errorType: ErrorType.INTERNAL,
  };
}

/**
 * Standardized error handler for server actions
 * Following Clean Code principles - orchestrates error handling with clear delegation
 */
export async function handleActionError(
  error: unknown,
  context?: Record<string, any>
): Promise<ApiResponse<never>> {
  // If this is the special Next.js PPR postpone signal, do NOT catch it — rethrow to allow bailout
  if (isPPRPostpone(error)) {
    throw error as any;
  }

  // Log the error
  logger.error('Server action error', error, context);

  // Handle known AppError instances
  if (error instanceof AppError) {
    return await handleAppError(error);
  }

  // Handle Supabase errors
  if (isSupabaseError(error)) {
    return await handleSupabaseError(error);
  }

  // Handle validation errors (Zod, etc.)
  if (isValidationError(error)) {
    return await handleValidationError(error);
  }

  // Handle generic Error instances
  if (error instanceof Error) {
    return await handleGenericError(error);
  }

  // Handle unknown errors
  return await handleUnknownError();
}

/**
 * Detect Next.js PPR postpone signal errors that must be rethrown
 */
function isPPRPostpone(error: unknown): boolean {
  if (!error || typeof error !== 'object') return false;
  const e = error as any;

  // Check common patterns from Next.js runtime for PPR bailout
  const msg: string | undefined =
    typeof e.message === 'string' ? e.message : undefined;
  const stack: string | undefined =
    typeof e.stack === 'string' ? e.stack : undefined;

  // Known indicators
  const indicators = [
    'needs to bail out of prerendering',
    'ppr-caught-error',
    'unstable_postpone',
  ];

  return (
    (msg && indicators.some(i => msg.toLowerCase().includes(i))) ||
    (stack && indicators.some(i => stack.toLowerCase().includes(i))) ||
    // Some builds attach a digest-like marker for postpone
    (typeof e.digest === 'string' &&
      e.digest.toLowerCase().includes('postpone'))
  );
}

// ============================================================================
// FORM VALIDATION UTILITIES
// ============================================================================

/**
 * Validate form data with Zod schema
 * Following Clean Code principles - focused validation with clear error handling
 */
export async function validateFormData<T>(
  formData: FormData,
  schema: z.ZodSchema<T>
): Promise<{ data?: T; error?: string }> {
  try {
    // Convert FormData to object
    const rawData = await convertFormDataToObject(formData);

    // Log for debugging
    logger.info('Form data before validation', { rawData });

    // Validate with Zod schema
    const result = schema.safeParse(rawData);

    if (!result.success) {
      const errors = result.error.format();
      const firstError = result.error.issues[0];
      const errorMessage =
        firstError?.message || CORE_ERROR_MESSAGES.INVALID_DATA_FORMAT;

      logger.error('Validation errors', result.error, {
        errors,
        schema: schema.description,
        rawData,
      });
      return {
        error: errorMessage,
      };
    }

    return { data: result.data };
  } catch (error: unknown) {
    logger.error('Form validation error', error, {
      schema: schema.description,
    });
    return {
      error:
        error instanceof Error
          ? error.message
          : 'Form verisi doğrulanırken bir hata oluştu.',
    };
  }
}

/**
 * Convert FormData to object with proper type handling
 * Following Clean Code principles - single responsibility, clear naming
 */
async function convertFormDataToObject(
  formData: FormData
): Promise<Record<string, unknown>> {
  const rawData: Record<string, unknown> = {};

  for (const [key, value] of formData.entries()) {
    // Handle JSON string arrays
    if ((await isJsonArrayField(key)) && typeof value === 'string') {
      rawData[key] = await parseJsonSafely(value, []);
    } else if (await isTimeField(key)) {
      // Keep time inputs as strings (even empty ones)
      rawData[key] = value;
    } else {
      // Convert empty strings to undefined for other fields
      rawData[key] = value === '' ? undefined : value;
    }
  }

  return rawData;
}

/**
 * Check if field should be parsed as JSON array
 */
async function isJsonArrayField(key: string): Promise<boolean> {
  return key === 'features' || key === 'time_slots';
}

/**
 * Check if field is a time input
 */
async function isTimeField(key: string): Promise<boolean> {
  return key === 'opening_time' || key === 'closing_time';
}

/**
 * Safely parse JSON with fallback
 */
async function parseJsonSafely<T>(value: string, fallback: T): Promise<T> {
  try {
    return JSON.parse(value);
  } catch {
    return fallback;
  }
}
