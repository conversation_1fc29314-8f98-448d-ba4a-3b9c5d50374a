/**
 * Equipment Management Actions
 *
 * Server actions for equipment CRUD operations, maintenance tracking, and analytics
 */

'use server';

import { createAction } from '../../core/core';
import { ApiResponse } from '@/types/global/api';
import {
  GymEquipment,
  EquipmentCategory,
  EquipmentFormData,
  EquipmentAnalytics,
  EquipmentFilters,
  EquipmentListResponse,
} from '@/types/database/equipment-inventory';
import { z } from 'zod';

// =============================================
// VALIDATION SCHEMAS
// =============================================

const equipmentSchema = z.object({
  name: z
    .string()
    .min(1, 'Ekipman adı gereklidir')
    .max(200, 'Ekipman adı en fazla 200 karakter olabilir'),
  category_id: z.uuid().optional(),
  brand: z.string().max(100).optional(),
  model: z.string().max(100).optional(),
  serial_number: z.string().max(100).optional(),
  purchase_date: z.string().optional(),
  purchase_price: z.number().min(0).optional(),
  warranty_expiry: z.string().optional(),
  condition: z.enum(['excellent', 'good', 'fair', 'poor', 'out_of_order']),
  status: z.enum(['active', 'maintenance', 'retired']),
  location: z.string().max(200).optional(),
  notes: z.string().optional(),
  image_url: z.url().optional(),
});

// =============================================
// EQUIPMENT CRUD OPERATIONS
// =============================================

/**
 * Get all equipment for a gym with filters and analytics
 */
export async function getGymEquipment(
  gymId: string,
  filters?: EquipmentFilters
): Promise<ApiResponse<EquipmentListResponse>> {
  return await createAction<EquipmentListResponse>(async (_, supabase) => {
    let query = supabase
      .from('gym_equipment')
      .select(
        `
        *,
        category:equipment_categories(id, name)
      `
      )
      .eq('gym_id', gymId);

    // Apply filters
    if (filters?.category_id) {
      query = query.eq('category_id', filters.category_id);
    }
    if (filters?.condition) {
      query = query.eq('condition', filters.condition);
    }
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    if (filters?.location) {
      query = query.ilike('location', `%${filters.location}%`);
    }
    if (filters?.search) {
      query = query.or(
        `name.ilike.%${filters.search}%,brand.ilike.%${filters.search}%,model.ilike.%${filters.search}%`
      );
    }

    const { data: equipment, error } = await query.order('created_at', {
      ascending: false,
    });

    if (error) {
      throw new Error(`Ekipman listesi alınamadı: ${error.message}`);
    }

    // Get analytics
    const analytics = await getEquipmentAnalytics(gymId, supabase);

    return {
      equipment: equipment || [],
      total: equipment?.length || 0,
      analytics,
    };
  });
}

/**
 * Get equipment analytics for dashboard
 */
async function getEquipmentAnalytics(
  gymId: string,
  supabase: any
): Promise<EquipmentAnalytics> {
  // Get equipment counts by status and condition
  const { data: equipmentStats } = await supabase
    .from('gym_equipment')
    .select('status, condition')
    .eq('gym_id', gymId);

  const stats = equipmentStats || [];
  const totalEquipment = stats.length;
  const activeEquipment = stats.filter(
    (e: any) => e.status === 'active'
  ).length;
  const maintenanceEquipment = stats.filter(
    (e: any) => e.status === 'maintenance'
  ).length;
  const retiredEquipment = stats.filter(
    (e: any) => e.status === 'retired'
  ).length;

  const conditionBreakdown = {
    excellent: stats.filter((e: any) => e.condition === 'excellent').length,
    good: stats.filter((e: any) => e.condition === 'good').length,
    fair: stats.filter((e: any) => e.condition === 'fair').length,
    poor: stats.filter((e: any) => e.condition === 'poor').length,
    out_of_order: stats.filter((e: any) => e.condition === 'out_of_order')
      .length,
  };

  return {
    total_equipment: totalEquipment,
    active_equipment: activeEquipment,
    maintenance_equipment: maintenanceEquipment,
    retired_equipment: retiredEquipment,
    condition_breakdown: conditionBreakdown,
  };
}

/**
 * Get single equipment by ID
 */
export async function getEquipmentById(
  equipmentId: string
): Promise<ApiResponse<GymEquipment>> {
  return await createAction<GymEquipment>(async (_, supabase) => {
    const { data, error } = await supabase
      .from('gym_equipment')
      .select(
        `
        *,
        category:equipment_categories(id, name),
        usage_logs:equipment_usage_logs(*)
      `
      )
      .eq('id', equipmentId)
      .single();

    if (error) {
      throw new Error(`Ekipman bulunamadı: ${error.message}`);
    }

    return data;
  });
}

/**
 * Create new equipment
 */
export async function createEquipment(
  gymId: string,
  formData: EquipmentFormData
): Promise<ApiResponse<GymEquipment>> {
  return await createAction<GymEquipment>(
    async (_, supabase) => {
      // Validate input
      const validatedData = equipmentSchema.parse(formData);

      const { data, error } = await supabase
        .from('gym_equipment')
        .insert({
          gym_id: gymId,
          ...validatedData,
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Ekipman oluşturulamadı: ${error.message}`);
      }

      return data;
    },
    {
      revalidatePaths: [`/dashboard/gym/${gymId}/equipment`],
    }
  );
}

/**
 * Update equipment
 */
export async function updateEquipment(
  equipmentId: string,
  formData: EquipmentFormData
): Promise<ApiResponse<GymEquipment>> {
  return await createAction<GymEquipment>(async (_, supabase) => {
    // Validate input
    const validatedData = equipmentSchema.parse(formData);

    const { data, error } = await supabase
      .from('gym_equipment')
      .update(validatedData)
      .eq('id', equipmentId)
      .select()
      .single();

    if (error) {
      throw new Error(`Ekipman güncellenemedi: ${error.message}`);
    }

    return data;
  });
}

/**
 * Delete equipment
 */
export async function deleteEquipment(
  equipmentId: string
): Promise<ApiResponse<void>> {
  return await createAction<void>(
    async (_, supabase) => {
      const { error } = await supabase
        .from('gym_equipment')
        .delete()
        .eq('id', equipmentId);

      if (error) {
        throw new Error(`Ekipman silinemedi: ${error.message}`);
      }
    },
    {
      revalidatePaths: [`/dashboard/gym/[gymId]/equipment`],
    }
  );
}

// =============================================
// EQUIPMENT CATEGORIES
// =============================================

/**
 * Get all equipment categories
 */
export async function getEquipmentCategories(): Promise<
  ApiResponse<EquipmentCategory[]>
> {
  return await createAction<EquipmentCategory[]>(async (_, supabase) => {
    const { data, error } = await supabase
      .from('equipment_categories')
      .select('*')
      .order('name');

    if (error) {
      throw new Error(`Kategoriler alınamadı: ${error.message}`);
    }

    return data || [];
  });
}
