import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Mail, Plus, UserPlus, UserCheck } from 'lucide-react';
import Link from 'next/link';
import { Gyms } from '@/types/database/tables';

interface GymQuickActionsProps {
  gym: Gyms;
}

export function GymQuickActions({ gym }: GymQuickActionsProps) {
  const gymId = gym.id;
  const quickActions = [
    {
      title: '<PERSON><PERSON> Ekle',
      description: 'Yeni üye kaydı oluştur',
      icon: UserPlus,
      href: `/dashboard/gym/${gymId}/members?action=add`,
      color: 'text-primary',
      bgColor: 'bg-primary/10',
    },
    {
      title: 'Paket Oluştur',
      description: 'Yeni üyelik paketi ekle',
      icon: Plus,
      href: `/dashboard/gym/${gymId}/packages?action=create`,
      color: 'text-secondary-foreground',
      bgColor: 'bg-secondary/20',
    },
    {
      title: '<PERSON><PERSON>kle',
      description: 'Yeni personel kaydı oluştur',
      icon: UserCheck,
      href: `/dashboard/gym/${gymId}/staff?action=add`,
      color: 'text-accent-foreground',
      bgColor: 'bg-accent/20',
    },
    {
      title: 'Davetiye Gönder',
      description: 'Üye davetiyesi oluştur',
      icon: Mail,
      href: `/dashboard/gym/${gymId}/invitations?action=create`,
      color: 'text-chart-5',
      bgColor: 'bg-chart-5/10',
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-lg font-semibold">
          Hızlı Aksiyonlar
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-3 lg:grid-cols-4">
          {quickActions.map((action, index) => {
            const IconComponent = action.icon;
            return (
              <Link key={index} href={action.href}>
                <Button
                  variant="outline"
                  className="h-auto w-full flex-col items-start gap-1.5 rounded-xl border-dashed p-3 text-left transition-all hover:border-solid hover:shadow-sm sm:gap-2 sm:p-4"
                >
                  <div className={`rounded-lg p-1.5 sm:p-2 ${action.bgColor}`}>
                    <IconComponent
                      className={`h-4 w-4 sm:h-5 sm:w-5 ${action.color}`}
                    />
                  </div>
                  <div className="">
                    <div className="text-xs leading-tight font-semibold sm:text-sm">
                      {action.title}
                    </div>
                    <div className="text-muted-foreground mt-0.5 line-clamp-2 text-[10px] sm:mt-1 sm:text-xs">
                      {action.description}
                    </div>
                  </div>
                </Button>
              </Link>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
