'use server';

import { createAction } from '../../core/core';
import { ApiResponse } from '@/types/global/api';
import { Gyms } from '@/types/database/tables';

/**
 * Kullanıcının yönetici olduğu salonları getirir
 * RLS otomatik olarak sadece yetkili salonları döndürür
 * Company manager ve gym manager eri<PERSON><PERSON><PERSON> destekler
 */
export async function getGymManagerGyms(): Promise<ApiResponse<Gyms[]>> {
  return await createAction<Gyms[]>(async (_, supabase, userId) => {
    const { data, error } = await supabase
      .from('gyms')
      .select(`*`)
      .eq('manager_profile_id', userId);

    if (error) {
      throw new Error(error.message);
    }
    return data || [];
  });
}

/**
 * Gym manager'ın yönettiği salonların istatistiklerini getirir
 */
export async function getGymManagerStats(): Promise<
  ApiResponse<{
    totalGyms: number;
    totalMembers: number;
    totalRevenue: number;
    todayAppointments: number;
  }>
> {
  return await createAction<{
    totalGyms: number;
    totalMembers: number;
    totalRevenue: number;
    todayAppointments: number;
  }>(async (_, supabase, _userId) => {
    // Yönetilen salonları al - RLS politikası otomatik filtreleme yapacak
    const { data: gyms, error: gymsError } = await supabase
      .from('gyms')
      .select('id');
    // RLS politikası company manager ve gym manager erişimini otomatik kontrol eder

    if (gymsError || !gyms) {
      throw new Error('Salonlar getirilemedi');
    }

    const gymIds = gyms.map(gym => gym.id);

    if (gymIds.length === 0) {
      return {
        totalGyms: 0,
        totalMembers: 0,
        totalRevenue: 0,
        todayAppointments: 0,
      };
    }

    // Paralel olarak istatistikleri getir
    const [membersResult, appointmentsResult] = await Promise.all([
      // Toplam üye sayısı
      supabase
        .from('gym_memberships')
        .select('id', { count: 'exact' })
        .in('gym_id', gymIds)
        .eq('status', 'active'),

      // Bugünkü randevular
      supabase
        .from('appointments')
        .select('id', { count: 'exact' })
        .in('gym_id', gymIds)
        .gte('appointment_date', new Date().toISOString().split('T')[0])
        .lt(
          'appointment_date',
          new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        ),
    ]);

    return {
      totalGyms: gyms.length,
      totalMembers: membersResult.count || 0,
      totalRevenue: 0, // TODO: Gelir hesaplaması eklenebilir
      todayAppointments: appointmentsResult.count || 0,
    };
  });
}
