'use client';

import { useMemo, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search, Filter } from 'lucide-react';
import { MembershipWithMember } from '@/types/business/membership';
import { GymMembersStats } from './gym-members-stats';
import { GymMembersTable } from './gym-members-table';

interface GymMembersClientProps {
  members: MembershipWithMember[];
  gymId: string;
}

export function GymMembersClient({ members, gymId }: GymMembersClientProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');

  const filteredMembers = useMemo(() => {
    const normalizedSearch = searchTerm.trim().toLowerCase();
    const start = startDate ? new Date(startDate) : null;
    const end = endDate ? new Date(endDate) : null;

    return members.filter(m => {
      // Search by name, email or phone
      const name = m.member?.full_name?.toLowerCase() || '';
      const email = m.member?.email?.toLowerCase() || '';
      const phone = m.member?.phone_number?.toLowerCase?.() || '';
      const matchesSearch =
        !normalizedSearch ||
        name.includes(normalizedSearch) ||
        email.includes(normalizedSearch) ||
        phone.includes(normalizedSearch);

      // Status filter (keep only active/passive for now)
      const matchesStatus =
        statusFilter === 'all' ||
        (m.status || '').toLowerCase() === statusFilter;

      // Date range filter on membership created_at
      const createdAt = m.created_at ? new Date(m.created_at) : null;
      const matchesStart = !start || (createdAt && createdAt >= start);
      const matchesEnd = !end || (createdAt && createdAt <= end);

      return matchesSearch && matchesStatus && matchesStart && matchesEnd;
    });
  }, [members, searchTerm, statusFilter, startDate, endDate]);

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-5 w-5" /> Filtreler
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            {/* Search */}
            <div className="col-span-2">
              <div className="relative">
                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                <Input
                  placeholder="Üye adı, e-posta veya telefon ile ara..."
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Status */}
            <div className="w-full">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Durum filtresi" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tüm Durumlar</SelectItem>
                  <SelectItem value="active">Aktif</SelectItem>
                  <SelectItem value="passive">Pasif</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Date range */}
            <div className="grid grid-cols-2 gap-2">
              <Input
                type="date"
                value={startDate}
                onChange={e => setStartDate(e.target.value)}
                placeholder="Başlangıç"
              />
              <Input
                type="date"
                value={endDate}
                onChange={e => setEndDate(e.target.value)}
                placeholder="Bitiş"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Count */}
      <div className="flex items-center justify-between">
        <p className="text-muted-foreground text-sm">
          {filteredMembers.length} üye bulundu
        </p>
      </div>

      {/* Stats and Table use filtered data */}
      <GymMembersStats members={filteredMembers} />
      <GymMembersTable members={filteredMembers} gymId={gymId} />
    </div>
  );
}
