import { GymCardData, GymSearchFilters } from '@/types/business/gym';
import { getCityNameById } from '@/lib/constants';

/**
 * Round rating to 1 decimal place
 */
export const roundRating = (rating: number | null): number | null => {
  return rating ? Math.round(rating * 10) / 10 : null;
};

/**
 * Validate slug format
 */
export const validateSlug = (slug: string): boolean => {
  return Boolean(slug && slug.trim().length > 0);
};

/**
 * Validate search query
 */
export const validateSearchQuery = (query: string): boolean => {
  return Boolean(query && query.trim().length >= 2);
};

/**
 * Sanitize search query for PostgreSQL
 */
export const sanitizeSearchQuery = (query: string): string => {
  return query
    .trim()
    .replace(/[^\w\sğüşıöçĞÜŞİÖÇ]/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
};

/**
 * Create search vector query for PostgreSQL FTS
 */
export const createSearchVector = (query: string): string => {
  const sanitized = sanitizeSearchQuery(query);
  const terms = sanitized.split(' ').filter(term => term.length > 1);

  if (terms.length === 0) return '';

  // Create tsquery with prefix matching
  const tsquery = terms.map(term => `${term}:*`).join(' & ');
  return tsquery;
};

/**
 * Transform gym data to card format
 */
export const transformToGymCard = (
  gym: any,
  relevanceScore?: number
): GymCardData => ({
  slug: gym.slug,
  name: gym.name,
  city: getCityNameById(gym.city) || gym.city,
  district: gym.district,
  cover_image_url: gym.cover_image_url,
  average_rating: roundRating(gym.average_rating),
  review_count: gym.review_count || 0,
  ...(relevanceScore && { relevance_score: relevanceScore }),
  ...(gym.company && { company: gym.company }),
});

/**
 * Sort configurations and accessor
 */
const SORT_CONFIGS = {
  rating: [
    { column: 'average_rating', ascending: false, nullsFirst: false },
    { column: 'review_count', ascending: false, nullsFirst: false },
  ],
  newest: [{ column: 'created_at', ascending: false, nullsFirst: false }],
  popular: [
    { column: 'review_count', ascending: false, nullsFirst: false },
    { column: 'average_rating', ascending: false, nullsFirst: false },
  ],
  relevance: [], // Will be handled by search query
} as const;

export const getSortConfig = (
  sortBy: GymSearchFilters['sortBy'] = 'rating'
) => {
  return SORT_CONFIGS[sortBy] || SORT_CONFIGS.rating;
};
