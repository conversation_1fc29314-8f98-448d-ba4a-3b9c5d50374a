'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Building2, Plus, Sparkles } from 'lucide-react';
import Link from 'next/link';

export function EmptyCompaniesState() {
  return (
    <div className="flex min-h-[60vh] items-center justify-center">
      <Card className="w-full max-w-md">
        <CardContent className="flex flex-col items-center justify-center p-8 text-center">
          {/* Icon */}
          <div className="bg-primary/10 mb-4 flex h-16 w-16 items-center justify-center rounded-full">
            <Building2 className="text-primary h-8 w-8" />
          </div>

          {/* Title */}
          <h2 className="mb-2 text-2xl font-bold">İlk Şirketinizi Oluşturun</h2>

          {/* Description */}
          <p className="text-muted-foreground mb-6 leading-relaxed">
            Sportiva&apos;da şirket oluşturarak birden fazla şubeyi tek çatı
            altında yönetebilirsiniz. Müşterileriniz tüm şubelerinizi kolayca
            keşfedebilir.
          </p>

          {/* Features */}
          <div className="text-muted-foreground mb-6 space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <Sparkles className="text-primary h-4 w-4" />
              <span>Merkezi şirket yönetimi</span>
            </div>
            <div className="flex items-center gap-2">
              <Sparkles className="text-primary h-4 w-4" />
              <span>Şube bazlı organizasyon</span>
            </div>
            <div className="flex items-center gap-2">
              <Sparkles className="text-primary h-4 w-4" />
              <span>Tek URL altında tüm şubeler</span>
            </div>
          </div>

          {/* CTA Button */}
          <Link href="/dashboard/manager/company-setup" className="w-full">
            <Button size="lg" className="w-full">
              <Plus className="mr-2 h-5 w-5" />
              Şirket Kurulumunu Tamamla
            </Button>
          </Link>

          {/* Help Text */}
          <p className="text-muted-foreground mt-4 text-xs">
            Mevcut salonlarınız otomatik olarak şirketinize bağlanacak
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
