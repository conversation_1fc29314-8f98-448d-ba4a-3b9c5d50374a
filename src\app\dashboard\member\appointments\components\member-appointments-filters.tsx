'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface MemberAppointmentsFiltersProps {
  searchParams: {
    startDate?: string;
    endDate?: string;
    status?: string;
  };
}

export function MemberAppointmentsFilters({
  searchParams,
}: MemberAppointmentsFiltersProps) {
  const router = useRouter();
  const params = useSearchParams();

  const updateFilter = (key: string, value: string) => {
    const newParams = new URLSearchParams(params.toString());

    if (value && value !== 'all') {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }

    router.push(`?${newParams.toString()}`);
  };

  const clearFilters = () => {
    router.push('/dashboard/member/appointments');
  };

  const hasFilters = Object.values(searchParams).some(
    value => value && value !== 'all'
  );

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        {/* Start Date */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Başlangıç Tarihi</label>
          <Input
            type="date"
            value={searchParams.startDate || ''}
            onChange={e => updateFilter('startDate', e.target.value)}
          />
        </div>

        {/* End Date */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Bitiş Tarihi</label>
          <Input
            type="date"
            value={searchParams.endDate || ''}
            onChange={e => updateFilter('endDate', e.target.value)}
          />
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Durum</label>
          <Select
            value={searchParams.status || 'all'}
            onValueChange={value => updateFilter('status', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Durum seçin" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tüm Durumlar</SelectItem>
              <SelectItem value="scheduled">Planlandı</SelectItem>
              <SelectItem value="completed">Tamamlandı</SelectItem>
              <SelectItem value="cancelled">İptal Edildi</SelectItem>
              <SelectItem value="no_show">Gelmedi</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {hasFilters && (
        <div className="flex justify-end">
          <Button variant="outline" onClick={clearFilters}>
            <X className="mr-2 h-4 w-4" />
            Filtreleri Temizle
          </Button>
        </div>
      )}
    </div>
  );
}
