/**
 * Constants for membership operations
 * Following Clean Code principles - centralized constants to avoid magic numbers and strings
 */

// ============================================================================
// MEMBERSHIP STATUS CONSTANTS
// ============================================================================

export const MEMBERSHIP_STATUS = {
  ACTIVE: 'active',
  PASSIVE: 'passive',
  PENDING: 'pending',
  CANCELLED: 'cancelled',
  EXPIRED: 'expired',
} as const;

// ============================================================================
// PACKAGE STATUS CONSTANTS
// ============================================================================

export const PACKAGE_STATUS = {
  ACTIVE: 'active',
  passive: 'passive',
  EXPIRED: 'expired',
  CANCELLED: 'cancelled',
} as const;

// ============================================================================
// ERROR MESSAGES
// ============================================================================

export const MEMBERSHIP_ERROR_MESSAGES = {
  // Membership errors
  MEMBERSHIP_CREATION_FAILED: 'Salon üyeliği oluşturulamadı',
  MEMBERSHIP_NOT_FOUND: 'Üyelik bulunamadı',
  MEMBERSHIP_UPDATE_FAILED: 'Üyelik güncellenemedi',
  MEMBERSHIP_CANCEL_FAILED: 'Üyelik iptal edilemedi',

  // Package errors
  PACKAGE_NOT_FOUND: 'Paket bulunamadı',
  PACKAGE_CREATION_FAILED: 'Paket oluşturulamadı',
  PACKAGE_UPDATE_FAILED: 'Paket güncellenemedi',

  // Stats errors
  STATS_FETCH_FAILED: 'İstatistikler alınamadı',

  // General errors
  INVALID_USER_ID: 'Geçersiz kullanıcı ID',
  INVALID_GYM_ID: 'Geçersiz salon ID',
  INVALID_PACKAGE_ID: 'Geçersiz paket ID',
} as const;

// ============================================================================
// SUCCESS MESSAGES
// ============================================================================

export const MEMBERSHIP_SUCCESS_MESSAGES = {
  MEMBERSHIP_CREATED: 'Üyelik başarıyla oluşturuldu',
  MEMBERSHIP_UPDATED: 'Üyelik başarıyla güncellendi',
  MEMBERSHIP_CANCELLED: 'Üyelik başarıyla iptal edildi',
  PACKAGE_CREATED: 'Paket başarıyla oluşturuldu',
  PACKAGE_UPDATED: 'Paket başarıyla güncellendi',
} as const;

// ============================================================================
// DATABASE DEFAULTS
// ============================================================================

export const MEMBERSHIP_DEFAULTS = {
  STATUS: MEMBERSHIP_STATUS.ACTIVE,
  NULL_VALUE: null,
  EMPTY_STRING: '',
} as const;

// ============================================================================
// QUERY LIMITS
// ============================================================================

export const QUERY_LIMITS = {
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100,
  MIN_LIMIT: 1,
} as const;
