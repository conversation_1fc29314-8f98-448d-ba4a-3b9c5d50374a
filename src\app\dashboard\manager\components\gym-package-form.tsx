'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { GymPackages } from '@/types/database/tables';
import {
  createGymPackage,
  updateGymPackage,
} from '@/lib/actions/dashboard/company/package-actions';

// Form validasyon şeması - basitleştirilmiş
const packageFormSchema = z
  .object({
    name: z
      .string()
      .min(1, 'Paket adı gereklidir')
      .max(100, 'Paket adı en fazla 100 karakter olabilir'),
    package_type: z.enum(
      ['appointment_standard', 'appointment_vip', 'daily'],
      'Geçerli bir paket türü seçiniz'
    ),
    duration_days: z
      .number()
      .int()
      .positive('Süre pozitif olmalıdır')
      .optional()
      .or(z.literal(undefined)),
    price: z.number().positive("Fiyat 0'dan büyük olmalıdır"),
    description: z
      .string()
      .max(500, 'Açıklama en fazla 500 karakter olabilir')
      .optional(),
    max_participants: z
      .number()
      .int()
      .positive('Maksimum katılımcı sayısı pozitif olmalıdır')
      .optional(),
    session_count: z
      .number()
      .int()
      .positive('Seans sayısı pozitif olmalıdır')
      .optional(),
    session_duration_minutes: z
      .number()
      .int()
      .positive('Seans süresi pozitif olmalıdır')
      .optional(),
    is_active: z.boolean(),
  })
  .refine(
    data => {
      // Randevu bazlı paketler için validasyon
      if (
        data.package_type === 'appointment_standard' ||
        data.package_type === 'appointment_vip'
      ) {
        if (!data.session_count || data.session_count <= 0) {
          return false; // session_count zorunlu
        }
        if (
          !data.session_duration_minutes ||
          data.session_duration_minutes <= 0
        ) {
          return false; // session_duration_minutes zorunlu
        }
        if (!data.max_participants || data.max_participants <= 0) {
          return false; // max_participants zorunlu
        }
        // VIP paketlerde max_participants 1 olmalı
        if (
          data.package_type === 'appointment_vip' &&
          data.max_participants !== 1
        ) {
          return false;
        }
        // duration_days undefined olmalı
        if (data.duration_days !== undefined) {
          return false;
        }
      }

      // Günlük paketler için validasyon
      if (data.package_type === 'daily') {
        if (!data.duration_days || data.duration_days <= 0) {
          return false; // duration_days zorunlu
        }
        // session_count, session_duration_minutes, max_participants undefined olmalı
        if (data.session_count !== undefined) {
          return false;
        }
        if (data.session_duration_minutes !== undefined) {
          return false;
        }
        if (data.max_participants !== undefined) {
          return false;
        }
      }

      return true;
    },
    {
      message: 'Paket türüne göre gerekli alanları doldurun',
    }
  );

type PackageFormValues = z.infer<typeof packageFormSchema>;

interface GymPackageFormProps {
  gymId: string;
  onSuccess?: () => void;
  trigger: React.ReactNode;
  editPackage?: GymPackages | null;
}

export function GymPackageForm({
  gymId,
  onSuccess,
  trigger,
  editPackage,
}: GymPackageFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const form = useForm<PackageFormValues>({
    resolver: zodResolver(packageFormSchema),
    defaultValues: {
      name: editPackage?.name || '',
      package_type:
        (editPackage?.package_type as
          | 'appointment_standard'
          | 'appointment_vip'
          | 'daily') || 'appointment_standard',
      duration_days: editPackage?.duration_days || undefined,
      price: editPackage?.price || 0,
      description: editPackage?.description || '',
      max_participants: editPackage?.max_participants || undefined,
      session_count: editPackage?.session_count || undefined,
      session_duration_minutes:
        editPackage?.session_duration_minutes || undefined,
      is_active: editPackage?.is_active ?? true,
    },
  });

  const packageType = form.watch('package_type');

  // Paket türü değiştiğinde form alanlarını ayarla
  useEffect(() => {
    if (packageType === 'appointment_vip') {
      form.setValue('max_participants', 1);
      form.setValue('duration_days', undefined);
    } else if (packageType === 'appointment_standard') {
      form.setValue('duration_days', undefined);
      if (!form.getValues('max_participants')) {
        form.setValue('max_participants', 1);
      }
    } else if (packageType === 'daily') {
      // Günlük paket seçildiğinde randevu alanlarını temizle
      form.setValue('max_participants', undefined);
      form.setValue('session_count', undefined);
      form.setValue('session_duration_minutes', undefined);
      // Eğer duration_days boşsa varsayılan değer ata
      if (!form.getValues('duration_days')) {
        form.setValue('duration_days', 30);
      }
    }
  }, [packageType, form]);

  // editPackage değiştiğinde form değerlerini güncelle
  useEffect(() => {
    if (editPackage) {
      form.reset({
        name: editPackage.name,
        package_type:
          (editPackage.package_type as
            | 'appointment_standard'
            | 'appointment_vip'
            | 'daily') || 'appointment_standard',
        duration_days: editPackage.duration_days || undefined,
        price: editPackage.price,
        description: editPackage.description || '',
        max_participants: editPackage.max_participants || undefined,
        session_count: editPackage.session_count || undefined,
        session_duration_minutes:
          editPackage.session_duration_minutes || undefined,
        is_active: editPackage.is_active ?? true,
      });
    } else {
      form.reset({
        name: '',
        package_type: 'appointment_standard',
        duration_days: undefined,
        price: 0,
        description: '',
        max_participants: 1,
        session_count: 1,
        session_duration_minutes: 60,
        is_active: true,
      });
    }
  }, [editPackage, form]);

  const onSubmit = async (values: PackageFormValues) => {
    setIsLoading(true);
    try {
      // Paket türüne göre veri hazırlama
      let packageData: any = {
        name: values.name,
        package_type: values.package_type,
        price: values.price,
        description: values.description || null,
        is_active: values.is_active,
      };

      if (
        values.package_type === 'appointment_standard' ||
        values.package_type === 'appointment_vip'
      ) {
        // Randevu bazlı paket
        packageData = {
          ...packageData,
          max_participants: values.max_participants!,
          session_count: values.session_count!,
          session_duration_minutes: values.session_duration_minutes!,
          duration_days: null,
        };
      } else {
        // Günlük paket (daily)
        packageData = {
          ...packageData,
          duration_days: values.duration_days!,
          max_participants: null,
          session_count: null,
          session_duration_minutes: null,
        };
      }

      if (editPackage) {
        // Düzenleme modu
        await updateGymPackage(gymId, editPackage.id, packageData);
      } else {
        // Yeni paket oluşturma modu
        await createGymPackage(gymId, packageData);
      }

      toast.success(
        editPackage
          ? 'Paket başarıyla güncellendi.'
          : 'Paket başarıyla oluşturuldu.'
      );
      form.reset();
      setOpen(false);
      onSuccess?.();
    } catch (error) {
      console.error('Paket işlemi hatası:', error);
      toast.error(
        error instanceof Error ? error.message : 'Beklenmeyen bir hata oluştu.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {editPackage ? 'Paket Düzenle' : 'Yeni Paket Ekle'}
          </DialogTitle>
          <DialogDescription>
            {editPackage
              ? 'Mevcut paketi düzenleyin.'
              : 'Salonunuz için yeni bir üyelik paketi oluşturun.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField<PackageFormValues>
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Paket Adı</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Örn: Aylık Üyelik"
                      {...field}
                      value={field.value as string}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField<PackageFormValues>
              control={form.control}
              name="package_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Paket Türü</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value as string}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Paket türü seçin" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="appointment_standard">
                        Randevu - Standart (Grup)
                      </SelectItem>
                      <SelectItem value="appointment_vip">
                        Randevu - VIP (Kişisel)
                      </SelectItem>
                      <SelectItem value="daily">Günlük Üyelik</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Randevu paketleri seans bazlı, günlük paketler süre bazlıdır
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField<PackageFormValues>
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Fiyat (₺)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      value={field.value?.toString() || ''}
                      onChange={e => {
                        const value = e.target.value;
                        field.onChange(value === '' ? 0 : parseFloat(value));
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Günlük paket için süre alanı */}
            {packageType === 'daily' && (
              <FormField<PackageFormValues>
                control={form.control}
                name="duration_days"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Süre (Gün)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="30"
                        value={field.value?.toString() || ''}
                        onChange={e => {
                          const value = e.target.value;
                          field.onChange(
                            value === '' ? undefined : parseInt(value)
                          );
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Üyelik süresi (örn: 30, 60, 90, 365 gün)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Randevu bazlı paketler için alanlar */}
            {(packageType === 'appointment_standard' ||
              packageType === 'appointment_vip') && (
              <>
                <FormField<PackageFormValues>
                  control={form.control}
                  name="max_participants"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Maksimum Katılımcı Sayısı</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="1"
                          value={field.value?.toString() || ''}
                          onChange={e => {
                            const value = e.target.value;
                            const numValue = parseInt(value) || 1;
                            // VIP paketlerde max_participants otomatik olarak 1
                            if (packageType === 'appointment_vip') {
                              field.onChange(1);
                            } else {
                              field.onChange(numValue);
                            }
                          }}
                          disabled={packageType === 'appointment_vip'}
                        />
                      </FormControl>
                      <FormDescription>
                        {packageType === 'appointment_standard' &&
                          'Standart paket: Grup antrenmanı (1 veya daha fazla kişi)'}
                        {packageType === 'appointment_vip' &&
                          'VIP paket: Kişisel antrenman (sadece 1 kişi)'}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField<PackageFormValues>
                  control={form.control}
                  name="session_count"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Seans Sayısı</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="1"
                          value={field.value?.toString() || ''}
                          onChange={e => {
                            const value = e.target.value;
                            field.onChange(parseInt(value) || 1);
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        Bu pakette kaç seans randevu alınabilir
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField<PackageFormValues>
                  control={form.control}
                  name="session_duration_minutes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Seans Süresi (Dakika)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="60"
                          value={field.value?.toString() || ''}
                          onChange={e => {
                            const value = e.target.value;
                            field.onChange(parseInt(value) || 60);
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        Her seansın süresi (dakika cinsinden)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            <FormField<PackageFormValues>
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Açıklama (Opsiyonel)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Paket hakkında açıklama..."
                      className="resize-none"
                      {...field}
                      value={(field.value as string) || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField<PackageFormValues>
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>Aktif</FormLabel>
                    <FormDescription>
                      Paket satışa açık olsun mu?
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value as boolean}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isLoading}
              >
                İptal
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {editPackage ? 'Paketi Güncelle' : 'Paket Oluştur'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
