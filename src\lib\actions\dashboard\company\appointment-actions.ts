'use server';

import { z } from 'zod';
import { createClient } from '@/lib/supabase/server';
import { createAction, validateFormData } from '../../core';
import { ApiResponse } from '@/types/global/api';
import {
  Appointments,
  AppointmentParticipants,
  GymMembershipPackages,
  GymMemberships,
  Profiles,
  GymPackages,
} from '@/types/database/tables';
import { AppointmentStatus, ParticipantStatus } from '@/lib/supabase/types';
import { createNotification } from '@/lib/actions/notifications/notification-actions';

// Helper: fetch computed remaining sessions for package ids from view
async function getComputedRemainingForPackages(
  supabase: any,
  packageIds: string[]
): Promise<Map<string, number>> {
  if (!packageIds || packageIds.length === 0) return new Map();
  const { data, error } = await supabase
    .from('v_gym_membership_packages_with_stats')
    .select('id, computed_remaining_sessions')
    .in('id', packageIds);
  if (error) {
    throw new Error(`Paket kalan seansları getirilemedi: ${error.message}`);
  }
  const map = new Map<string, number>();
  (data || []).forEach((row: any) =>
    map.set(row.id, row.computed_remaining_sessions ?? 0)
  );
  return map;
}

// Types for trainer with profile information
export interface TrainerWithProfile extends Profiles {
  gym_trainer_id: string;
  gym_id: string;
  trainer_profile_id: string;
  status: string;
}

// Validation schemas
const createAppointmentSchema = z.object({
  gym_id: z.uuid('Geçersiz salon ID'),
  appointment_date: z
    .string()
    .min(1, 'Randevu tarihi gerekli')
    .refine(date => {
      const parsedDate = new Date(date);
      return !isNaN(parsedDate.getTime());
    }, 'Geçersiz tarih formatı'),
  appointment_type: z.string().min(1, 'Randevu türü gerekli'),
  max_participants: z.coerce
    .number()
    .min(1, 'Maksimum katılımcı sayısı en az 1 olmalı'),
  trainer_profile_id: z.uuid('Geçersiz antrenör ID').optional(),
  gym_package_id: z.uuid('Geçersiz paket ID'),
  notes: z.string().optional(),
});

const createAppointmentParticipantSchema = z.object({
  appointment_id: z.uuid('Geçersiz randevu ID'),
  gym_membership_package_id: z.uuid('Geçersiz paket ID'),
  notes: z.string().optional(),
});

const updateAppointmentStatusSchema = z.object({
  appointment_id: z.uuid(),
  status: z.enum(['scheduled', 'completed', 'cancelled']),
});

// Types for responses
interface AppointmentWithDetails extends Appointments {
  trainer_profile?: Profiles;
  participants?: (AppointmentParticipants & {
    membership?: GymMemberships & {
      profile?: Profiles;
    };
    membership_package?: GymMembershipPackages & {
      gym_package?: GymPackages;
    };
  })[];
}

interface MemberWithPackages extends Profiles {
  gym_memberships?: (GymMemberships & {
    gym_membership_packages?: (GymMembershipPackages & {
      gym_package?: GymPackages;
    })[];
  })[];
}

/**
 * Get all appointments for a gym
 */
export async function getGymAppointments(
  gymId: string,
  filters?: {
    startDate?: string;
    endDate?: string;
    trainerId?: string;
    status?: AppointmentStatus;
  }
): Promise<ApiResponse<AppointmentWithDetails[]>> {
  try {
    const supabase = await createClient();

    // İlk olarak basit appointments sorgusunu dene
    let appointmentsQuery = supabase
      .from('appointments')
      .select(
        `
        *,
        trainer_profile:profiles!appointments_trainer_profile_id_fkey(
          id,
          full_name,
          avatar_url
        )
      `
      )
      .eq('gym_id', gymId)
      .order('appointment_date', { ascending: false });

    // Apply filters to appointments
    if (filters?.startDate) {
      appointmentsQuery = appointmentsQuery.gte(
        'appointment_date',
        filters.startDate
      );
    }
    if (filters?.endDate) {
      appointmentsQuery = appointmentsQuery.lte(
        'appointment_date',
        filters.endDate
      );
    }
    if (filters?.trainerId) {
      appointmentsQuery = appointmentsQuery.eq(
        'trainer_profile_id',
        filters.trainerId
      );
    }
    if (filters?.status) {
      appointmentsQuery = appointmentsQuery.eq('status', filters.status);
    }

    const { data: appointments, error: appointmentsError } =
      await appointmentsQuery;

    if (appointmentsError) {
      throw new Error(`Randevular getirilemedi: ${appointmentsError.message}`);
    }

    if (!appointments || appointments.length === 0) {
      return { success: true, data: [] };
    }

    // Ayrı olarak participants'ları getir
    const appointmentIds = appointments.map(a => a.id);
    const { data: participants, error: participantsError } = await supabase
      .from('appointment_participants')
      .select(
        `
        *,
        membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
          *,
          membership:gym_memberships!gym_membership_packages_membership_id_fkey(
            *,
            profile:profiles!gym_memberships_profile_id_fkey(
              id,
              full_name,
              avatar_url
            )
          ),
          gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(
            name,
            package_type,
            session_duration_minutes
          )
        )
      `
      )
      .in('appointment_id', appointmentIds);

    if (participantsError) {
      console.warn('Katılımcılar getirilemedi:', participantsError.message);
      // Katılımcılar getirilemezse sadece randevuları döndür
      const result = appointments.map(appointment => ({
        ...appointment,
        participants: [],
      })) as AppointmentWithDetails[];
      return { success: true, data: result };
    }

    // Participants'ları appointment'lara grupla
    const participantsByAppointment = (participants || []).reduce(
      (acc, participant) => {
        const appointmentId = participant.appointment_id;
        if (!acc[appointmentId]) {
          acc[appointmentId] = [];
        }
        acc[appointmentId].push(participant);
        return acc;
      },
      {} as Record<string, any[]>
    );

    // Combine appointments with their participants
    const result = appointments.map(appointment => ({
      ...appointment,
      participants: participantsByAppointment[appointment.id] || [],
    })) as AppointmentWithDetails[];

    return { success: true, data: result };
  } catch (error) {
    console.error('getGymAppointments error:', error);
    return {
      success: false,
      error: `Randevular yüklenirken hata oluştu: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
    };
  }
}

/**
 * Get gym members with their active packages for appointment creation
 */
export async function getGymMembersWithPackages(
  gymId: string
): Promise<ApiResponse<MemberWithPackages[]>> {
  return createAction<MemberWithPackages[]>(async (_, supabase, _userId) => {
    const { data, error } = await supabase
      .from('profiles')
      .select(
        `
          *,
          gym_memberships!inner(
            *,
            gym_membership_packages!inner(
              *,
              gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(
                name,
                package_type,
                max_participants,
                session_count,
                session_duration_minutes
              )
            )
          )
        `
      )
      .eq('gym_memberships.gym_id', gymId)
      .eq('gym_memberships.status', 'active')
      .eq('gym_memberships.gym_membership_packages.status', 'active');

    if (error) {
      throw new Error(`Üyeler getirilemedi: ${error.message}`);
    }

    const members = (data || []) as MemberWithPackages[];
    // Collect all package ids
    const packageIds: string[] = [];
    for (const m of members) {
      const memberships = m.gym_memberships || [];
      for (const ms of memberships) {
        const pkgs = (ms.gym_membership_packages || []) as any[];
        pkgs.forEach(p => packageIds.push(p.id));
      }
    }
    const remainingMap = await getComputedRemainingForPackages(
      supabase,
      packageIds
    );
    // Override remaining_sessions with computed and filter out packages with no remaining
    for (const m of members) {
      const memberships = m.gym_memberships || [];
      for (const ms of memberships) {
        ms.gym_membership_packages = (ms.gym_membership_packages || [])
          .map((p: any) => {
            const computed = remainingMap.get(p.id);
            return {
              ...p,
              remaining_sessions: computed ?? p.remaining_sessions,
              computed_remaining_sessions: computed ?? p.remaining_sessions,
            };
          })
          .filter(
            (p: any) =>
              (p.computed_remaining_sessions ?? p.remaining_sessions ?? 0) > 0
          ) as any;
      }
    }
    // Optionally filter out memberships without packages
    const filtered = members.map(m => ({
      ...m,
      gym_memberships: (m.gym_memberships || []).filter(
        ms => (ms.gym_membership_packages || []).length > 0
      ),
    })) as MemberWithPackages[];

    return filtered;
  });
}

/**
 * Get available trainers for a gym
 */
export async function getGymTrainers(
  gymId: string
): Promise<ApiResponse<TrainerWithProfile[]>> {
  return createAction<TrainerWithProfile[]>(async (_, supabase, _userId) => {
    const { data, error } = await supabase
      .from('gym_trainers')
      .select(
        `
            id,
            gym_id,
            trainer_profile_id,
            status,
            created_at,
            profiles:trainer_profile_id (
              id,
              created_at,
              updated_at,
              email,
              full_name,
              avatar_url,
              phone_number,
              is_guest_account
            )
          `
      )
      .eq('gym_id', gymId)
      .eq('status', 'active')
      .order('created_at', { ascending: false });
    if (error) {
      throw new Error(`Antrenörler getirilemedi: ${error.message}`);
    }

    // Transform data to include profile information at the top level
    const trainersWithProfiles: TrainerWithProfile[] =
      data?.map(trainer => {
        const profile = Array.isArray(trainer.profiles)
          ? trainer.profiles[0]
          : trainer.profiles;

        if (!profile) {
          throw new Error('Antrenör profil bilgisi bulunamadı');
        }

        return {
          // Profile fields
          id: profile.id,
          created_at: profile.created_at,
          updated_at: profile.updated_at,
          email: profile.email,
          full_name: profile.full_name,
          avatar_url: profile.avatar_url,
          phone_number: profile.phone_number,
          is_guest_account: profile.is_guest_account,
          // Gym trainer fields
          gym_trainer_id: trainer.id,
          gym_id: trainer.gym_id,
          trainer_profile_id: trainer.trainer_profile_id,
          status: trainer.status,
        };
      }) || [];

    return trainersWithProfiles;
  });
}

/**
 * Check trainer availability for a specific date and time
 */
export async function checkTrainerAvailability(
  trainerId: string,
  appointmentDate: string,
  sessionDurationMinutes: number = 60,
  gym_id: string
): Promise<
  ApiResponse<{
    available: boolean;
    conflictingAppointments: Appointments[];
    existingGroupAppointment?: Appointments;
  }>
> {
  return createAction<{
    available: boolean;
    conflictingAppointments: Appointments[];
    existingGroupAppointment?: Appointments;
  }>(async (_, supabase, _userId) => {
    const appointmentDateTime = new Date(appointmentDate);

    // Calculate session time range
    const sessionStart = new Date(appointmentDateTime);
    const sessionEnd = new Date(
      appointmentDateTime.getTime() + sessionDurationMinutes * 60 * 1000
    );

    const { data: conflictingAppointments, error } = await supabase
      .from('appointments')
      .select(
        `
          *,
          participants:appointment_participants(count)
        `
      )
      .eq('gym_id', gym_id)
      .eq('trainer_profile_id', trainerId)
      .eq('status', 'scheduled')
      .gte('appointment_date', sessionStart.toISOString())
      .lt('appointment_date', sessionEnd.toISOString());

    if (error) {
      throw new Error(
        `Antrenör müsaitlik kontrolü yapılamadı: ${error.message}`
      );
    }

    // Check for exact time match (potential group appointment)
    const exactTimeMatch = conflictingAppointments?.find(apt => {
      const aptDate = new Date(apt.appointment_date);
      return (
        Math.abs(aptDate.getTime() - appointmentDateTime.getTime()) < 60000
      ); // 1 minute tolerance
    });

    // For group appointments, check if there's space for more participants
    let canJoinExistingGroup = false;
    if (exactTimeMatch) {
      const currentParticipants = exactTimeMatch.participants?.[0]?.count || 0;
      canJoinExistingGroup =
        currentParticipants < exactTimeMatch.max_participants;
    }

    // Check for time conflicts (overlapping sessions that are NOT exact matches)
    const timeConflicts =
      conflictingAppointments?.filter(apt => {
        const aptDate = new Date(apt.appointment_date);
        const aptEnd = new Date(
          aptDate.getTime() + sessionDurationMinutes * 60 * 1000
        );

        // Check if sessions overlap but are not exact matches
        return (
          aptDate < sessionEnd &&
          aptEnd > sessionStart &&
          Math.abs(aptDate.getTime() - appointmentDateTime.getTime()) >= 60000 // Not exact match
        );
      }) || [];

    return {
      available: timeConflicts.length === 0,
      conflictingAppointments: timeConflicts,
      existingGroupAppointment: canJoinExistingGroup
        ? exactTimeMatch
        : undefined,
    };
  });
}

/**
 * Get upcoming group classes for a specific package type
 */
export async function getUpcomingGroupClasses(
  gymId: string,
  packageType: string,
  memberProfileId?: string,
  days: number = 7
): Promise<ApiResponse<any[]>> {
  return createAction<any[]>(async (_, supabase, _userId) => {
    const now = new Date();
    const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);

    // Get upcoming group appointments with participant count
    const { data: appointments, error } = await supabase
      .from('appointments')
      .select(
        `
          *,
          trainer_profile:profiles!appointments_trainer_profile_id_fkey(
            id,
            full_name
          ),
          gym_package:gym_packages!appointments_gym_package_id_fkey(
            name,
            package_type,
            max_participants
          ),
          participants:appointment_participants(
            id,
            status,
            membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
              membership:gym_memberships!gym_membership_packages_membership_id_fkey(
                profile:profiles!gym_memberships_profile_id_fkey(
                  full_name
                )
              )
            )
          )
        `
      )
      .eq('gym_id', gymId)
      .eq('status', 'scheduled')
      .eq('appointment_type', 'appointment_standard')
      .gte('appointment_date', now.toISOString())
      .lte('appointment_date', futureDate.toISOString())
      .order('appointment_date', { ascending: true });

    if (error) {
      throw new Error(`Yaklaşan grup dersleri getirilemedi: ${error.message}`);
    }

    // Filter by package type and add participant count
    const filteredAppointments =
      appointments
        ?.filter(apt => apt.gym_package?.package_type === packageType)
        .map(apt => {
          const confirmedParticipants =
            apt.participants?.filter((p: any) => p.status === 'confirmed') ||
            [];
          const availableSpots =
            apt.max_participants - confirmedParticipants.length;

          // Check if the specific member is already participating
          const memberAlreadyJoined = memberProfileId
            ? confirmedParticipants.some(
                (p: any) =>
                  p.membership_package?.membership?.profile?.id ===
                  memberProfileId
              )
            : false;

          return {
            ...apt,
            participant_count: confirmedParticipants.length,
            available_spots: availableSpots,
            can_join: availableSpots > 0 && !memberAlreadyJoined,
            member_already_joined: memberAlreadyJoined,
            participants: confirmedParticipants,
          };
        }) || [];

    return filteredAppointments;
  });
}

/**
 * Get available time slots for a trainer on a specific date
 */
export async function getAvailableTimeSlots(
  trainerId: string,
  date: string,
  sessionDurationMinutes: number = 60,
  gymId: string
): Promise<ApiResponse<any[]>> {
  return createAction<any[]>(async (_, supabase, _userId) => {
    const targetDate = new Date(date);
    const startOfDay = new Date(targetDate);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);

    // Get trainer's existing appointments for the day
    const { data: existingAppointments, error } = await supabase
      .from('appointments')
      .select('appointment_date')
      .eq('gym_id', gymId)
      .eq('trainer_profile_id', trainerId)
      .eq('status', 'scheduled')
      .gte('appointment_date', startOfDay.toISOString())
      .lte('appointment_date', endOfDay.toISOString());

    if (error) {
      throw new Error(`Antrenör randevuları getirilemedi: ${error.message}`);
    }

    // Salonun tanımlı randevu saatlerini kullan (gyms.time_slots)
    const { data: gym } = await supabase
      .from('gyms')
      .select('time_slots')
      .eq('id', gymId)
      .single();

    const configuredSlots: string[] = Array.isArray(gym?.time_slots)
      ? (gym!.time_slots as string[])
      : [];

    const timeSlots: any[] = [];

    if (configuredSlots.length > 0) {
      // Gym'in saat listesine göre üret
      for (const t of configuredSlots) {
        const [hhStr, mmStr] = t.split(':');
        const hour = parseInt(hhStr, 10);
        const minute = parseInt(mmStr || '0', 10);

        const slotTime = new Date(targetDate);
        slotTime.setHours(hour, minute, 0, 0);

        // Mevcut randevularla çakışma var mı kontrol et
        const hasConflict = existingAppointments?.some(apt => {
          const aptDate = new Date(apt.appointment_date);
          const aptEnd = new Date(
            aptDate.getTime() + sessionDurationMinutes * 60 * 1000
          );
          const slotEnd = new Date(
            slotTime.getTime() + sessionDurationMinutes * 60 * 1000
          );
          return slotTime < aptEnd && slotEnd > aptDate;
        });

        timeSlots.push({
          time: slotTime.toISOString(),
          display_time: slotTime.toLocaleTimeString('tr-TR', {
            hour: '2-digit',
            minute: '2-digit',
          }),
          available: !hasConflict,
          period: hour < 12 ? 'morning' : hour < 17 ? 'afternoon' : 'evening',
        });
      }
    } else {
      // Fallback: Önceki mock saat aralığı (09:00 - 20:00, saat başı)
      for (let hour = 9; hour <= 20; hour++) {
        for (let minute = 0; minute < 60; minute += 60) {
          const slotTime = new Date(targetDate);
          slotTime.setHours(hour, minute, 0, 0);

          const hasConflict = existingAppointments?.some(apt => {
            const aptDate = new Date(apt.appointment_date);
            const aptEnd = new Date(
              aptDate.getTime() + sessionDurationMinutes * 60 * 1000
            );
            const slotEnd = new Date(
              slotTime.getTime() + sessionDurationMinutes * 60 * 1000
            );
            return slotTime < aptEnd && slotEnd > aptDate;
          });

          timeSlots.push({
            time: slotTime.toISOString(),
            display_time: slotTime.toLocaleTimeString('tr-TR', {
              hour: '2-digit',
              minute: '2-digit',
            }),
            available: !hasConflict,
            period: hour < 12 ? 'morning' : hour < 17 ? 'afternoon' : 'evening',
          });
        }
      }
    }

    return timeSlots;
  });
}

/**
 * Find existing group appointment for a specific time and trainer
 */
export async function findExistingGroupAppointment(
  trainerId: string,
  appointmentDate: string,
  packageType: string,
  gym_id: string
): Promise<ApiResponse<Appointments | null>> {
  return createAction<Appointments | null>(async (_, supabase, _userId) => {
    // Only standard appointments can be group appointments
    if (packageType !== 'appointment_standard') {
      return null;
    }

    const appointmentDateTime = new Date(appointmentDate);

    // Find exact time match for group appointments
    const { data: existingAppointments, error } = await supabase
      .from('appointments')
      .select(
        `
          *,
          participants:appointment_participants(count)
        `
      )
      .eq('gym_id', gym_id)
      .eq('trainer_profile_id', trainerId)
      .eq('status', 'scheduled')

      .eq('appointment_type', 'appointment_standard')
      .gte(
        'appointment_date',
        new Date(appointmentDateTime.getTime() - 60000).toISOString()
      ) // 1 minute before
      .lte(
        'appointment_date',
        new Date(appointmentDateTime.getTime() + 60000).toISOString()
      ); // 1 minute after

    if (error) {
      throw new Error(`Mevcut randevu kontrolü yapılamadı: ${error.message}`);
    }

    // Find exact match
    const exactMatch = existingAppointments?.find(apt => {
      const aptDate = new Date(apt.appointment_date);
      return (
        Math.abs(aptDate.getTime() - appointmentDateTime.getTime()) < 60000
      ); // 1 minute tolerance
    });

    if (!exactMatch) {
      return null;
    }

    // Check if there's space for more participants
    const currentParticipants = exactMatch.participants?.[0]?.count || 0;
    if (currentParticipants >= exactMatch.max_participants) {
      throw new Error('Bu saatteki grup randevusu dolu');
    }

    return exactMatch;
  });
}

/**
 * Auto appointment creation - automatically join existing or create new appointments
 */
export async function autoCreateAppointments(
  membershipPackageId: string,
  appointments: Array<{
    trainerId: string;
    appointmentDate: string;
    gymId: string;
  }>
): Promise<
  ApiResponse<{
    appointmentIds: string[];
    joined_count: number;
    created_count: number;
    details: Array<{
      date: string;
      action: 'joined' | 'created';
      appointmentId: string;
    }>;
  }>
> {
  return createAction<{
    appointmentIds: string[];
    joined_count: number;
    created_count: number;
    details: Array<{
      date: string;
      action: 'joined' | 'created';
      appointmentId: string;
    }>;
  }>(
    async (_, supabase) => {
      if (!appointments || appointments.length === 0) {
        throw new Error('En az bir randevu bilgisi gereklidir');
      }

      // Get package info
      const { data: packageData, error: packageError } = await supabase
        .from('gym_membership_packages')
        .select(
          `
          *,
          gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(
            *
          )
        `
        )
        .eq('id', membershipPackageId)
        .single();

      if (packageError || !packageData) {
        throw new Error('Paket bilgileri alınamadı');
      }

      // Check if member has enough remaining sessions (view-based)
      const remainingMap = await getComputedRemainingForPackages(supabase, [
        membershipPackageId,
      ]);
      const computedRemaining =
        remainingMap.get(membershipPackageId) ?? packageData.remaining_sessions;
      if (computedRemaining < appointments.length) {
        throw new Error(
          `Yetersiz seans. Kalan seans: ${computedRemaining}, İstenen: ${appointments.length}`
        );
      }

      const results: Array<{
        date: string;
        action: 'joined' | 'created';
        appointmentId: string;
      }> = [];
      const appointmentIds: string[] = [];
      let joinedCount = 0;
      let createdCount = 0;

      // Process each appointment
      for (const apt of appointments) {
        const appointmentDateTime = new Date(apt.appointmentDate);

        // Check if there's an existing group appointment at this time
        const { data: existingAppointments, error: searchError } =
          await supabase
            .from('appointments')
            .select(
              `
            id,
            max_participants,
            gym_package:gym_packages!appointments_gym_package_id_fkey(
              package_type
            ),
            appointment_participants!inner(
              id,
              status,
              gym_membership_package_id,
              membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
                membership:gym_memberships!gym_membership_packages_membership_id_fkey(
                  profile:profiles!gym_memberships_profile_id_fkey(
                    id,
                    full_name
                  )
                )
              )
            )
          `
            )
            .eq('trainer_profile_id', apt.trainerId)
            .eq('appointment_date', appointmentDateTime.toISOString())
            .eq('status', 'scheduled')
            .eq(
              'gym_package.package_type',
              packageData.gym_package.package_type
            );

        if (searchError) {
          throw new Error(
            `Mevcut randevu arama hatası: ${searchError.message}`
          );
        }

        let appointmentId: string;
        let action: 'joined' | 'created';

        if (existingAppointments && existingAppointments.length > 0) {
          // Found existing appointment, try to join
          const existingAppointment = existingAppointments[0];
          const confirmedParticipants =
            existingAppointment.appointment_participants?.filter(
              p => p.status === 'confirmed'
            ) || [];

          // Check if member is already in this appointment
          const memberAlreadyJoined = confirmedParticipants.some(
            (p: any) =>
              p.membership_package?.membership?.profile?.id ===
              packageData.membership?.profile?.id
          );

          if (memberAlreadyJoined) {
            throw new Error(
              `${appointmentDateTime.toLocaleString('tr-TR')} tarihinde zaten randevunuz bulunmaktadır`
            );
          }

          // Check if there's space
          if (
            confirmedParticipants.length >= existingAppointment.max_participants
          ) {
            throw new Error(
              `${appointmentDateTime.toLocaleString('tr-TR')} tarihindeki randevu doludur`
            );
          }

          // Join existing appointment
          const participantFormData = new FormData();
          participantFormData.append('appointment_id', existingAppointment.id);
          participantFormData.append(
            'gym_membership_package_id',
            membershipPackageId
          );

          const joinResult =
            await addAppointmentParticipant(participantFormData);
          if (!joinResult.success) {
            throw new Error(`Randevuya katılım hatası: ${joinResult.error}`);
          }

          appointmentId = existingAppointment.id;
          action = 'joined';
          joinedCount++;
        } else {
          // No existing appointment, create new one
          const appointmentToCreate = {
            gym_id: apt.gymId,
            appointment_date: apt.appointmentDate,
            appointment_type: packageData.gym_package.package_type,
            max_participants: packageData.gym_package.max_participants || 1,
            trainer_profile_id: apt.trainerId,
            gym_package_id: packageData.gym_package.id,
          };

          const createResult = await createBulkAppointments([
            appointmentToCreate,
          ]);
          if (!createResult.success) {
            throw new Error(`Randevu oluşturma hatası: ${createResult.error}`);
          }

          // Add participant to created appointment
          const participantFormData = new FormData();
          participantFormData.append(
            'appointment_id',
            createResult.data!.ids[0]
          );
          participantFormData.append(
            'gym_membership_package_id',
            membershipPackageId
          );

          const participantResult =
            await addAppointmentParticipant(participantFormData);
          if (!participantResult.success) {
            throw new Error(
              `Katılımcı ekleme hatası: ${participantResult.error}`
            );
          }

          appointmentId = createResult.data!.ids[0];
          action = 'created';
          createdCount++;
        }

        appointmentIds.push(appointmentId);
        results.push({
          date: appointmentDateTime.toLocaleString('tr-TR'),
          action,
          appointmentId,
        });
      }

      return {
        appointmentIds,
        joined_count: joinedCount,
        created_count: createdCount,
        details: results,
      };
    },
    { requireAuth: true }
  );
}

/**
 * Quick bulk appointment creation - create multiple appointments for a member
 */
export async function quickCreateBulkAppointments(
  membershipPackageId: string,
  appointments: Array<{
    trainerId: string;
    appointmentDate: string;
    gymId: string;
  }>
): Promise<ApiResponse<{ appointmentIds: string[]; created_count: number }>> {
  return createAction<{ appointmentIds: string[]; created_count: number }>(
    async (_, supabase) => {
      if (!appointments || appointments.length === 0) {
        throw new Error('En az bir randevu bilgisi gereklidir');
      }

      // Get package info for appointment creation
      const { data: packageData, error: packageError } = await supabase
        .from('gym_membership_packages')
        .select(
          `
          *,
          gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(
            *
          )
        `
        )
        .eq('id', membershipPackageId)
        .single();

      if (packageError || !packageData) {
        throw new Error('Paket bilgileri alınamadı');
      }

      // Check if member has enough remaining sessions (view-based)
      const remainingMap = await getComputedRemainingForPackages(supabase, [
        membershipPackageId,
      ]);
      const computedRemaining =
        remainingMap.get(membershipPackageId) ?? packageData.remaining_sessions;
      if (computedRemaining < appointments.length) {
        throw new Error(
          `Yetersiz seans. Kalan seans: ${computedRemaining}, İstenen: ${appointments.length}`
        );
      }

      // Create bulk appointments
      const appointmentsToCreate = appointments.map(apt => ({
        gym_id: apt.gymId,
        appointment_date: apt.appointmentDate,
        appointment_type: packageData.gym_package.package_type,
        max_participants: packageData.gym_package.max_participants || 1,
        trainer_profile_id: apt.trainerId,
        gym_package_id: packageData.gym_package.id,
      }));

      const appointmentResult =
        await createBulkAppointments(appointmentsToCreate);

      if (!appointmentResult.success) {
        throw new Error(appointmentResult.error || 'Randevular oluşturulamadı');
      }

      // Add participant to all created appointments
      const participantResults = await Promise.all(
        appointmentResult.data!.ids.map(async appointmentId => {
          const participantFormData = new FormData();
          participantFormData.append('appointment_id', appointmentId);
          participantFormData.append(
            'gym_membership_package_id',
            membershipPackageId
          );

          return await addAppointmentParticipant(participantFormData);
        })
      );

      // Check if all participants were added successfully
      const failedParticipants = participantResults.filter(
        result => !result.success
      );
      if (failedParticipants.length > 0) {
        throw new Error(
          `Bazı randevulara katılımcı eklenemedi: ${failedParticipants.map(f => f.error).join(', ')}`
        );
      }

      return {
        appointmentIds: appointmentResult.data!.ids,
        created_count: appointmentResult.data!.created_count,
      };
    },
    { requireAuth: true }
  );
}

/**
 * Quick appointment creation - either join existing group or create new
 */
export async function quickCreateAppointment(
  type: 'join-group' | 'create-new',
  membershipPackageId: string,
  appointmentId?: string,
  trainerId?: string,
  appointmentDate?: string,
  gymId?: string
): Promise<ApiResponse<{ appointmentId: string; type: string }>> {
  return createAction<{ appointmentId: string; type: string }>(
    async (_, supabase) => {
      if (type === 'join-group') {
        if (!appointmentId) {
          throw new Error('Grup randevusuna katılım için randevu ID gerekli');
        }

        // Join existing group appointment
        const participantFormData = new FormData();
        participantFormData.append('appointment_id', appointmentId);
        participantFormData.append(
          'gym_membership_package_id',
          membershipPackageId
        );

        const result = await addAppointmentParticipant(participantFormData);

        if (!result.success) {
          throw new Error(result.error || 'Grup randevusuna katılım başarısız');
        }

        return {
          appointmentId,
          type: 'join-group',
        };
      } else {
        // Create new appointment
        if (!trainerId || !appointmentDate || !gymId) {
          throw new Error('Yeni randevu için tüm bilgiler gerekli');
        }

        // Get package info for appointment creation
        const { data: packageData, error: packageError } = await supabase
          .from('gym_membership_packages')
          .select(
            `
            *,
            gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(
              *
            )
          `
          )
          .eq('id', membershipPackageId)
          .single();

        if (packageError || !packageData) {
          throw new Error('Paket bilgileri alınamadı');
        }

        // Create appointment
        const appointmentFormData = new FormData();
        appointmentFormData.append('gym_id', gymId);
        appointmentFormData.append('appointment_date', appointmentDate);
        appointmentFormData.append(
          'appointment_type',
          packageData.gym_package.package_type
        );
        appointmentFormData.append(
          'max_participants',
          packageData.gym_package.max_participants?.toString() || '1'
        );
        appointmentFormData.append('trainer_profile_id', trainerId);
        appointmentFormData.append(
          'gym_package_id',
          packageData.gym_package.id
        );

        const appointmentResult = await createAppointment(
          appointmentFormData,
          gymId
        );

        if (!appointmentResult.success) {
          throw new Error(appointmentResult.error || 'Randevu oluşturulamadı');
        }

        // Add participant
        const participantFormData = new FormData();
        participantFormData.append(
          'appointment_id',
          appointmentResult.data!.id
        );
        participantFormData.append(
          'gym_membership_package_id',
          membershipPackageId
        );

        const participantResult =
          await addAppointmentParticipant(participantFormData);

        if (!participantResult.success) {
          throw new Error(participantResult.error || 'Katılımcı eklenemedi');
        }

        return {
          appointmentId: appointmentResult.data!.id,
          type: 'create-new',
        };
      }
    },
    { requireAuth: true }
  );
}

/**
 * Check if a member can join an existing group appointment
 */
export async function checkGroupAppointmentAvailability(
  appointmentId: string,
  membershipId: string
): Promise<ApiResponse<{ canJoin: boolean; reason?: string }>> {
  return createAction<{ canJoin: boolean; reason?: string }>(
    async (_, supabase) => {
      // Get appointment details with participants
      const { data: appointment, error: appointmentError } = await supabase
        .from('appointments')
        .select(
          `
          *,
          participants:appointment_participants(
            id,
            gym_membership_package_id,
            status,
            membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
              membership_id
            )
          )
        `
        )
        .eq('id', appointmentId)
        .single();

      if (appointmentError || !appointment) {
        throw new Error('Randevu bulunamadı');
      }

      // Check if member is already in this appointment
      const isAlreadyParticipant = appointment.participants?.some(
        (p: any) =>
          p.membership_package?.membership_id === membershipId &&
          p.status === 'confirmed'
      );

      if (isAlreadyParticipant) {
        return { canJoin: false, reason: 'Bu randevuya zaten katılıyorsunuz' };
      }

      // Check if appointment is full
      const currentParticipants =
        appointment.participants?.filter((p: any) => p.status === 'confirmed')
          .length || 0;

      if (currentParticipants >= appointment.max_participants) {
        return { canJoin: false, reason: 'Randevu dolu' };
      }

      // Check if appointment is in the future
      const appointmentDate = new Date(appointment.appointment_date);
      const now = new Date();

      if (appointmentDate <= now) {
        return { canJoin: false, reason: 'Geçmiş randevulara katılamazsınız' };
      }

      return { canJoin: true };
    },
    { requireAuth: true }
  );
}

/**
 * Create multiple appointments at once
 */
export async function createBulkAppointments(
  appointments: Array<{
    gym_id: string;
    appointment_date: string;
    appointment_type: string;
    max_participants: number;
    trainer_profile_id: string;
    gym_package_id: string;
    notes?: string;
  }>
): Promise<ApiResponse<{ ids: string[]; created_count: number }>> {
  if (!appointments || appointments.length === 0) {
    throw new Error('En az bir randevu bilgisi gereklidir');
  }

  const gymId = appointments[0].gym_id;

  return createAction<{
    ids: string[];
    created_count: number;
  }>(
    async (_, supabase, _userId) => {
      // Validate all appointments have the same gym_id
      const invalidGymIds = appointments.filter(apt => apt.gym_id !== gymId);
      if (invalidGymIds.length > 0) {
        throw new Error('Tüm randevular aynı salona ait olmalıdır');
      }

      // Validate all appointments
      for (const apt of appointments) {
        if (
          !apt.gym_id ||
          !apt.appointment_date ||
          !apt.appointment_type ||
          !apt.trainer_profile_id ||
          !apt.gym_package_id
        ) {
          throw new Error(
            'Tüm randevular için gerekli alanlar doldurulmalıdır'
          );
        }
      }

      // Check for conflicts for all appointments
      const conflictChecks = await Promise.all(
        appointments.map(async (apt, index) => {
          const appointmentDateTime = new Date(apt.appointment_date);

          // Get session duration from package type
          let sessionDuration = 60;
          if (apt.appointment_type === 'appointment_standard') {
            sessionDuration = 60;
          } else if (apt.appointment_type === 'appointment_vip') {
            sessionDuration = 60;
          }

          const availabilityResult = await checkTrainerAvailability(
            apt.trainer_profile_id,
            apt.appointment_date,
            sessionDuration,
            gymId
          );

          if (!availabilityResult.success) {
            throw new Error(
              `${index + 1}. randevu için antrenör müsaitlik kontrolü başarısız: ${availabilityResult.error}`
            );
          }

          const {
            available,
            conflictingAppointments,
            existingGroupAppointment,
          } = availabilityResult.data!;

          return {
            appointment: apt,
            index: index + 1,
            hasConflict: !available || !!existingGroupAppointment,
            conflictDate: appointmentDateTime.toLocaleString('tr-TR'),
            conflictingAppointments,
            existingGroupAppointment,
          };
        })
      );

      // Check for conflicts
      const conflicts = conflictChecks.filter(check => check.hasConflict);
      if (conflicts.length > 0) {
        const conflictMessages = conflicts
          .map(
            c =>
              `${c.index}. randevu (${c.conflictDate}): ${
                c.existingGroupAppointment
                  ? 'Bu saatte zaten bir randevu var'
                  : 'Antrenör bu saatte müsait değil'
              }`
          )
          .join('; ');
        throw new Error(`Çakışan randevular bulundu: ${conflictMessages}`);
      }

      // Create all appointments
      const appointmentsToInsert = appointments.map(apt => ({
        gym_id: apt.gym_id,
        appointment_date: new Date(apt.appointment_date).toISOString(),
        appointment_type: apt.appointment_type as any,
        max_participants: apt.max_participants,
        trainer_profile_id: apt.trainer_profile_id,
        gym_package_id: apt.gym_package_id,
        notes: apt.notes,
        status: 'scheduled' as any,
      }));

      const { data: createdAppointments, error } = await supabase
        .from('appointments')
        .insert(appointmentsToInsert)
        .select('id');

      if (error) {
        throw new Error(`Randevular oluşturulamadı: ${error.message}`);
      }

      return {
        ids: createdAppointments.map(apt => apt.id),
        created_count: createdAppointments.length,
      };
    },
    {
      requireAuth: true,
      revalidatePaths: [`/dashboard/gym/${gymId}/appointments`],
    }
  );
}

/**
 * Create a new appointment with conflict checking
 */
export async function createAppointment(
  formData: FormData,
  gymId: string
): Promise<ApiResponse<Appointments>> {
  // Validate form data
  const validation = await validateFormData(formData, createAppointmentSchema);
  if (validation.error) {
    throw new Error(validation.error);
  }

  const appointmentData = validation.data!;

  return createAction<Appointments>(
    async (_, supabase, _userId) => {
      // Get session duration from package type (default 60 minutes)
      let sessionDuration = 60;
      if (appointmentData.appointment_type === 'appointment_standard') {
        sessionDuration = 60; // Group sessions
      } else if (appointmentData.appointment_type === 'appointment_vip') {
        sessionDuration = 60; // VIP sessions
      }

      // Check trainer availability if trainer is specified
      if (appointmentData.trainer_profile_id) {
        const availabilityResult = await checkTrainerAvailability(
          appointmentData.trainer_profile_id,
          appointmentData.appointment_date,
          sessionDuration,
          gymId
        );

        if (!availabilityResult.success) {
          throw new Error(
            availabilityResult.error || 'Antrenör müsaitlik kontrolü başarısız'
          );
        }

        const { available, conflictingAppointments, existingGroupAppointment } =
          availabilityResult.data!;

        // If there's an existing group appointment, this function should not be called
        // The frontend should handle joining existing appointments separately
        if (existingGroupAppointment) {
          throw new Error(
            'Bu saatte zaten bir randevu var. Mevcut randevuya katılım için farklı bir işlem kullanın.'
          );
        }

        // If there are any conflicts, reject the appointment creation
        if (!available) {
          const conflictTimes = conflictingAppointments
            .map(apt => new Date(apt.appointment_date).toLocaleString('tr-TR'))
            .join(', ');
          throw new Error(
            `Antrenör bu saatte müsait değil. Çakışan randevular: ${conflictTimes}`
          );
        }
      }

      // Create new appointment
      const { data: appointment, error: appointmentError } = await supabase
        .from('appointments')
        .insert({
          ...appointmentData,
          status: 'scheduled' as AppointmentStatus,
        })
        .eq('gym_id', gymId)
        .select()
        .single();

      if (appointmentError) {
        throw new Error(`Randevu oluşturulamadı: ${appointmentError.message}`);
      }

      return appointment;
    },
    {
      revalidatePaths: ['/dashboard/manager/appointments'],
    }
  );
}

/**
 * Add participant to appointment
 */
export async function addAppointmentParticipant(
  formData: FormData
): Promise<ApiResponse<AppointmentParticipants>> {
  // Validate form data
  const validation = await validateFormData(
    formData,
    createAppointmentParticipantSchema
  );
  if (validation.error) {
    throw new Error(validation.error);
  }

  const participantData = validation.data!;

  // Get appointment to verify gym access
  const supabaseTemp = await createClient();
  const { data: appointment, error: appointmentError } = await supabaseTemp
    .from('appointments')
    .select('gym_id')
    .eq('id', participantData.appointment_id)
    .single();

  if (appointmentError || !appointment) {
    throw new Error('Randevu bulunamadı');
  }

  return createAction<AppointmentParticipants>(
    async (_, supabase, _userId) => {
      // Check if member package has remaining sessions (view-based)
      const { data: membershipPackage, error: packageError } = await supabase
        .from('gym_membership_packages')
        .select(
          `
          id,
          remaining_sessions,
          membership:gym_memberships!gym_membership_packages_membership_id_fkey(
            profile_id
          )
        `
        )
        .eq('id', participantData.gym_membership_package_id)
        .single();

      if (packageError || !membershipPackage) {
        throw new Error('Üyelik paketi bulunamadı');
      }

      const remainingMap = await getComputedRemainingForPackages(supabase, [
        membershipPackage.id,
      ]);
      const computedRemaining =
        remainingMap.get(membershipPackage.id) ??
        membershipPackage.remaining_sessions;
      if ((computedRemaining ?? 0) <= 0) {
        throw new Error('Bu pakette kalan seans bulunmuyor');
      }

      // Check if this member is already a participant in this appointment
      const { data: existingParticipant, error: participantCheckError } =
        await supabase
          .from('appointment_participants')
          .select(
            `
          id,
          membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
            membership:gym_memberships!gym_membership_packages_membership_id_fkey(
              profile_id
            )
          )
        `
          )
          .eq('appointment_id', participantData.appointment_id)
          .eq('status', 'confirmed');

      if (participantCheckError) {
        throw new Error('Katılımcı kontrolü yapılamadı');
      }

      // Check if the member is already in this appointment
      const memberProfileId = (membershipPackage.membership as any)?.profile_id;
      const isAlreadyParticipant = existingParticipant?.some(
        (p: any) =>
          p.membership_package?.membership?.profile_id === memberProfileId
      );

      if (isAlreadyParticipant) {
        throw new Error('Bu üye zaten bu randevuya katılıyor');
      }

      // Add participant
      const { data: participant, error: participantError } = await supabase
        .from('appointment_participants')
        .insert({
          ...participantData,
          status: 'confirmed' as ParticipantStatus,
        })
        .select()
        .single();

      if (participantError) {
        throw new Error(`Katılımcı eklenemedi: ${participantError.message}`);
      }

      return participant;
    },
    {
      requireAuth: true,
      revalidatePaths: ['/dashboard/manager/appointments'],
    }
  );
}

/**
 * Update appointment date/time (reschedule)
 */
export async function rescheduleAppointment(
  appointmentId: string,
  newAppointmentDateISO: string,
  gymId?: string
): Promise<ApiResponse<Appointments>> {
  // Basic validation
  if (!appointmentId || !newAppointmentDateISO) {
    return { success: false, error: 'Randevu ve tarih bilgisi gereklidir' };
  }

  return createAction<Appointments>(
    async (_, supabase, _userId) => {
      const { data: updated, error } = await supabase
        .from('appointments')
        .update({
          appointment_date: new Date(newAppointmentDateISO).toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', appointmentId)
        .select()
        .single();

      if (error) {
        throw new Error(`Randevu yeniden planlanamadı: ${error.message}`);
      }

      return updated;
    },
    {
      requireAuth: true,
      revalidatePaths: [
        '/dashboard/manager/appointments',
        '/dashboard/trainer/appointments',
        ...(gymId ? [`/dashboard/gym/${gymId}/appointments`] : []),
      ],
    }
  );
}

/**
 * Update appointment status
 */
export async function updateAppointmentStatus(
  formData: FormData
): Promise<ApiResponse<Appointments>> {
  // Validate form data
  const validation = await validateFormData(
    formData,
    updateAppointmentStatusSchema
  );
  if (validation.error) {
    throw new Error(validation.error);
  }

  const { appointment_id, status } = validation.data!;

  // Get appointment to verify gym access
  const supabaseTemp = await createClient();
  const { data: appointment, error: appointmentError } = await supabaseTemp
    .from('appointments')
    .select('gym_id')
    .eq('id', appointment_id)
    .single();

  if (appointmentError || !appointment) {
    throw new Error('Randevu bulunamadı');
  }

  return createAction<Appointments>(
    async (_, supabase, _userId) => {
      // Update appointment status
      const { data: updatedAppointment, error: updateError } = await supabase
        .from('appointments')
        .update({ status })
        .eq('id', appointment_id)
        .select(
          `
          *,
          trainer_profile:profiles!appointments_trainer_profile_id_fkey(id, full_name),
          gym:gyms!appointments_gym_id_fkey(id, name)
        `
        )
        .single();

      if (updateError) {
        throw new Error(
          `Randevu durumu güncellenemedi: ${updateError.message}`
        );
      }

      // If appointment cancelled => cancel all participants
      if (status === 'cancelled') {
        const { data: cancelledParts, error: partErr } = await supabase
          .from('appointment_participants')
          .update({ status: 'cancelled' as ParticipantStatus })
          .eq('appointment_id', appointment_id).select(`
            id,
            membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
              membership:gym_memberships!gym_membership_packages_membership_id_fkey(profile_id),
              gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(name)
            )
          `);
        if (partErr) {
          // Log but don't hard fail the appointment update
          console.error('Katılımcılar iptal edilirken hata:', partErr);
        } else {
          try {
            const aptDate = (updatedAppointment as any)?.appointment_date as
              | string
              | undefined;
            const gymId = (updatedAppointment as any)?.gym_id as
              | string
              | undefined;
            const gymName = (updatedAppointment as any)?.gym?.name as
              | string
              | undefined;
            const trainerName = (updatedAppointment as any)?.trainer_profile
              ?.full_name as string | undefined;

            for (const p of cancelledParts || []) {
              const recipientUserId = (p as any)?.membership_package?.membership
                ?.profile_id as string | undefined;
              const packageName = (p as any)?.membership_package?.gym_package
                ?.name as string | undefined;
              if (!recipientUserId) continue;

              const parts: string[] = [];
              if (aptDate)
                parts.push(new Date(aptDate).toLocaleString('tr-TR'));
              if (gymName) parts.push(`Salon: ${gymName}`);
              if (trainerName) parts.push(`Antrenör: ${trainerName}`);
              if (packageName) parts.push(`Paket: ${packageName}`);
              const detail = parts.length ? ` (${parts.join(' • ')})` : '';

              await createNotification({
                recipientUserId,
                gymId,
                title: 'Randevu İptali',
                message: `Randevu iptal edildi${detail}`,
              });
            }
          } catch (e) {
            console.error('Randevu iptal bildirimleri gönderilirken hata:', e);
          }
        }
      }

      return updatedAppointment;
    },
    {
      requireAuth: true,
      revalidatePaths: ['/dashboard/manager/appointments'],
    }
  );
}

/**
 * Delete appointment permanently
 */
export async function deleteAppointment(
  appointmentId: string
): Promise<ApiResponse<void>> {
  // Get appointment to verify gym access
  const supabaseTemp = await createClient();
  const { data: appointment, error: appointmentError } = await supabaseTemp
    .from('appointments')
    .select('gym_id')
    .eq('id', appointmentId)
    .single();

  if (appointmentError || !appointment) {
    throw new Error('Randevu bulunamadı');
  }

  return createAction<void>(
    async (_, supabase, _userId) => {
      // First delete all participants
      const { error: participantsError } = await supabase
        .from('appointment_participants')
        .delete()
        .eq('appointment_id', appointmentId);

      if (participantsError) {
        throw new Error(
          `Randevu katılımcıları silinirken hata: ${participantsError.message}`
        );
      }

      // Then delete the appointment
      const { error: deleteError } = await supabase
        .from('appointments')
        .delete()
        .eq('id', appointmentId);

      if (deleteError) {
        throw new Error(`Randevu silinirken hata: ${deleteError.message}`);
      }

      return undefined;
    },
    {
      requireAuth: true,
      revalidatePaths: [
        '/dashboard/manager/appointments',
        '/dashboard/trainer/appointments',
        `/dashboard/gym/${appointment.gym_id}/appointments`,
      ],
    }
  );
}

/**
 * Cancel appointment
 */
export async function cancelAppointment(
  appointmentId: string,
  reason?: string
): Promise<ApiResponse<Appointments>> {
  // Get appointment to verify gym access
  const supabaseTemp = await createClient();
  const { data: appointment, error: appointmentError } = await supabaseTemp
    .from('appointments')
    .select('gym_id')
    .eq('id', appointmentId)
    .single();

  if (appointmentError || !appointment) {
    throw new Error('Randevu bulunamadı');
  }

  return createAction<Appointments>(
    async (_, supabase, _userId) => {
      // Cancel appointment
      const { data: cancelledAppointment, error: cancelError } = await supabase
        .from('appointments')
        .update({
          status: 'cancelled' as AppointmentStatus,
          notes: reason ? `İptal nedeni: ${reason}` : undefined,
        })
        .eq('id', appointmentId)
        .select(
          `
          *,
          trainer_profile:profiles!appointments_trainer_profile_id_fkey(id, full_name),
          gym:gyms!appointments_gym_id_fkey(id, name)
        `
        )
        .single();

      // Also cancel all participants of this appointment
      const { data: cancelledParts, error: partErr } = await supabase
        .from('appointment_participants')
        .update({ status: 'cancelled' as ParticipantStatus })
        .eq('appointment_id', appointmentId).select(`
          id,
          membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
            membership:gym_memberships!gym_membership_packages_membership_id_fkey(profile_id),
            gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(name)
          )
        `);
      if (partErr) {
        console.error('Katılımcılar iptal edilirken hata:', partErr);
      } else {
        try {
          const aptDate = (cancelledAppointment as any)?.appointment_date as
            | string
            | undefined;
          const gymId = (cancelledAppointment as any)?.gym_id as
            | string
            | undefined;
          const gymName = (cancelledAppointment as any)?.gym?.name as
            | string
            | undefined;
          const trainerName = (cancelledAppointment as any)?.trainer_profile
            ?.full_name as string | undefined;

          for (const p of cancelledParts || []) {
            const recipientUserId = (p as any)?.membership_package?.membership
              ?.profile_id as string | undefined;
            const packageName = (p as any)?.membership_package?.gym_package
              ?.name as string | undefined;
            if (!recipientUserId) continue;

            const parts: string[] = [];
            if (aptDate) parts.push(new Date(aptDate).toLocaleString('tr-TR'));
            if (gymName) parts.push(`Salon: ${gymName}`);
            if (trainerName) parts.push(`Antrenör: ${trainerName}`);
            if (packageName) parts.push(`Paket: ${packageName}`);
            const detail = parts.length ? ` (${parts.join(' • ')})` : '';

            await createNotification({
              recipientUserId,
              gymId,
              title: 'Randevu İptali',
              message: `Randevu iptal edildi${detail}`,
            });
          }
        } catch (e) {
          console.error('Randevu iptal bildirimleri gönderilirken hata:', e);
        }
      }

      if (cancelError) {
        throw new Error(`Randevu iptal edilemedi: ${cancelError.message}`);
      }

      return cancelledAppointment;
    },
    {
      requireAuth: true,
      revalidatePaths: ['/dashboard/manager/appointments'],
    }
  );
}

/**
 * Get appointment details with participants
 */
export async function getAppointmentDetails(
  appointmentId: string
): Promise<ApiResponse<AppointmentWithDetails>> {
  // First get appointment to find gym_id
  const supabaseTemp = await createClient();
  const { data: appointmentBasic, error: basicError } = await supabaseTemp
    .from('appointments')
    .select('gym_id')
    .eq('id', appointmentId)
    .single();

  if (basicError || !appointmentBasic) {
    throw new Error('Randevu bulunamadı');
  }

  return createAction<AppointmentWithDetails>(async (_, supabase, _userId) => {
    const { data: appointment, error } = await supabase
      .from('appointments')
      .select(
        `
          *,
          trainer_profile:profiles!appointments_trainer_profile_id_fkey(
            id,
            full_name,
            avatar_url
          ),
          participants:appointment_participants(
            *,
            membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
              *,
              membership:gym_memberships!gym_membership_packages_membership_id_fkey(
                *,
                profile:profiles!gym_memberships_profile_id_fkey(
                  id,
                  full_name,
                  avatar_url
                )
              ),
              gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(
                name,
                package_type,
                session_duration_minutes
              )
            )
          )
        `
      )
      .eq('id', appointmentId)
      .single();

    if (error) {
      throw new Error(`Randevu detayları getirilemedi: ${error.message}`);
    }

    if (!appointment) {
      throw new Error('Randevu bulunamadı');
    }

    // Gym access kontrolü otomatik olarak yapıldı
    return appointment as AppointmentWithDetails;
  });
}
