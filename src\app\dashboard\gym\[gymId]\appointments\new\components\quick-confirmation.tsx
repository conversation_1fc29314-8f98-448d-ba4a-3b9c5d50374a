'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  User,
  Calendar,
  Clock,
  Package,
  UserCheck,
  Loader2,
  CheckCircle2,
  Users,
} from 'lucide-react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import {
  quickCreateAppointment,
  quickCreateBulkAppointments,
  autoCreateAppointments,
} from '@/lib/actions/dashboard/company/appointment-actions';
import { toast } from 'sonner';

interface MemberInfo {
  id: string;
  full_name: string;
}

interface PackageInfo {
  id: string;
  name: string;
  package_type: string;
  remaining_sessions: number;
}

interface TrainerInfo {
  id: string;
  full_name: string;
}

interface AppointmentInfo {
  id?: string;
  date?: string;
  time?: string;
  type: 'join-group' | 'create-new' | 'create-bulk' | 'session-planner';
  existing_participants?: Array<{
    name: string;
  }>;
  max_participants?: number;
  bulk_appointments?: Array<{ date: string; time: string }>;
  planned_sessions?: Array<{ date: string; time: string }>;
}

interface QuickConfirmationProps {
  member: MemberInfo;
  package: PackageInfo;
  trainer: TrainerInfo;
  appointment: AppointmentInfo;
  gymId: string;
  onSuccess: (appointmentId: string) => void;
  onBack: () => void;
}

export function QuickConfirmation({
  member,
  package: pkg,
  trainer,
  appointment,
  gymId,
  onSuccess,
  onBack,
}: QuickConfirmationProps) {
  const [loading, setLoading] = useState(false);

  const handleConfirm = async () => {
    setLoading(true);

    try {
      if (
        appointment.type === 'session-planner' &&
        appointment.planned_sessions
      ) {
        // Session planner - auto create appointments
        const plannedAppointments = appointment.planned_sessions.map(
          session => ({
            trainerId: trainer.id,
            appointmentDate: session.time, // session.time is already a full ISO datetime string
            gymId: gymId,
          })
        );

        const result = await autoCreateAppointments(
          pkg.id,
          plannedAppointments
        );

        if (result.success) {
          const { joined_count, created_count } = result.data!;
          toast.success(
            `${joined_count} randevuya katıldınız, ${created_count} yeni randevu oluşturuldu!`
          );
          onSuccess(result.data!.appointmentIds[0]); // Return first appointment ID
        } else {
          toast.error(result.error || 'Bir hata oluştu');
        }
      } else if (
        appointment.type === 'create-bulk' &&
        appointment.bulk_appointments
      ) {
        // Bulk appointment creation
        const bulkAppointments = appointment.bulk_appointments.map(apt => ({
          trainerId: trainer.id,
          appointmentDate: apt.time,
          gymId: gymId,
        }));

        const result = await quickCreateBulkAppointments(
          pkg.id,
          bulkAppointments
        );

        if (result.success) {
          toast.success(
            `${result.data!.created_count} randevu başarıyla oluşturuldu!`
          );
          onSuccess(result.data!.appointmentIds[0]); // Return first appointment ID
        } else {
          toast.error(result.error || 'Bir hata oluştu');
        }
      } else {
        // Single appointment creation
        const result = await quickCreateAppointment(
          appointment.type as 'join-group' | 'create-new',
          pkg.id,
          appointment.id,
          trainer.id,
          appointment.type === 'create-new' ? appointment.time : undefined,
          appointment.type === 'create-new' ? gymId : undefined
        );

        if (result.success) {
          toast.success(
            appointment.type === 'join-group'
              ? 'Grup randevusuna başarıyla katıldınız!'
              : 'Randevu başarıyla oluşturuldu!'
          );
          onSuccess(result.data!.appointmentId);
        } else {
          toast.error(result.error || 'Bir hata oluştu');
        }
      }
    } catch (error: any) {
      toast.error(error.message || 'Bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Randevu Onayı</h2>
        <p className="text-muted-foreground">
          Bilgileri kontrol edin ve randevuyu onaylayın
        </p>
      </div>

      <div className="grid gap-6">
        {/* Üye Bilgileri */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Üye Bilgileri
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarFallback className="text-lg">
                  {getInitials(member.full_name)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-semibold">{member.full_name}</h3>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Paket Bilgileri */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Paket Bilgileri
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">{pkg.name}</h3>
                <p className="text-muted-foreground text-sm">
                  {pkg.package_type === 'personal'
                    ? 'Kişisel Paket'
                    : 'Grup Paketi'}
                </p>
              </div>
              <Badge variant="outline">
                {appointment.type === 'session-planner'
                  ? `${pkg.remaining_sessions - (appointment.planned_sessions?.length || 1)} seans kalacak`
                  : appointment.type === 'create-bulk'
                    ? `${pkg.remaining_sessions - (appointment.bulk_appointments?.length || 1)} seans kalacak`
                    : `${pkg.remaining_sessions - 1} seans kalacak`}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Antrenör Bilgileri */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserCheck className="h-5 w-5" />
              Antrenör Bilgileri
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarFallback className="text-lg">
                  {getInitials(trainer.full_name)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-semibold">{trainer.full_name}</h3>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Randevu Detayları */}
        <Card className="border-primary/20 bg-primary/5">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Randevu Detayları
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {appointment.type === 'session-planner' ? (
              <>
                <div className="max-h-40 space-y-2 overflow-y-auto">
                  {appointment.planned_sessions?.map((session, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 rounded bg-white/50 p-2"
                    >
                      <Calendar className="text-muted-foreground h-4 w-4" />
                      <span className="font-medium">
                        Seans {index + 1}:{' '}
                        {format(
                          new Date(session.time),
                          'dd MMMM yyyy, EEEE HH:mm',
                          { locale: tr }
                        )}
                      </span>
                    </div>
                  ))}
                </div>
              </>
            ) : appointment.type === 'create-bulk' ? (
              <>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">
                    Çoklu Randevu ({appointment.bulk_appointments?.length})
                  </Badge>
                </div>

                <div className="max-h-40 space-y-2 overflow-y-auto">
                  {appointment.bulk_appointments?.map((apt, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 rounded bg-white/50 p-2"
                    >
                      <Calendar className="text-muted-foreground h-4 w-4" />
                      <span className="font-medium">
                        {format(
                          new Date(apt.time),
                          'dd MMMM yyyy, EEEE HH:mm',
                          { locale: tr }
                        )}
                      </span>
                    </div>
                  ))}
                </div>

                <div className="text-muted-foreground flex items-center gap-2 text-sm">
                  <Users className="h-4 w-4" />
                  <span>
                    Toplam {appointment.bulk_appointments?.length} randevu
                    oluşturulacak
                  </span>
                </div>
              </>
            ) : appointment.type === 'join-group' ? (
              <>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Grup Randevusuna Katılım</Badge>
                </div>

                {appointment.date && (
                  <div className="flex items-center gap-2">
                    <Calendar className="text-muted-foreground h-4 w-4" />
                    <span className="font-medium">
                      {format(
                        new Date(appointment.date),
                        'dd MMMM yyyy, EEEE',
                        { locale: tr }
                      )}
                    </span>
                  </div>
                )}

                {appointment.time && (
                  <div className="flex items-center gap-2">
                    <Clock className="text-muted-foreground h-4 w-4" />
                    <span className="font-medium">
                      {format(new Date(appointment.time), 'HH:mm')}
                    </span>
                  </div>
                )}

                {appointment.existing_participants && (
                  <div>
                    <div className="mb-2 flex items-center gap-2">
                      <Users className="text-muted-foreground h-4 w-4" />
                      <span className="text-sm font-medium">
                        Mevcut Katılımcılar (
                        {appointment.existing_participants.length + 1}/
                        {appointment.max_participants})
                      </span>
                    </div>
                    <div className="space-y-1 pl-6">
                      {appointment.existing_participants.map(
                        (participant, index) => (
                          <p
                            key={index}
                            className="text-muted-foreground text-sm"
                          >
                            • {participant.name}
                          </p>
                        )
                      )}
                      <p className="text-primary text-sm font-medium">
                        • {member.full_name} (Katılacak)
                      </p>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">Yeni Randevu</Badge>
                </div>

                {appointment.date && appointment.time && (
                  <>
                    <div className="flex items-center gap-2">
                      <Calendar className="text-muted-foreground h-4 w-4" />
                      <span className="font-medium">
                        {format(
                          new Date(appointment.time),
                          'dd MMMM yyyy, EEEE',
                          { locale: tr }
                        )}
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <Clock className="text-muted-foreground h-4 w-4" />
                      <span className="font-medium">
                        {format(new Date(appointment.time), 'HH:mm')}
                      </span>
                    </div>
                  </>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Onay Butonu */}
        <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <CardContent className="pt-6">
            <div className="mb-4 flex items-center gap-3">
              <CheckCircle2 className="h-6 w-6 text-green-600" />
              <div>
                <h3 className="font-semibold text-green-700 dark:text-green-300">
                  Randevu Oluşturulmaya Hazır
                </h3>
                <p className="text-sm text-green-600 dark:text-green-400">
                  Onaylamak için &quot;Randevuyu Oluştur&quot; butonuna tıklayın
                </p>
              </div>
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={onBack}
                disabled={loading}
                className="flex-1"
              >
                Geri Dön
              </Button>

              <Button
                onClick={handleConfirm}
                disabled={loading}
                className="flex-1"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Oluşturuluyor...
                  </>
                ) : (
                  <>
                    <CheckCircle2 className="mr-2 h-4 w-4" />
                    Randevuyu Oluştur
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
