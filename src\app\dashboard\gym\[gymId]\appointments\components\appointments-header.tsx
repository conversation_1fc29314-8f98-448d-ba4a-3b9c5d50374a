'use client';

import { Plus } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { AppointmentsViewSwitcher } from '@/components/dashboard/appointments/appointments-view-switcher';

interface AppointmentsHeaderProps {
  gymId: string;
}

export function AppointmentsHeader({ gymId }: AppointmentsHeaderProps) {
  return (
    <div className="flex flex-col gap-4 sm:flex-row">
      <div className="bg-background/70 supports-[backdrop-filter]:bg-background/50 z-10 w-full py-4 backdrop-blur">
        <AppointmentsViewSwitcher />
      </div>{' '}
      <Button asChild>
        <Link href={`/dashboard/gym/${gymId}/appointments/new`}>
          <Plus className="mr-2 h-4 w-4" />
          Rand<PERSON><PERSON>
        </Link>
      </Button>
    </div>
  );
}
