import { InviteTrainerButton } from './components/invite-trainer-button';
import { UnifiedTrainerCard } from './components/unified-trainer-card';
import { Users, Shield } from 'lucide-react';
import { getGymTrainersWithPermissions } from '@/lib/actions/all-actions';
import { getGymCapacityUsage } from '@/lib/actions/business/subscription-actions';
import { CapacityNotice } from '@/components/dashboard/common/capacity-notice';
import { getDashboardContext } from '@/lib/actions/auth/header-auth-actions';
import { UnauthorizedAccess } from '@/components/dashboard/common/UnauthorizedAccess';

interface ManagerTrainersPageProps {
  params: Promise<{
    gymId: string;
  }>;
}

// Unified Trainer Management Component
async function UnifiedTrainerManagement({ gymId }: { gymId: string }) {
  try {
    const result = await getGymTrainersWithPermissions(gymId);

    if (!result.success) {
      return (
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="space-y-4 text-center">
            <h2 className="text-xl font-semibold">
              Antrenör Bilgileri Yüklenemedi
            </h2>
            <p className="text-muted-foreground">{result.error}</p>
          </div>
        </div>
      );
    }

    const trainers = result.data || [];
    const activeTrainers = trainers.filter(t => t.status === 'active');

    if (activeTrainers.length === 0) {
      return (
        <div className="flex min-h-[500px] items-center justify-center">
          <div className="max-w-md space-y-6 text-center">
            <div className="relative">
              <div className="bg-muted mx-auto flex h-20 w-20 items-center justify-center rounded-full">
                <Users className="text-muted-foreground h-10 w-10" />
              </div>
              <div className="bg-primary absolute -right-1 -bottom-1 flex h-6 w-6 items-center justify-center rounded-full">
                <Shield className="text-primary-foreground h-3 w-3" />
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-xl font-semibold">
                Henüz Antrenör Bulunmuyor
              </h3>
              <p className="text-muted-foreground">
                Bu salonda henüz aktif antrenör bulunmuyor. Antrenör davet
                ederek başlayabilirsiniz.
              </p>
            </div>

            <InviteTrainerButton />
          </div>
        </div>
      );
    }

    return (
      <div className="grid gap-6">
        {activeTrainers.map(trainer => (
          <UnifiedTrainerCard
            key={trainer.id}
            trainer={trainer}
            gymId={gymId}
          />
        ))}
      </div>
    );
  } catch (error) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="space-y-4 text-center">
          <h2 className="text-xl font-semibold">Hata Oluştu</h2>
          <p className="text-muted-foreground">
            {error instanceof Error ? error.message : 'Bilinmeyen hata'}
          </p>
        </div>
      </div>
    );
  }
}

// Main Page Component
export default async function ManagerTrainersPage({
  params,
}: ManagerTrainersPageProps) {
  const { gymId } = await params;

  // Dashboard context üzerinden yetki kontrolü
  const ctx = await getDashboardContext(gymId);
  if (!ctx.gymAccess?.hasAccess) {
    return (
      <UnauthorizedAccess reason="Bu salona erişim yetkiniz bulunmuyor." />
    );
  }
  if (ctx.gymAccess?.restrictedPages?.includes('trainers')) {
    return (
      <UnauthorizedAccess reason="Bu salonun antrenörlerini yönetme yetkiniz bulunmuyor." />
    );
  }

  const capacityResult = await getGymCapacityUsage(gymId);
  const capacity = capacityResult.success ? capacityResult.data : null;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            Antrenör Yönetimi
          </h1>
          <p className="text-muted-foreground">
            Antrenörleri yönetin ve yetkilerini kontrol edin
          </p>
          {capacity && (
            <CapacityNotice
              label="Antrenörler"
              count={capacity.trainers.count}
              max={capacity.trainers.max}
            />
          )}
        </div>
        <InviteTrainerButton />
      </div>
      <UnifiedTrainerManagement gymId={gymId} />
    </div>
  );
}
