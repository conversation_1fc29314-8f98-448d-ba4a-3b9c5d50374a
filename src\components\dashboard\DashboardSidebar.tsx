'use client';

import {
  getSidebarNavigation,
  NavigationItem,
  SidebarMode,
} from '@/lib/constants';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useState } from 'react';

function NavItem({
  href,
  title,
  icon,
  isActive,
  isExpanded,
}: NavigationItem & { isExpanded: boolean }) {
  return (
    <Link
      href={href}
      className={cn(
        'group/item relative flex w-full items-center rounded-xl text-base font-semibold transition-all duration-200',

        isActive
          ? 'from-primary/10 to-primary/5 text-primary ring-primary/20 cursor-default bg-gradient-to-r shadow-sm ring-1'
          : 'text-foreground/80 hover:bg-sidebar-border hover:text-foreground'
      )}
    >
      <div className="flex items-center justify-center rounded-lg p-2 transition-all duration-200 size-8">
        {icon}
      </div>

      <span
        className={cn(
          'ml-1 min-w-0 flex-1 truncate font-medium transition-all duration-200',
          isExpanded ? 'opacity-100' : 'w-0 opacity-0'
        )}
      >
        {title}
      </span>
    </Link>
  );
}
export interface SidebarProps {
  mode: SidebarMode;
  gymId?: string;
}

export function DashboardSidebar({ mode, gymId }: SidebarProps) {
  const pathname = usePathname();
  const [isExpanded, setIsExpanded] = useState(false);

  // En spesifik eşleşen path'i bul (sadece bir kez hesapla)
  const activeHref = React.useMemo(() => {
    const allItems = getSidebarNavigation(mode, gymId);
    const matchingItems = allItems
      .filter(item => pathname.startsWith(item.href))
      .sort((a, b) => b.href.length - a.href.length);

    return matchingItems[0]?.href || null;
  }, [pathname, mode, gymId]);

  const isActive = (href: string) => {
    return activeHref === href;
  };

  const navigationItems = getSidebarNavigation(mode, gymId);

  // Tüm modlar için modern tasarım - Mobilde gizli
  return (
    <div
      className={cn(
        'border-border bg-background/80 group fixed top-12 left-0 z-30 hidden h-[calc(100vh-3.5rem)] flex-col border-r backdrop-blur-xl transition-all duration-300 md:flex',
        isExpanded ? 'w-60' : 'w-12'
      )}
      onMouseEnter={() => setIsExpanded(true)}
      onMouseLeave={() => setIsExpanded(false)}
    >
      {/* Navigation */}
      <nav className="flex-1 space-y-1 overflow-y-hidden scroll-smooth px-2 py-3 group-hover:overflow-y-auto">
        {navigationItems.map(item => (
          <NavItem
            key={item.href}
            href={item.href}
            title={item.title}
            icon={item.icon}
            isActive={isActive(item.href)}
            isExpanded={isExpanded}
          />
        ))}
      </nav>
    </div>
  );
}
