'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import {
  Clock,
  Sun,
  Sunset,
  Moon,
  Loader2,
  CheckCircle2,
  XCircle,
} from 'lucide-react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { getAvailableTimeSlots } from '@/lib/actions/dashboard/company/appointment-actions';

interface TimeSlot {
  time: string;
  display_time: string;
  available: boolean;
  period: 'morning' | 'afternoon' | 'evening';
}

interface SmartTimeSelectionProps {
  trainerId: string;
  gymId: string;
  sessionDurationMinutes: number;
  onTimeSelect: (date: string, time: string) => void;
  onBulkTimeSelect?: (
    appointments: Array<{ date: string; time: string }>
  ) => void;
  onAutoTimeSelect?: (
    appointments: Array<{ date: string; time: string }>
  ) => void;
  selectedDate?: Date;
  selectedTime?: string;
  allowBulkSelection?: boolean;
  allowAutoMode?: boolean;
}

export function SmartTimeSelection({
  trainerId,
  gymId,
  sessionDurationMinutes,
  onTimeSelect,
  onBulkTimeSelect,
  onAutoTimeSelect,
  selectedDate,
  selectedTime,
  allowBulkSelection = false,
  allowAutoMode = false,
}: SmartTimeSelectionProps) {
  const [date, setDate] = useState<Date | undefined>(
    selectedDate || new Date()
  );
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(false);
  const [bulkMode, setBulkMode] = useState(false);
  const [autoMode, setAutoMode] = useState(false);
  const [selectedTimes, setSelectedTimes] = useState<
    Array<{ date: string; time: string }>
  >([]);

  // Load available time slots when date changes
  useEffect(() => {
    if (!date) return;

    const loadTimeSlots = async () => {
      setLoading(true);
      try {
        const result = await getAvailableTimeSlots(
          trainerId,
          date.toISOString(),
          sessionDurationMinutes,
          gymId
        );

        if (result.success) {
          setTimeSlots(result.data || []);
        }
      } catch (error) {
        console.error('Error loading time slots:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTimeSlots();
  }, [date, trainerId, gymId, sessionDurationMinutes]);

  const handleTimeSelect = (timeSlot: TimeSlot) => {
    if (!timeSlot.available || !date) return;

    const selectedDateTime = new Date(timeSlot.time);

    if (bulkMode && allowBulkSelection) {
      const newSelection = {
        date: date.toISOString(),
        time: selectedDateTime.toISOString(),
      };

      // Check if already selected
      const isAlreadySelected = selectedTimes.some(
        st => st.date === newSelection.date && st.time === newSelection.time
      );

      if (isAlreadySelected) {
        // Remove from selection
        setSelectedTimes(prev =>
          prev.filter(
            st =>
              !(st.date === newSelection.date && st.time === newSelection.time)
          )
        );
      } else {
        // Add to selection
        setSelectedTimes(prev => [...prev, newSelection]);
      }
    } else {
      onTimeSelect(date.toISOString(), selectedDateTime.toISOString());
    }
  };

  const handleBulkConfirm = () => {
    if (selectedTimes.length > 0) {
      if (autoMode && onAutoTimeSelect) {
        onAutoTimeSelect(selectedTimes);
      } else if (onBulkTimeSelect) {
        onBulkTimeSelect(selectedTimes);
      }
    }
  };

  const isTimeSelected = (timeSlot: TimeSlot) => {
    if (!bulkMode || !date) return selectedTime === timeSlot.time;

    return selectedTimes.some(
      st => st.date === date.toISOString() && st.time === timeSlot.time
    );
  };

  const getPeriodIcon = (period: string) => {
    switch (period) {
      case 'morning':
        return <Sun className="h-4 w-4" />;
      case 'afternoon':
        return <Sunset className="h-4 w-4" />;
      case 'evening':
        return <Moon className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getPeriodLabel = (period: string) => {
    switch (period) {
      case 'morning':
        return 'Sabah';
      case 'afternoon':
        return 'Öğlen';
      case 'evening':
        return 'Akşam';
      default:
        return '';
    }
  };

  const groupedTimeSlots = timeSlots.reduce(
    (acc, slot) => {
      if (!acc[slot.period]) {
        acc[slot.period] = [];
      }
      acc[slot.period].push(slot);
      return acc;
    },
    {} as Record<string, TimeSlot[]>
  );

  return (
    <div className="space-y-6">
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Tarih ve Saat Seçimi</h2>
            <p className="text-muted-foreground">
              Randevu için uygun tarih ve saati seçin
            </p>
          </div>

          {(allowBulkSelection || allowAutoMode) && (
            <div className="flex items-center gap-3">
              {allowBulkSelection && (
                <label className="flex cursor-pointer items-center gap-2">
                  <input
                    type="checkbox"
                    checked={bulkMode && !autoMode}
                    onChange={e => {
                      setBulkMode(e.target.checked);
                      setAutoMode(false);
                      if (!e.target.checked) {
                        setSelectedTimes([]);
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm font-medium">Çoklu Seçim</span>
                </label>
              )}

              {allowAutoMode && (
                <label className="flex cursor-pointer items-center gap-2">
                  <input
                    type="checkbox"
                    checked={autoMode}
                    onChange={e => {
                      setAutoMode(e.target.checked);
                      setBulkMode(e.target.checked);
                      if (!e.target.checked) {
                        setSelectedTimes([]);
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm font-medium">🤖 Otomatik Mod</span>
                </label>
              )}

              {(bulkMode || autoMode) && selectedTimes.length > 0 && (
                <Button onClick={handleBulkConfirm} size="sm">
                  {autoMode
                    ? `🤖 ${selectedTimes.length} Otomatik Randevu`
                    : `${selectedTimes.length} Randevu Seç`}
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Takvim */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Tarih Seçin
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={date}
              onSelect={setDate}
              disabled={date =>
                date < new Date() ||
                date > new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
              }
              locale={tr}
              className="rounded-md border"
            />
          </CardContent>
        </Card>

        {/* Saat Seçimi */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Saat Seçin
              {date && (
                <Badge variant="outline">
                  {format(date, 'dd MMMM yyyy', { locale: tr })}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="mr-2 h-6 w-6 animate-spin" />
                Müsait saatler yükleniyor...
              </div>
            ) : !date ? (
              <p className="text-muted-foreground py-8 text-center">
                Önce bir tarih seçin
              </p>
            ) : (
              <div className="space-y-4">
                {Object.entries(groupedTimeSlots).map(([period, slots]) => (
                  <div key={period}>
                    <div className="mb-3 flex items-center gap-2">
                      {getPeriodIcon(period)}
                      <h4 className="font-medium">{getPeriodLabel(period)}</h4>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      {slots.map(slot => (
                        <Button
                          key={slot.time}
                          variant={
                            isTimeSelected(slot)
                              ? 'default'
                              : slot.available
                                ? 'outline'
                                : 'secondary'
                          }
                          size="sm"
                          onClick={() => handleTimeSelect(slot)}
                          disabled={!slot.available}
                          className={`relative ${
                            bulkMode && isTimeSelected(slot)
                              ? 'ring-primary ring-2'
                              : ''
                          }`}
                        >
                          <div className="flex items-center gap-1">
                            {slot.available ? (
                              <CheckCircle2 className="h-3 w-3" />
                            ) : (
                              <XCircle className="h-3 w-3" />
                            )}
                            {slot.display_time}
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>
                ))}

                {Object.keys(groupedTimeSlots).length === 0 && (
                  <p className="text-muted-foreground py-4 text-center">
                    Bu tarihte müsait saat bulunmuyor
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Single selection display */}
      {!bulkMode && selectedTime && date && (
        <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
              <CheckCircle2 className="h-5 w-5" />
              <span className="font-medium">
                Seçilen Randevu: {format(date, 'dd MMMM yyyy', { locale: tr })}{' '}
                - {format(new Date(selectedTime), 'HH:mm')}
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bulk selection display */}
      {bulkMode && selectedTimes.length > 0 && (
        <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
              <CheckCircle2 className="h-5 w-5" />
              Seçilen Randevular ({selectedTimes.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="max-h-32 space-y-2 overflow-y-auto">
              {selectedTimes.map((appointment, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between text-sm"
                >
                  <span className="text-blue-700 dark:text-blue-300">
                    {format(new Date(appointment.date), 'dd MMM yyyy', {
                      locale: tr,
                    })}{' '}
                    - {format(new Date(appointment.time), 'HH:mm')}
                  </span>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => {
                      setSelectedTimes(prev =>
                        prev.filter((_, i) => i !== index)
                      );
                    }}
                    className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800"
                  >
                    ×
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
