import { fetchAllPlatformPackages } from '@/lib/actions/business/platform-packages';
import {
  getManagerDetail,
  getManagerUsageStats,
} from '@/lib/actions/user/manager-actions';
import { ModernBillingClient } from './modern-billing-client';

export const dynamic = 'force-dynamic';

export default async function BillingPage() {
  const [packagesRes, managerRes, usageRes] = await Promise.all([
    fetchAllPlatformPackages(),
    getManagerDetail(),
    getManagerUsageStats(),
  ]);

  if (!packagesRes.success) {
    throw new Error(packagesRes.error || 'Paketler yüklenemedi');
  }

  if (!managerRes.success) {
    throw new Error(managerRes.error || 'Şirket bilgileri alınamadı');
  }

  const packages = packagesRes.data ?? [];
  const managerDetails = managerRes.data;
  const companyActive = managerDetails?.company?.status === 'active';
  const currentPackageId = managerDetails?.package?.id || null;
  const maxGyms = managerDetails?.package?.max_gyms ?? null;
  const maxMembers = managerDetails?.package?.max_members ?? null;
  const maxTrainers = managerDetails?.package?.max_trainers ?? null;
  const maxStaff = managerDetails?.package?.max_staff ?? null;
  const maxMonthlyAppointments = managerDetails?.package?.max_monthly_appointments ?? null;
  const trialEndsAt = managerDetails?.company?.trial_ends_at || null;

  const usageData = usageRes.success && usageRes.data ? usageRes.data : { 
    gymsUsed: 0, 
    membersUsed: 0, 
    trainersUsed: 0,
    staffUsed: 0,
    monthlyAppointmentsUsed: 0,
    daysRemaining: 0 
  };

  return (
    <ModernBillingClient
      packages={packages}
      companyActive={companyActive}
      currentPackageId={currentPackageId}
      maxGyms={maxGyms}
      maxMembers={maxMembers}
      maxTrainers={maxTrainers}
      maxStaff={maxStaff}
      maxMonthlyAppointments={maxMonthlyAppointments}
      usageGyms={usageData.gymsUsed}
      usageMembers={usageData.membersUsed}
      usageTrainers={usageData.trainersUsed}
      usageStaff={usageData.staffUsed}
      usageMonthlyAppointments={usageData.monthlyAppointmentsUsed}
      daysRemaining={usageData.daysRemaining}
      trialEndsAt={trialEndsAt}
    />
  );
}
