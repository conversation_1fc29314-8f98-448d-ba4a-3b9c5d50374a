'use client';

import { forwardRef, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ModernAuthInputProps {
  name: string;
  type: string;
  label: string;
  placeholder: string;
  required?: boolean;
  error?: string;
  className?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export const ModernAuthInput = forwardRef<
  HTMLInputElement,
  ModernAuthInputProps
>(
  (
    {
      name,
      type,
      label,
      placeholder,
      required,
      error,
      className = '',
      value,
      onChange,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const [isFocused, setIsFocused] = useState(false);

    const isPassword = type === 'password';
    const inputType = isPassword && showPassword ? 'text' : type;

    return (
      <div className="space-y-2">
        <Label htmlFor={name} className="text-foreground text-sm font-medium">
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </Label>

        <div className="group relative">
          <Input
            ref={ref}
            id={name}
            name={name}
            type={inputType}
            placeholder={placeholder}
            required={required}
            value={value}
            onChange={onChange}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            aria-invalid={error ? 'true' : 'false'}
            aria-describedby={error ? `${name}-error` : undefined}
            className={`bg-background/50 h-12 border-2 text-base backdrop-blur-sm transition-all duration-300 ease-out ${
              error
                ? 'border-destructive focus:border-destructive focus:ring-destructive/20 animate-pulse'
                : 'border-border focus:border-primary focus:ring-primary/20'
            } ${isFocused ? 'shadow-primary/10 scale-[1.01] shadow-lg' : ''} hover:border-primary/50 hover:shadow-primary/5 hover:shadow-md ${className} `}
            {...props}
          />

          {/* Password Toggle */}
          {isPassword && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowPassword(!showPassword)}
              className="hover:bg-muted/50 absolute top-1/2 right-2 h-8 w-8 -translate-y-1/2 p-0 transition-all duration-200"
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeOff className="text-muted-foreground h-4 w-4" />
              ) : (
                <Eye className="text-muted-foreground h-4 w-4" />
              )}
            </Button>
          )}

          {/* Focus Ring Effect */}
          <div
            className={`pointer-events-none absolute inset-0 rounded-md transition-all duration-300 ${isFocused ? 'ring-primary/20 ring-offset-background ring-2 ring-offset-2' : ''} `}
          />
        </div>

        {/* Error Message */}
        {error && (
          <p
            id={`${name}-error`}
            className="text-destructive animate-in slide-in-from-top-1 text-sm duration-300"
            role="alert"
          >
            {error}
          </p>
        )}
      </div>
    );
  }
);

ModernAuthInput.displayName = 'ModernAuthInput';
