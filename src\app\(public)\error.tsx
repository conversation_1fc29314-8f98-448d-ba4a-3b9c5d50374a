'use client';

import { RouteError } from '@/components/errors/route-error';

export default function PublicGroupError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <RouteError
      title="Bir şeyler ters gitti"
      description="Genel sayfaları yüklerken bir sorun oluştu."
      error={error}
      reset={reset}
      context={{ routeGroup: '(public)' }}
    />
  );
}
