import { getGymTrainers } from '@/lib/actions/dashboard/company/appointment-actions';
import { MembershipWithMember } from '@/types/business/membership';
import { SmartAppointmentForm } from './components/smart-appointment-form';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { getGymMembers } from '@/lib/actions/all-actions';

interface PageProps {
  params: {
    gymId: string;
  };
  searchParams: {
    memberId?: string;
  };
}

export default async function NewAppointmentPage({
  params,
  searchParams,
}: PageProps) {
  const { gymId } = await params;
  const { memberId } = await searchParams;

  // Load all required data in parallel
  const [membersResult, trainersResult] = await Promise.all([
    getGymMembers(gymId),
    getGymTrainers(gymId),
  ]);

  if (!membersResult.success) {
    return (
      <div className="py-8 text-center text-red-600">
        Ü<PERSON>ler yüklenirken hata oluştu: {membersResult.error}
      </div>
    );
  }

  if (!trainersResult.success) {
    return (
      <div className="py-8 text-center text-red-600">
        Antrenörler yüklenirken hata oluştu: {trainersResult.error}
      </div>
    );
  }

  const members = membersResult.data?.members || [];
  const trainers = trainersResult.data || [];

  if (members.length === 0) {
    return (
      <div className="py-8 text-center">
        <h3 className="mb-2 text-lg font-medium">Aktif üye bulunamadı</h3>
        <p className="text-muted-foreground mb-4">
          Randevu oluşturabilmek için önce salona aktif paketli üyeler
          eklemelisiniz.
        </p>
        <Button asChild>
          <Link href={`/dashboard/gym/${gymId}/members`}>Üye Yönetimi</Link>
        </Button>
      </div>
    );
  }

  return (
    <SmartAppointmentForm
      gymId={gymId}
      members={members as MembershipWithMember[]}
      trainers={trainers}
      preSelectedMemberId={memberId}
    />
  );
}
