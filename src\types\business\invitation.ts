/**
 * Invitation business domain types
 *
 * Extracted from src/lib/actions/notifications/invitation-actions.ts
 */

import type {
  InvitationStatus,
  InvitationType,
  InvitationRole,
} from '../database/enums';

/**
 * Incoming invitation with gym details (gym_invite type)
 */
export interface IncomingInvitation {
  id: string;
  gym: {
    id: string;
    name: string;
    address: string | null;
    gym_phone: string | null;
  };
  role: InvitationRole;
  message: string | null;
  status: InvitationStatus;
  created_at: string;
  expires_at: string | null;
  responded_at: string | null;
}

/**
 * Outgoing request with gym details (join_request type)
 */
export interface OutgoingRequest {
  id: string;
  gym: {
    id: string;
    name: string;
    address: string | null;
    gym_phone: string | null;
  };
  role: InvitationRole;
  message: string | null;
  status: InvitationStatus | 'expired';
  created_at: string;
  expires_at: string | null;
  responded_at: string | null;
}

/**
 * Gym incoming request (join_request type)
 */
export interface GymIncomingRequest {
  id: string;
  profile: {
    id: string;
    full_name: string;
    email: string;
  };
  role: InvitationRole;
  status: InvitationStatus;
  created_at: string;
  expires_at: string | null;
  responded_at: string | null;
}

/**
 * Gym outgoing invitation (gym_invite type)
 */
export interface GymOutgoingInvitation {
  id: string;
  profile: {
    id: string;
    full_name: string;
    email: string;
  };
  role: InvitationRole;
  status: InvitationStatus;
  created_at: string;
  expires_at: string | null;
  responded_at: string | null;
}

/**
 * Base gym invitation interface - V2 Minimal Structure
 */
export interface GymInvitation {
  id: string;
  gym_id: string;
  profile_id: string;
  type: InvitationType;
  role: InvitationRole;
  status: InvitationStatus;
  message: string | null;
  created_at: string;
  expires_at: string | null;
  responded_at: string | null;
  updated_at: string;
}

/**
 * Trainer invitation interface - DEPRECATED
 * Use IncomingInvitation and OutgoingInvitation from invitation-types.ts instead
 */
