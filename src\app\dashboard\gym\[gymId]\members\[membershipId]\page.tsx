import { notFound } from 'next/navigation';
import { getMemberCompleteData } from '@/lib/actions/dashboard/company/gym-member-actions';
import { MemberDetailClient } from './components/member-detail-client';

interface MemberDetailPageProps {
  params: Promise<{
    gymId: string;
    membershipId: string;
  }>;
}

export default async function MemberDetailPage({
  params,
}: MemberDetailPageProps) {
  const { gymId, membershipId } = await params;

  // Üye verilerini çek
  const memberResult = await getMemberCompleteData(membershipId, gymId);

  if (!memberResult.success || !memberResult.data) {
    notFound();
  }

  const memberData = memberResult.data;

  return (
    <MemberDetailClient
      memberData={memberData}
      gymId={gymId}
      membershipId={membershipId}
    />
  );
}
