'use client';

import { useState } from 'react';
import {
  ArrowLeft,
  User,
  Mail,
  Phone,
  Calendar,
  Activity,
  Target,
  Ruler,
  Weight,
  Plus,
} from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { formatDate } from '@/lib/utils';
import { isGuestEmail, getGuestDisplayName } from '@/lib/utils/guest-utils';

import type { CompleteMemberData } from '@/lib/actions/dashboard/company/gym-member-actions';
import { AssignPackageDialog } from '@/app/dashboard/manager/components/AssignPackageDialog';

interface MemberDetailClientProps {
  memberData: CompleteMemberData;
  gymId: string;
  membershipId: string;
}

// İsim kısaltması helper fonksiyonu
function getInitials(fullName: string) {
  return fullName
    .split(' ')
    .map(name => name.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

// Cinsiyet çevirisi
function getGenderDisplay(gender: string | null) {
  switch (gender) {
    case 'male':
      return 'Erkek';
    case 'female':
      return 'Kadın';
    case 'other':
      return 'Diğer';
    case 'prefer_not_to_say':
      return 'Belirtmek istemiyorum';
    default:
      return 'Belirtilmemiş';
  }
}

// Paket durumu çevirisi
function getPackageStatusDisplay(status: string) {
  switch (status) {
    case 'active':
      return { label: 'Aktif', variant: 'default' as const };
    case 'expired':
      return { label: 'Süresi Dolmuş', variant: 'destructive' as const };
    case 'cancelled':
      return { label: 'İptal Edilmiş', variant: 'secondary' as const };
    default:
      return { label: 'Bilinmeyen', variant: 'outline' as const };
  }
}

export function MemberDetailClient({
  memberData,
  gymId,
  membershipId,
}: MemberDetailClientProps) {
  const [isAssignPackageDialogOpen, setIsAssignPackageDialogOpen] =
    useState(false);

  const profile = memberData.profile;
  const memberDetails = memberData.memberDetails;
  const packages = memberData.packages || [];

  // Misafir account kontrolü
  const isGuest =
    profile.is_guest_account || (profile.email && isGuestEmail(profile.email));
  const displayEmail =
    isGuest && profile.email
      ? getGuestDisplayName(profile.email)
      : profile.email;

  const handleCloseDialog = () => {
    setIsAssignPackageDialogOpen(false);
  };

  const handlePackageAssigned = () => {
    setIsAssignPackageDialogOpen(false);
    // assignPackageToMember fonksiyonu revalidatePaths ile cache'i otomatik günceller
  };

  return (
    <div className="space-y-6">
      {/* Header with back button */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/dashboard/gym/${gymId}/members`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Geri Dön
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Üye Detayları</h1>
          <p className="text-muted-foreground">
            Üye bilgilerini görüntüleyin ve yönetin
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Profil Bilgileri */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Profil Bilgileri
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage
                  src={profile.avatar_url || ''}
                  alt={profile.full_name || ''}
                />
                <AvatarFallback className="text-lg">
                  {getInitials(profile.full_name || 'İsimsiz Üye')}
                </AvatarFallback>
              </Avatar>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <h3 className="text-xl font-semibold">
                    {profile.full_name || 'İsimsiz Üye'}
                  </h3>
                  {isGuest && (
                    <Badge
                      variant="outline"
                      className="bg-orange-100 text-orange-800"
                    >
                      Misafir
                    </Badge>
                  )}
                </div>
                {profile.email && (
                  <div className="text-muted-foreground flex items-center gap-2 text-sm">
                    <Mail className="h-4 w-4" />
                    <span className={isGuest ? 'italic' : ''}>
                      {isGuest ? displayEmail : profile.email}
                    </span>
                  </div>
                )}
                {profile.phone_number && (
                  <div className="text-muted-foreground flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4" />
                    {profile.phone_number}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Üye Detayları */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Üye Detayları
            </CardTitle>
          </CardHeader>
          <CardContent>
            {memberDetails ? (
              <div className="grid gap-4 sm:grid-cols-2">
                {memberDetails.age && (
                  <div className="flex items-center gap-2">
                    <Calendar className="text-muted-foreground h-4 w-4" />
                    <span className="text-sm">
                      <span className="font-medium">Yaş:</span>{' '}
                      {memberDetails.age}
                    </span>
                  </div>
                )}
                {memberDetails.gender && (
                  <div className="flex items-center gap-2">
                    <User className="text-muted-foreground h-4 w-4" />
                    <span className="text-sm">
                      <span className="font-medium">Cinsiyet:</span>{' '}
                      {getGenderDisplay(memberDetails.gender)}
                    </span>
                  </div>
                )}
                {memberDetails.height_cm && (
                  <div className="flex items-center gap-2">
                    <Ruler className="text-muted-foreground h-4 w-4" />
                    <span className="text-sm">
                      <span className="font-medium">Boy:</span>{' '}
                      {memberDetails.height_cm} cm
                    </span>
                  </div>
                )}
                {memberDetails.weight_kg && (
                  <div className="flex items-center gap-2">
                    <Weight className="text-muted-foreground h-4 w-4" />
                    <span className="text-sm">
                      <span className="font-medium">Kilo:</span>{' '}
                      {memberDetails.weight_kg} kg
                    </span>
                  </div>
                )}
                {memberDetails.fitness_goal && (
                  <div className="col-span-full">
                    <div className="flex items-start gap-2">
                      <Target className="text-muted-foreground mt-0.5 h-4 w-4" />
                      <div className="text-sm">
                        <span className="font-medium">Fitness Hedefi:</span>
                        <p className="text-muted-foreground mt-1">
                          {memberDetails.fitness_goal}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-muted-foreground text-sm">
                Üye detay bilgileri henüz eklenmemiş.
              </p>
            )}
          </CardContent>
        </Card>

        {/* Paket Bilgileri */}
        <Card className="md:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Paket Bilgileri</CardTitle>
                <CardDescription>
                  Üyenin satın aldığı paketler ve durumları
                </CardDescription>
              </div>
              <Button
                onClick={() => setIsAssignPackageDialogOpen(true)}
                className="bg-primary/5 border-primary/20 hover:bg-primary/10"
                variant="outline"
              >
                <Plus className="mr-2 h-4 w-4" />
                Yeni Paket Ekle
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {packages.length > 0 ? (
              <div className="space-y-4">
                {packages.map(pkg => {
                  const statusInfo = getPackageStatusDisplay(pkg.status);
                  return (
                    <div key={pkg.id} className="rounded-lg border p-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <h4 className="font-semibold">{pkg.name}</h4>
                            <Badge variant={statusInfo.variant}>
                              {statusInfo.label}
                            </Badge>
                          </div>
                          <p className="text-muted-foreground text-sm">
                            {pkg.package_type} • {pkg.duration_days} gün
                          </p>
                          <div className="grid gap-1 text-sm">
                            <div>
                              <span className="font-medium">Başlangıç:</span>{' '}
                              {formatDate(pkg.start_date)}
                            </div>
                            {pkg.end_date && (
                              <div>
                                <span className="font-medium">Bitiş:</span>{' '}
                                {formatDate(pkg.end_date)}
                              </div>
                            )}
                            <div>
                              <span className="font-medium">Fiyat:</span> ₺
                              {pkg.purchase_price}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <p className="text-muted-foreground text-sm">
                Henüz satın alınmış paket bulunmuyor.
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Assign Package Dialog */}
      <AssignPackageDialog
        isOpen={isAssignPackageDialogOpen}
        onClose={handleCloseDialog}
        membershipId={membershipId}
        memberName={profile.full_name || 'İsimsiz Üye'}
        gymId={gymId}
        onPackageAssigned={handlePackageAssigned}
      />
    </div>
  );
}
