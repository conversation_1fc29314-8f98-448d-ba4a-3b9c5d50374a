'use client';

import { RouteError } from '@/components/errors/route-error';

export default function ProfileError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <RouteError
      title="Profil Hatası"
      description="Profil sayfası yüklenirken bir hata oluştu."
      error={error}
      reset={reset}
      context={{ route: '/profile' }}
    />
  );
}
