'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Plus, UserPlus, Users, ChevronDown } from 'lucide-react';
export interface MemberAddDropdownProps {
  onInviteExisting: () => void;
  onCreateNew: () => void;
}
export function MemberAddDropdown({
  onInviteExisting,
  onCreateNew,
}: MemberAddDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleInviteExisting = () => {
    setIsOpen(false);
    onInviteExisting();
  };

  const handleCreateNew = () => {
    setIsOpen(false);
    onCreateNew();
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          <PERSON><PERSON>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuItem
          onClick={handleInviteExisting}
          className="cursor-pointer"
        >
          <Users className="mr-2 h-4 w-4" />
          <div className="flex flex-col">
            <span className="font-medium">Mevcut Kullanıcı Davet Et</span>
            <span className="text-muted-foreground text-xs">
              Sisteme kayıtlı kullanıcıları ara ve davet et
            </span>
          </div>
        </DropdownMenuItem>

        <DropdownMenuItem onClick={handleCreateNew} className="cursor-pointer">
          <UserPlus className="mr-2 h-4 w-4" />
          <div className="flex flex-col">
            <span className="font-medium">Yeni Kullanıcı Oluştur</span>
            <span className="text-muted-foreground text-xs">
              Yeni kullanıcı hesabı oluştur ve direkt ekle
            </span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
