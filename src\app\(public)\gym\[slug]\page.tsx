import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { getGymUrl } from '@/lib/utils/url-utils';

import { getAuthenticatedUser } from '@/lib/auth/server-auth';
import { getUserRoles } from '@/lib/auth/server-auth';

import { GymPackages, Gyms } from '@/types/database/tables';
import {
  GymAboutSection,
  GymContactSection,
  GymHeroSection,
  GymPackagesSection,
  GymReviewsSection,
} from '@/components/public/gym-page';
import {
  getComprehensiveGymMembershipStatus,
  getGymBySlug,
  getGymPackages,
  getReviewsByGymId,
  getReviewStats,
  getTrainerStatus,
} from '@/lib/actions/all-actions';

interface PublicGymPageProps {
  params: Promise<{ slug: string }>;
}
// Generate dynamic metadata for SEO
export async function generateMetadata({
  params,
}: PublicGymPageProps): Promise<Metadata> {
  const { slug } = await params;

  // Get gym data by slug
  const gymResponse = await getGymBySlug(slug);

  if (!gymResponse.success || !gymResponse.data) {
    return {
      title: 'Salon Bulunamadı | Sportiva',
      description: 'Aradığınız spor salonu bulunamadı.',
    };
  }

  const gym = gymResponse.data;

  // Create SEO-optimized title and description
  const title = `${gym.name} | Sportiva`;

  let description = `${gym.name} spor salonuna katılın. `;

  if (gym.city && gym.district) {
    description += `${gym.city} ${gym.district} bölgesinde `;
  } else if (gym.city) {
    description += `${gym.city} şehrinde `;
  }

  description +=
    'modern ekipmanlar ve profesyonel eğitmenlerle fitness hedeflerinize ulaşın.';

  if (gym.description) {
    description += ` ${gym.description.slice(0, 100)}...`;
  }

  // Create keywords array
  const keywords = [
    gym.name,
    'spor salonu',
    'gym',
    'fitness',
    'spor merkezi',
    'antrenman',
  ];

  if (gym.city) {
    keywords.push(gym.city.toLowerCase());
  }

  if (gym.district) {
    keywords.push(gym.district.toLowerCase());
  }

  if (gym.features && gym.features.length > 0) {
    keywords.push(...gym.features.slice(0, 3));
  }

  // Get gym URL using utility function
  const gymUrl = getGymUrl(slug);

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      url: gymUrl,
      siteName: 'Sportiva',
      images: gym.company?.logo_url
        ? [
            {
              url: gym.company.logo_url,
              width: 1200,
              height: 630,
              alt: `${gym.name} logosu`,
            },
          ]
        : [],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: gym.company?.logo_url ? [gym.company.logo_url] : [],
    },
    alternates: {
      canonical: gymUrl,
    },
  };
}

export default async function PublicGymPage({ params }: PublicGymPageProps) {
  // Get slug from params
  const { slug } = await params;

  // Get gym data by slug first
  const gymResponse = await getGymBySlug(slug);

  if (!gymResponse.success || !gymResponse.data) {
    notFound();
  }

  const gym: Gyms = gymResponse.data;

  // Get current user using auth helper
  const user = await getAuthenticatedUser();
  const roles = user ? await getUserRoles() : [];
  const hasTrainerRole = roles.includes('trainer');
  const hasMemberRole = roles.includes('member');

  // Fetch basic data that's always needed
  const [packagesResponse, reviewStatsResponse, reviewsResponse] =
    await Promise.all([
      getGymPackages(gym.id),
      getReviewStats(gym.id),
      getReviewsByGymId(gym.id, 1, 4),
    ]);

  // Fetch user-specific data only if user is authenticated
  let membershipStatusResponse = null;
  let trainerStatusResponse = null;

  if (user) {
    [membershipStatusResponse, trainerStatusResponse] = await Promise.all([
      getComprehensiveGymMembershipStatus(gym.id),
      getTrainerStatus(gym.id),
    ]);
  }

  // Process the responses
  const packages: GymPackages[] = packagesResponse.success
    ? packagesResponse.data || []
    : [];

  const reviewStats =
    reviewStatsResponse.success && reviewStatsResponse.data
      ? reviewStatsResponse.data
      : null;

  const reviews =
    reviewsResponse.success && reviewsResponse.data
      ? reviewsResponse.data.reviews
      : [];

  const membershipStatus =
    membershipStatusResponse?.success && membershipStatusResponse.data
      ? membershipStatusResponse.data.membershipStatus
      : 'none';

  const isGymOwner =
    membershipStatusResponse?.success && membershipStatusResponse.data
      ? membershipStatusResponse.data.isGymOwner
      : false;

  const trainerStatus =
    trainerStatusResponse?.success && trainerStatusResponse.data
      ? trainerStatusResponse.data.trainerStatus
      : 'none';

  return (
    <div className="-mt-10 min-h-screen">
      {/* Hero Section */}
      <GymHeroSection
        gymId={gym.id}
        gym={gym}
        membershipStatus={membershipStatus}
        trainerStatus={trainerStatus}
        reviewStats={reviewStats}
        user={user}
        isGymOwner={isGymOwner}
        hasTrainerRole={hasTrainerRole}
        hasMemberRole={hasMemberRole}
      />

      {/* About Section */}
      <GymAboutSection
        name={gym.name}
        description={gym.description}
        features={gym.features}
      />

      {/* Packages Section */}
      <GymPackagesSection packages={packages} />

      {/* Reviews Section */}
      <GymReviewsSection initialReviews={reviews} />

      {/* Contact Section */}
      <GymContactSection
        address={gym.address}
        city={gym.city}
        district={gym.district}
        gym_phone={gym.gym_phone}
        socialLinks={(gym as any).company?.social_links}
        companyName={(gym as any).company?.name}
      />
    </div>
  );
}
