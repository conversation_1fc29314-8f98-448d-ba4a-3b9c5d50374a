// Supabase Database Types - Auto-generated from schema
// Generated on: 2025-01-03

import { GymManagerInvitationStatus } from '@/types';

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

// Enum Types
export type AppointmentStatus = 'scheduled' | 'completed' | 'cancelled';
export type InvitationStatus = 'pending' | 'accepted' | 'rejected';
export type InvitationType = 'join_request' | 'gym_invite';

export type InvitationRole = 'member' | 'trainer';
export type MembershipPackageStatus =
  | 'active'
  | 'expired'
  | 'cancelled'
  | 'suspended'
  | 'pending';
export type StaffRoleType =
  | 'trainer'
  | 'receptionist'
  | 'cleaner'
  | 'security'
  | 'manager_assistant'
  | 'maintenance'
  | 'nutritionist'
  | 'physiotherapist'
  | 'custom';
export type StaffStatus = 'active' | 'passive' | 'terminated' | 'on_leave';
export type AppointmentType = 'personal' | 'group';
export type ParticipantStatus =
  | 'confirmed'
  | 'cancelled'
  | 'no_show'
  | 'completed';
export type PackageType = 'appointment_standard' | 'appointment_vip' | 'daily';

// User Settings Types
export type FontSize = 'small' | 'normal' | 'large';
export type ProfileVisibility = 'public' | 'private' | 'friends';

export type NotificationCategories = {
  appointments: boolean;
  payments: boolean;
  announcements: boolean;
  reminders: boolean;
};

// Database Tables
export interface Database {
  public: {
    Tables: {
      appointment_participants: {
        Row: {
          id: string;
          appointment_id: string;
          gym_membership_package_id: string;
          status: string | null;
          notes: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          appointment_id: string;
          gym_membership_package_id: string;
          status?: string | null;
          notes?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          appointment_id?: string;
          gym_membership_package_id?: string;
          status?: string | null;
          notes?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      appointments: {
        Row: {
          id: string;
          gym_id: string;
          appointment_date: string;
          appointment_type: string;
          max_participants: number;
          status: AppointmentStatus | null;
          trainer_profile_id: string | null;
          notes: string | null;
          created_at: string | null;
          updated_at: string | null;
          gym_package_id: string | null;
        };
        Insert: {
          id?: string;
          gym_id: string;
          appointment_date: string;
          appointment_type: string;
          max_participants?: number;
          status?: AppointmentStatus | null;
          trainer_profile_id?: string | null;
          notes?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          gym_package_id?: string | null;
        };
        Update: {
          id?: string;
          gym_id?: string;
          appointment_date?: string;
          appointment_type?: string;
          max_participants?: number;
          status?: AppointmentStatus | null;
          trainer_profile_id?: string | null;
          notes?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          gym_package_id?: string | null;
        };
      };
      audit_logs: {
        Row: {
          id: string;
          user_id: string;
          action: string;
          table_name: string | null;
          record_id: string | null;
          old_values: Json | null;
          new_values: Json | null;
          created_at: string | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          action: string;
          table_name?: string | null;
          record_id?: string | null;
          old_values?: Json | null;
          new_values?: Json | null;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          action?: string;
          table_name?: string | null;
          record_id?: string | null;
          old_values?: Json | null;
          new_values?: Json | null;
          created_at?: string | null;
        };
      };
      companies: {
        Row: {
          id: string;
          manager_profile_id: string;
          name: string;
          logo_url: string | null;
          trial_ends_at: string | null;
          phone: string | null;
          email: string | null;
          status: string | null;
          created_at: string | null;
          updated_at: string | null;
          subscription_start_date: string | null;
          subscription_end_date: string | null;
          platform_package_id: string | null;
          social_links: Json | null;
        };
        Insert: {
          id?: string;
          manager_profile_id: string;
          name: string;
          logo_url?: string | null;
          trial_ends_at?: string | null;
          phone?: string | null;
          email?: string | null;
          status?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          subscription_start_date?: string | null;
          subscription_end_date?: string | null;
          platform_package_id?: string | null;
          social_links?: Json | null;
        };
        Update: {
          id?: string;
          manager_profile_id?: string;
          name?: string;
          logo_url?: string | null;
          trial_ends_at?: string | null;
          phone?: string | null;
          email?: string | null;
          status?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          subscription_start_date?: string | null;
          subscription_end_date?: string | null;
          platform_package_id?: string | null;
          social_links?: Json | null;
        };
      };
      gym_invitations: {
        Row: {
          id: string;
          gym_id: string;
          profile_id: string;
          type: InvitationType;
          role: InvitationRole;
          status: InvitationStatus;
          message: string | null;
          expires_at: string | null;
          responded_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          gym_id: string;
          profile_id: string;
          type: InvitationType;
          role: InvitationRole;
          status?: InvitationStatus;
          message?: string | null;
          expires_at?: string | null;
          responded_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          gym_id?: string;
          profile_id?: string;
          type?: InvitationType;
          role?: InvitationRole;
          status?: InvitationStatus;
          message?: string | null;
          expires_at?: string | null;
          responded_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      gym_membership_packages: {
        Row: {
          id: string;
          membership_id: string;
          gym_package_id: string;
          purchase_price: number;
          start_date: string;
          end_date: string | null;
          created_at: string | null;
          updated_at: string | null;
          status: MembershipPackageStatus;
          gym_id: string;
          remaining_sessions: number | null;
          total_sessions: number | null;
          used_sessions: number | null;
          last_session_date: string | null;
          notes: string | null;
        };
        Insert: {
          id?: string;
          membership_id: string;
          gym_package_id: string;
          purchase_price: number;
          start_date: string;
          end_date?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          status?: MembershipPackageStatus;
          gym_id: string;
          remaining_sessions?: number | null;
          total_sessions?: number | null;
          used_sessions?: number | null;
          last_session_date?: string | null;
          notes?: string | null;
        };
        Update: {
          id?: string;
          membership_id?: string;
          gym_package_id?: string;
          purchase_price?: number;
          start_date?: string;
          end_date?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          status?: MembershipPackageStatus;
          gym_id?: string;
          remaining_sessions?: number | null;
          total_sessions?: number | null;
          used_sessions?: number | null;
          last_session_date?: string | null;
          notes?: string | null;
        };
      };
      gym_memberships: {
        Row: {
          id: string;
          gym_id: string | null;
          status: string | null;
          created_at: string | null;
          updated_at: string | null;
          request_date: string | null;
          approved_at: string | null;
          profile_id: string;
        };
        Insert: {
          id?: string;
          gym_id?: string | null;
          status?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          request_date?: string | null;
          approved_at?: string | null;
          profile_id: string;
        };
        Update: {
          id?: string;
          gym_id?: string | null;
          status?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          request_date?: string | null;
          approved_at?: string | null;
          profile_id?: string;
        };
      };
      gym_packages: {
        Row: {
          id: string;
          gym_id: string | null;
          name: string;
          description: string | null;
          price: number;
          duration_days: number | null;
          is_active: boolean | null;
          created_at: string | null;
          updated_at: string | null;
          package_type: PackageType | null;
          max_participants: number | null;
          session_count: number | null;
          session_duration_minutes: number | null;
        };
        Insert: {
          id?: string;
          gym_id?: string | null;
          name: string;
          description?: string | null;
          price: number;
          duration_days?: number | null;
          is_active?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
          package_type?: PackageType | null;
          max_participants?: number | null;
          session_count?: number | null;
          session_duration_minutes?: number | null;
        };
        Update: {
          id?: string;
          gym_id?: string | null;
          name?: string;
          description?: string | null;
          price?: number;
          duration_days?: number | null;
          is_active?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
          package_type?: PackageType | null;
          max_participants?: number | null;
          session_count?: number | null;
          session_duration_minutes?: number | null;
        };
      };
      gym_reviews: {
        Row: {
          id: string;
          profile_id: string;
          gym_id: string | null;
          rating: number | null;
          comment: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          profile_id: string;
          gym_id?: string | null;
          rating?: number | null;
          comment?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          profile_id?: string;
          gym_id?: string | null;
          rating?: number | null;
          comment?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      gym_staffs: {
        Row: {
          id: string;
          gym_id: string;
          name: string;
          surname: string;
          hire_date: string;
          salary_amount: number | null;
          created_at: string | null;
          updated_at: string | null;
          staff_type: string;
        };
        Insert: {
          id?: string;
          gym_id: string;
          name: string;
          surname: string;
          hire_date?: string;
          salary_amount?: number | null;
          created_at?: string | null;
          updated_at?: string | null;
          staff_type?: string;
        };
        Update: {
          id?: string;
          gym_id?: string;
          name?: string;
          surname?: string;
          hire_date?: string;
          salary_amount?: number | null;
          created_at?: string | null;
          updated_at?: string | null;
          staff_type?: string;
        };
      };
      gym_trainers: {
        Row: {
          id: string;
          gym_id: string;
          trainer_profile_id: string;
          status: string | null;
          left_at: string | null;
          created_at: string | null;
          updated_at: string | null;
          permissions: any; // JSONB
        };
        Insert: {
          id?: string;
          gym_id: string;
          trainer_profile_id: string;
          status?: string | null;
          left_at?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          permissions?: any; // JSONB
        };
        Update: {
          id?: string;
          gym_id?: string;
          trainer_profile_id?: string;
          status?: string | null;
          left_at?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          permissions?: any; // JSONB
        };
      };
      gyms: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          gym_phone: string | null;
          address: string | null;
          city: string | null;
          gym_type: string | null;
          cover_image_url: string | null;
          status: string | null;
          created_at: string | null;
          updated_at: string | null;
          features: string[] | null;
          district: string | null;
          slug: string | null;
          opening_time: string | null;
          closing_time: string | null;
          average_rating: number | null;
          review_count: number | null;
          company_id: string;
          max_capacity: number | null;
          time_slots: string[] | null;
          owner_profile_id: string;
          manager_profile_id: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          gym_phone?: string | null;
          address?: string | null;
          city?: string | null;
          gym_type?: string | null;
          cover_image_url?: string | null;
          status?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          features?: string[] | null;
          district?: string | null;
          slug?: string | null;
          opening_time?: string | null;
          closing_time?: string | null;
          average_rating?: number | null;
          review_count?: number | null;
          owner_profile_id: string;
          company_id: string;
          max_capacity?: number | null;
          time_slots?: string[] | null;
          manager_profile_id: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          gym_phone?: string | null;
          address?: string | null;
          city?: string | null;
          gym_type?: string | null;
          cover_image_url?: string | null;
          status?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          features?: string[] | null;
          district?: string | null;
          slug?: string | null;
          opening_time?: string | null;
          closing_time?: string | null;
          average_rating?: number | null;
          review_count?: number | null;
          owner_profile_id: string;
          max_capacity?: number | null;
          time_slots?: string[] | null;
          manager_profile_id: string;
        };
      };
      member_details: {
        Row: {
          profile_id: string;
          height_cm: number | null;
          weight_kg: number | null;
          gender: string | null;
          fitness_goal: string | null;
          created_at: string | null;
          updated_at: string | null;
          age: number | null;
          invite_code: string | null;
        };
        Insert: {
          profile_id: string;
          height_cm?: number | null;
          weight_kg?: number | null;
          gender?: string | null;
          fitness_goal?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          age?: number | null;
          invite_code?: string | null;
        };
        Update: {
          profile_id?: string;
          height_cm?: number | null;
          weight_kg?: number | null;
          gender?: string | null;
          fitness_goal?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          age?: number | null;
          invite_code?: string | null;
        };
      };
      notifications: {
        Row: {
          id: string;
          user_id: string | null;
          gym_id: string | null;
          title: string;
          message: string;
          is_read: boolean | null;
          created_at: string | null;
        };
        Insert: {
          id?: string;
          user_id?: string | null;
          gym_id?: string | null;
          title: string;
          message: string;
          is_read?: boolean | null;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string | null;
          gym_id?: string | null;
          title?: string;
          message?: string;
          is_read?: boolean | null;
          created_at?: string | null;
        };
      };
      platform_packages: {
        Row: {
          id: string;
          name: string;
          tier: string;
          duration: string;
          price: number;
          max_gyms: number | null;
          max_members: number | null;
          max_trainers: number | null;
          max_staff: number | null;
          max_monthly_appointments: number | null;
          features: Json;
          is_active: boolean | null;
        };
        Insert: {
          id?: string;
          name: string;
          tier: string;
          duration: string;
          price: number;
          max_gyms?: number | null;
          max_members?: number | null;
          max_trainers?: number | null;
          max_staff?: number | null;
          max_monthly_appointments?: number | null;
          features?: Json;
          is_active?: boolean | null;
        };
        Update: {
          id?: string;
          name?: string;
          tier?: string;
          duration?: string;
          price?: number;
          max_gyms?: number | null;
          max_members?: number | null;
          max_trainers?: number | null;
          max_staff?: number | null;
          max_monthly_appointments?: number | null;
          features?: Json;
          is_active?: boolean | null;
        };
      };
      profiles: {
        Row: {
          id: string;
          created_at: string | null;
          updated_at: string | null;
          email: string | null;
          full_name: string | null;
          avatar_url: string | null;
          phone_number: string | null;
          is_guest_account: boolean;
        };
        Insert: {
          id?: string;
          created_at?: string | null;
          updated_at?: string | null;
          email?: string | null;
          full_name?: string | null;
          avatar_url?: string | null;
          phone_number?: string | null;
          is_guest_account?: boolean;
        };
        Update: {
          id?: string;
          created_at?: string | null;
          updated_at?: string | null;
          email?: string | null;
          full_name?: string | null;
          avatar_url?: string | null;
          phone_number?: string | null;
          is_guest_account?: boolean;
        };
      };
      trainer_details: {
        Row: {
          profile_id: string;
          specialization: string | null;
          certification_level: string | null;
          status: string | null;
          experience_years: number | null;
          bio: string | null;
          invite_code: string | null;
        };
        Insert: {
          profile_id: string;
          specialization?: string | null;
          certification_level?: string | null;
          status?: string | null;
          experience_years?: number | null;
          bio?: string | null;
          invite_code?: string | null;
        };
        Update: {
          profile_id?: string;
          specialization?: string | null;
          certification_level?: string | null;
          status?: string | null;
          experience_years?: number | null;
          bio?: string | null;
          invite_code?: string | null;
        };
      };
      user_roles: {
        Row: {
          profile_id: string | null;
          is_member: boolean | null;
          member_gymids: string[] | null;
          is_trainer: boolean | null;
          trainer_gymids: string[] | null;
          is_manager: boolean | null;
          managed_gymids: string[] | null;
        };
        Insert: {
          profile_id?: string | null;
          is_member?: boolean | null;
          member_gymIds?: string[] | null;
          is_trainer?: boolean | null;
          trainer_gymIds?: string[] | null;
          is_manager?: boolean | null;
          managed_gymIds?: string[] | null;
        };
        Update: {
          profile_id?: string | null;
          is_member?: boolean | null;
          member_gymIds?: string[] | null;
          is_trainer?: boolean | null;
          trainer_gymIds?: string[] | null;
          is_manager?: boolean | null;
          managed_gymIds?: string[] | null;
        };
      };
      user_settings: {
        Row: {
          id: string;
          profile_id: string;
          email_notifications: boolean | null;
          push_notifications: boolean | null;
          profile_visibility: string | null;
          notification_categories: Json | null;
        };
        Insert: {
          id?: string;
          profile_id: string;
          email_notifications?: boolean | null;
          push_notifications?: boolean | null;
          profile_visibility?: string | null;
          notification_categories?: Json | null;
        };
        Update: {
          id?: string;
          profile_id?: string;
          email_notifications?: boolean | null;
          push_notifications?: boolean | null;
          profile_visibility?: string | null;
          notification_categories?: Json | null;
        };
      };
      equipment_categories: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      gym_equipment: {
        Row: {
          id: string;
          gym_id: string;
          category_id: string | null;
          name: string;
          brand: string | null;
          model: string | null;
          serial_number: string | null;
          purchase_date: string | null;
          purchase_price: number | null;
          warranty_expiry: string | null;
          condition: string;
          status: string;
          location: string | null;
          notes: string | null;
          image_url: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          gym_id: string;
          category_id?: string | null;
          name: string;
          brand?: string | null;
          model?: string | null;
          serial_number?: string | null;
          purchase_date?: string | null;
          purchase_price?: number | null;
          warranty_expiry?: string | null;
          condition?: string;
          status?: string;
          location?: string | null;
          notes?: string | null;
          image_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          gym_id?: string;
          category_id?: string | null;
          name?: string;
          brand?: string | null;
          model?: string | null;
          serial_number?: string | null;
          purchase_date?: string | null;
          purchase_price?: number | null;
          warranty_expiry?: string | null;
          condition?: string;
          status?: string;
          location?: string | null;
          notes?: string | null;
          image_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      equipment_usage_logs: {
        Row: {
          id: string;
          equipment_id: string;
          user_id: string;
          start_time: string;
          end_time: string | null;
          duration_minutes: number | null;
          notes: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          equipment_id: string;
          user_id: string;
          start_time: string;
          end_time?: string | null;
          duration_minutes?: number | null;
          notes?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          equipment_id?: string;
          user_id?: string;
          start_time?: string;
          end_time?: string | null;
          duration_minutes?: number | null;
          notes?: string | null;
          created_at?: string;
        };
      };
      inventory_categories: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      gym_inventory: {
        Row: {
          id: string;
          gym_id: string;
          category_id: string | null;
          name: string;
          description: string | null;
          sku: string | null;
          barcode: string | null;
          unit_type: string;
          current_stock: number;
          minimum_stock: number;
          maximum_stock: number | null;
          unit_cost: number | null;
          selling_price: number | null;
          supplier_name: string | null;
          supplier_contact: string | null;
          expiry_date: string | null;
          location: string | null;
          image_url: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          gym_id: string;
          category_id?: string | null;
          name: string;
          description?: string | null;
          sku?: string | null;
          barcode?: string | null;
          unit_type: string;
          current_stock: number;
          minimum_stock: number;
          maximum_stock?: number | null;
          unit_cost?: number | null;
          selling_price?: number | null;
          supplier_name?: string | null;
          supplier_contact?: string | null;
          expiry_date?: string | null;
          location?: string | null;
          image_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          gym_id?: string;
          category_id?: string | null;
          name?: string;
          description?: string | null;
          sku?: string | null;
          barcode?: string | null;
          unit_type?: string;
          current_stock?: number;
          minimum_stock?: number;
          maximum_stock?: number | null;
          unit_cost?: number | null;
          selling_price?: number | null;
          supplier_name?: string | null;
          supplier_contact?: string | null;
          expiry_date?: string | null;
          location?: string | null;
          image_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      inventory_transactions: {
        Row: {
          id: string;
          inventory_id: string;
          transaction_type: string;
          quantity: number;
          unit_price: number | null;
          total_amount: number | null;
          reference_number: string | null;
          notes: string | null;
          performed_by: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          inventory_id: string;
          transaction_type: string;
          quantity: number;
          unit_price?: number | null;
          total_amount?: number | null;
          reference_number?: string | null;
          notes?: string | null;
          performed_by?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          inventory_id?: string;
          transaction_type?: string;
          quantity?: number;
          unit_price?: number | null;
          total_amount?: number | null;
          reference_number?: string | null;
          notes?: string | null;
          performed_by?: string | null;
          created_at?: string;
        };
      };
      gym_manager_invitations: {
        Row: {
          id: string;
          company_id: string;
          invite_code: string;
          gym_id: string;
          status: GymManagerInvitationStatus;
          used_by: string | null;
          used_at: string | null;
          expires_at: string;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          company_id: string;
          invite_code?: string;
          gym_id: string;
          status?: GymManagerInvitationStatus;
          used_by?: string | null;
          used_at?: string | null;
          expires_at?: string;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          company_id?: string;
          invite_code?: string;
          gym_id?: string;
          status?: GymManagerInvitationStatus;
          used_by?: string | null;
          used_at?: string | null;
          expires_at?: string;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
    };
    Views: {
      manager_package_overview: {
        Row: {
          id: string | null;
          membership_id: string | null;
          gym_id: string | null;
          gym_package_id: string | null;
          package_name: string | null;
          package_type: string | null;
          total_sessions: number | null;
          used_sessions: number | null;
          remaining_sessions: number | null;
          last_session_date: string | null;
          start_date: string | null;
          end_date: string | null;
          status: MembershipPackageStatus | null;
          member_name: string | null;
          is_expired: boolean | null;
          needs_renewal: boolean | null;
        };
      };
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      appointment_status: AppointmentStatus;
      invitation_status: InvitationStatus;
      invitation_type: InvitationType;
      membership_package_status: MembershipPackageStatus;
      staff_role_type: StaffRoleType;
      staff_status: StaffStatus;
      gym_manager_invitation_status: GymManagerInvitationStatus;
    };
  };
}
