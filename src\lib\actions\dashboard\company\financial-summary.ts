'use server';

import { createAction } from '../../core/core';
import { SupabaseClient } from '@supabase/supabase-js';
import { ApiResponse } from '@/types/global/api';
import { ManagerFinancialSummary } from './dashboard-types';
import { getPackageNames } from './package-actions';
import { getManagerGymNames } from './dashboard-actions';

/**
 * Manager'ın finansal özetini getirir
 * Following Clean Code principles - orchestrates financial data collection
 */
export async function getManagerFinancialSummary(): Promise<
  ApiResponse<ManagerFinancialSummary>
> {
  return await createAction<ManagerFinancialSummary>(async (_, supabase) => {
    // Manager'ın salonlarını getir
    const gymsResponse = await getManagerGymNames();

    if (
      !gymsResponse.success ||
      !gymsResponse.data ||
      gymsResponse.data.length === 0
    ) {
      return getEmptyFinancialSummary();
    }

    const gyms = gymsResponse.data;
    const gymIds = gyms.map((gym: { id: string; name: string }) => gym.id);

    // Aktif üyelik paketlerini getir
    const membershipPackages = await getMembershipPackages(supabase, gymIds);

    // Finansal hesaplamalar
    const totalRevenue = calculateTotalRevenue(membershipPackages);
    const monthlyRevenue = calculateMonthlyRevenue(membershipPackages);
    const revenueGrowth = await calculateRevenueGrowth(
      supabase,
      gymIds,
      monthlyRevenue
    );

    // Salon bazında gelir
    const revenueByGym = calculateRevenueByGym(
      gyms,
      membershipPackages,
      totalRevenue
    );

    // Dönemsel gelir
    const revenueByPeriod = await calculateRevenueByPeriod(membershipPackages);

    // En çok satan paketler
    const topPackages = await calculateTopPackages(
      supabase,
      membershipPackages
    );

    return {
      totalRevenue,
      monthlyRevenue,
      revenueGrowth,
      revenueByGym,
      revenueByPeriod,
      topPackages,
    };
  });
}

/**
 * Aktif üyelik paketlerini getirir
 */
async function getMembershipPackages(
  supabase: SupabaseClient,
  gymIds: string[]
): Promise<any[]> {
  const { data, error } = await supabase
    .from('gym_membership_packages')
    .select(
      `
        *,
        memberships!inner(
          gym_id,
          status
        )
      `
    )
    .in('memberships.gym_id', gymIds)
    .eq('status', 'active');

  if (error) {
    throw new Error(`Paket verileri getirilirken hata: ${error.message}`);
  }

  return data || [];
}

/**
 * Toplam geliri hesaplar
 */
function calculateTotalRevenue(membershipPackages: any[]): number {
  return membershipPackages.reduce(
    (sum, pkg) => sum + (pkg.purchase_price || 0),
    0
  );
}

/**
 * Aylık geliri hesaplar (son 30 gün)
 */
function calculateMonthlyRevenue(membershipPackages: any[]): number {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  return membershipPackages
    .filter(pkg => pkg.created_at && new Date(pkg.created_at) >= thirtyDaysAgo)
    .reduce((sum, pkg) => sum + (pkg.purchase_price || 0), 0);
}

/**
 * Gelir büyüme oranını hesaplar
 */
async function calculateRevenueGrowth(
  supabase: SupabaseClient,
  gymIds: string[],
  currentMonthRevenue: number
): Promise<number> {
  // Geçen ayın gelirini hesapla
  const sixtyDaysAgo = new Date();
  sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const { data: lastMonthPackages } = await supabase
    .from('gym_membership_packages')
    .select(
      `
        purchase_price,
        created_at,
        memberships!inner(gym_id)
      `
    )
    .in('memberships.gym_id', gymIds)
    .eq('status', 'active')
    .gte('created_at', sixtyDaysAgo.toISOString())
    .lt('created_at', thirtyDaysAgo.toISOString());

  const lastMonthRevenue = (lastMonthPackages || []).reduce(
    (sum, pkg) => sum + (pkg.purchase_price || 0),
    0
  );

  if (lastMonthRevenue === 0) return 0;

  return ((currentMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100;
}

/**
 * Salon bazında geliri hesaplar
 */
function calculateRevenueByGym(
  gyms: Array<{ id: string; name: string }>,
  membershipPackages: any[],
  totalRevenue: number
): Array<{
  gymId: string;
  gymName: string;
  revenue: number;
  percentage: number;
}> {
  return gyms
    .map(gym => {
      const gymPackages = membershipPackages.filter(
        pkg => pkg.memberships?.gym_id === gym.id
      );
      const gymRevenue = gymPackages.reduce(
        (sum, pkg) => sum + (pkg.purchase_price || 0),
        0
      );
      const percentage =
        totalRevenue > 0 ? (gymRevenue / totalRevenue) * 100 : 0;

      return {
        gymId: gym.id,
        gymName: gym.name,
        revenue: gymRevenue,
        percentage,
      };
    })
    .sort((a, b) => b.revenue - a.revenue);
}

/**
 * Dönemsel geliri hesaplar (son 6 ay)
 */
async function calculateRevenueByPeriod(
  membershipPackages: any[]
): Promise<Array<{ period: string; revenue: number; memberCount: number }>> {
  const revenueByPeriod = [];

  for (let i = 5; i >= 0; i--) {
    const date = new Date();
    date.setMonth(date.getMonth() - i);
    const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
    const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

    const monthPackages = membershipPackages.filter(pkg => {
      if (!pkg.created_at) return false;
      const createdAt = new Date(pkg.created_at);
      return createdAt >= monthStart && createdAt <= monthEnd;
    });

    const monthRevenue = monthPackages.reduce(
      (sum, pkg) => sum + (pkg.purchase_price || 0),
      0
    );

    const memberCount = new Set(monthPackages.map(pkg => pkg.membership_id))
      .size;

    revenueByPeriod.push({
      period: date.toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
      }),
      revenue: monthRevenue,
      memberCount,
    });
  }

  return revenueByPeriod;
}

/**
 * En çok satan paketleri hesaplar
 */
async function calculateTopPackages(
  supabase: SupabaseClient,
  membershipPackages: any[]
): Promise<Array<{ packageName: string; sales: number; revenue: number }>> {
  // Paket ID'lerini topla
  const packageIds = [
    ...new Set(membershipPackages.map(pkg => pkg.gym_package_id)),
  ];
  const packageNamesMap = await getPackageNames(supabase, packageIds);

  // Paket satış istatistikleri
  const packageSales = new Map<
    string,
    { name: string; sales: number; revenue: number }
  >();

  membershipPackages.forEach(pkg => {
    const packageName =
      packageNamesMap.get(pkg.gym_package_id) || 'Bilinmeyen Paket';
    const existing = packageSales.get(packageName) || {
      name: packageName,
      sales: 0,
      revenue: 0,
    };
    existing.sales += 1;
    existing.revenue += pkg.purchase_price || 0;
    packageSales.set(packageName, existing);
  });

  return Array.from(packageSales.values())
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5)
    .map(pkg => ({
      packageName: pkg.name,
      sales: pkg.sales,
      revenue: pkg.revenue,
    }));
}

/**
 * Boş finansal özet döndürür
 */
function getEmptyFinancialSummary(): ManagerFinancialSummary {
  return {
    totalRevenue: 0,
    monthlyRevenue: 0,
    revenueGrowth: 0,
    revenueByGym: [],
    revenueByPeriod: [],
    topPackages: [],
  };
}
