'use client';

import { RouteError } from '@/components/errors/route-error';

export default function InvitationsErrorRoute({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <RouteError
      title="Davetler Yüklenemedi"
      description="Davetlerle ilgili bir hata <PERSON>."
      error={error}
      reset={reset}
      context={{ route: '/invitations' }}
    />
  );
}
