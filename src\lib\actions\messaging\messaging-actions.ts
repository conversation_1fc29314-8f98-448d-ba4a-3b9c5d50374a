'use server';

import { createClient } from '@/lib/supabase/server';
import { getSupabaseAdmin } from '@/lib/supabase/admin';
import { revalidatePath } from 'next/cache';
import {
  ChatMessageWithSender,
  SendMessageResponse,
  ConversationWithParticipants,
  CreateConversationResponse,
  MessageWithSender,
} from '@/types/messaging';
import { createClientNotification } from '@/lib/actions/notifications/client-notification-actions';

/**
 * Yeni mesaj gönder
 */
export async function sendMessage(
  conversationId: string,
  content: string,
  replyToId?: string
): Promise<SendMessageResponse> {
  try {
    const supabase = await createClient();

    // Kullanıcı kimlik doğrulama
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Kimlik doğrulama hatası' };
    }

    // <PERSON><PERSON><PERSON> ekle
    const { data: message, error } = await supabase
      .from('messages')
      .insert({
        conversation_id: conversationId,
        sender_id: user.id,
        content: content.trim(),
        reply_to_id: replyToId || null,
      })
      .select(
        `
        *,
        sender:profiles(id, full_name, avatar_url),
        reply_to:messages(
          *,
          sender:profiles(id, full_name, avatar_url)
        )
      `
      )
      .single();

    if (error) {
      console.error('Mesaj gönderme hatası:', error);
      return { success: false, error: 'Mesaj gönderilemedi' };
    }

    // Katılımcıların last_read_at'ini güncelle (gönderen hariç)
    await supabase
      .from('conversation_participants')
      .update({ last_read_at: new Date().toISOString() })
      .eq('conversation_id', conversationId)
      .eq('profile_id', user.id);

    // Diğer katılımcılara bildirim gönder
    try {
      const { data: otherParticipants } = await supabase
        .from('conversation_participants')
        .select('profile_id, profile:profiles(full_name)')
        .eq('conversation_id', conversationId)
        .neq('profile_id', user.id)
        .eq('is_active', true);

      if (otherParticipants && otherParticipants.length > 0) {
        const { data: senderProfile } = await supabase
          .from('profiles')
          .select('full_name')
          .eq('id', user.id)
          .single();

        const senderName = senderProfile?.full_name || 'Bilinmeyen Kullanıcı';

        // Her katılımcıya bildirim gönder
        for (const participant of otherParticipants) {
          await createClientNotification({
            recipientUserId: participant.profile_id,
            title: 'Yeni Mesaj',
            message: `${senderName}: ${content.length > 50 ? content.substring(0, 50) + '...' : content}`,
          });
        }
      }
    } catch (notificationError) {
      console.error('Mesaj bildirimi gönderilirken hata:', notificationError);
      // Bildirim hatası mesaj göndermeyi engellemez
    }

    revalidatePath('/dashboard/messages');
    return { success: true, message: message as ChatMessageWithSender };
  } catch (error) {
    console.error('Mesaj gönderme hatası:', error);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}

/**
 * Yeni konuşma oluştur
 */
export async function createConversation(
  participantIds: string[],
  name?: string
): Promise<CreateConversationResponse> {
  try {
    const supabase = await createClient();
    
    // Kullanıcı kimlik doğrulama
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Kimlik doğrulama hatası' };
    }

    // Kendini katılımcılara ekle
    const allParticipantIds = [...new Set([user.id, ...participantIds])];
    
    // Birebir konuşma kontrolü - optimize edilmiş
    if (allParticipantIds.length === 2 && !name) {
      const otherUserId = allParticipantIds.find(id => id !== user.id);
      if (otherUserId) {
        // Tek sorguda mevcut birebir konuşmayı kontrol et
        const { data: existingConversation } = await supabase
          .from('conversations')
          .select(`
            id,
            conversation_participants!inner(profile_id)
          `)
          .eq('type', 'direct')
          .eq('created_by', user.id)
          .eq('conversation_participants.profile_id', otherUserId)
          .eq('conversation_participants.is_active', true)
          .limit(1)
          .single();

        if (existingConversation) {
          return { success: true, conversation: { id: existingConversation.id } };
        }
      }
    }

    // Konuşmayı oluştur
    const { data: conversation, error: conversationError } = await supabase
      .from('conversations')
      .insert({
        name: name || null,
        type: 'direct', // Sadece direct mesaj destekleniyor
        created_by: user.id,
      })
      .select('*')
      .single();

    if (conversationError) {
      console.error('Konuşma oluşturma hatası:', conversationError);
      return { success: false, error: 'Konuşma oluşturulamadı' };
    }

    // Katılımcıları ekle
    const participantsData = allParticipantIds.map(id => ({
      conversation_id: conversation.id,
      profile_id: id,
    }));

    const { error: participantsError } = await supabase
      .from('conversation_participants')
      .insert(participantsData);

    if (participantsError) {
      console.error('Katılımcı ekleme hatası:', participantsError);
      // Konuşmayı sil
      await supabase.from('conversations').delete().eq('id', conversation.id);
      return { success: false, error: 'Katılımcılar eklenemedi' };
    }

    // Konuşmayı katılımcılarla birlikte getir
    const { data: conversationWithParticipants } = await supabase
      .from('conversations')
      .select(`
        *,
        participants:conversation_participants(
          *,
          profile:profiles(id, full_name, avatar_url, email)
        )
      `)
      .eq('id', conversation.id)
      .single();

    revalidatePath('/dashboard/messages');
    return { 
      success: true, 
      conversation: conversationWithParticipants as ConversationWithParticipants 
    };
  } catch (error) {
    console.error('Konuşma oluşturma hatası:', error);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}

/**
 * Kullanıcının konuşmalarını getir - Optimize edilmiş versiyon
 */
export async function getUserConversations(): Promise<ConversationWithParticipants[]> {
  try {
    const supabase = await createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return [];
    }

    // Önce kullanıcının katıldığı konuşmaları al
    const { data: userParticipations, error: participationError } = await supabase
      .from('conversation_participants')
      .select('conversation_id')
      .eq('profile_id', user.id)
      .eq('is_active', true);

    if (participationError || !userParticipations) {
      console.error('Katılım bilgileri alınamadı:', participationError);
      return [];
    }

    const conversationIds = userParticipations.map(p => p.conversation_id);

    if (conversationIds.length === 0) {
      return [];
    }

    // Konuşmaları al
    const { data: conversations, error } = await supabase
      .from('conversations')
      .select('*')
      .in('id', conversationIds)
      .order('last_message_at', { ascending: false });

    if (error) {
      console.error('Konuşmaları getirme hatası:', error);
      return [];
    }

    // Admin client ile katılımcı bilgilerini al
    const adminSupabase = getSupabaseAdmin();

    // Paralel olarak tüm katılımcıları ve son mesajları al
    const allConversationIds = conversations.map(c => c.id);

    // Tüm katılımcıları tek sorguda al
    const { data: allParticipants } = await adminSupabase
      .from('conversation_participants')
      .select(`
        conversation_id,
        profile_id,
        profile:profiles(id, full_name, avatar_url, email)
      `)
      .in('conversation_id', allConversationIds)
      .eq('is_active', true);

    // Tüm son mesajları tek sorguda al
    const { data: allLastMessages } = await adminSupabase
      .from('messages')
      .select(`
        conversation_id,
        id,
        content,
        created_at,
        sender:profiles(id, full_name, avatar_url)
      `)
      .in('conversation_id', allConversationIds)
      .order('created_at', { ascending: false });

    // Konuşma başına son mesajı grupla
    const lastMessagesByConversation = new Map();
    allLastMessages?.forEach(message => {
      if (!lastMessagesByConversation.has(message.conversation_id)) {
        lastMessagesByConversation.set(message.conversation_id, message);
      }
    });

    // Konuşma başına katılımcıları grupla
    const participantsByConversation = new Map();
    allParticipants?.forEach(participant => {
      if (!participantsByConversation.has(participant.conversation_id)) {
        participantsByConversation.set(participant.conversation_id, []);
      }
      participantsByConversation.get(participant.conversation_id).push(participant);
    });

    // Konuşmaları detaylarıyla birleştir
    const conversationsWithDetails = conversations.map(conversation => {
      const participants = participantsByConversation.get(conversation.id) || [];
      const lastMessage = lastMessagesByConversation.get(conversation.id);

      // Direct mesaj - diğer katılımcının adını kullan
      let displayName = conversation.name;
      if (participants.length > 0) {
        const otherParticipant = participants.find((p: any) => p.profile_id !== user.id);
        if (otherParticipant?.profile) {
          displayName = otherParticipant.profile.full_name ||
                       otherParticipant.profile.email ||
                       'Bilinmeyen Kullanıcı';
        }
      }

      return {
        ...conversation,
        name: displayName,
        participants,
        last_message: lastMessage,
      };
    });

    return conversationsWithDetails as ConversationWithParticipants[];
  } catch (error) {
    console.error('Konuşmaları getirme hatası:', error);
    return [];
  }
}

/**
 * Belirli bir konuşmanın mesajlarını getir - Optimize edilmiş versiyon
 */
export async function getConversationMessages(
  conversationId: string,
  limit: number = 50,
  offset: number = 0
): Promise<MessageWithSender[]> {
  try {
    const supabase = await createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return [];
    }

    // Tek sorguda hem erişim kontrolü hem de mesajları al
    const adminSupabase = getSupabaseAdmin();
    const { data: messages, error } = await adminSupabase
      .from('messages')
      .select(`
        *,
        sender:profiles(id, full_name, avatar_url),
        reply_to:messages(
          id,
          content,
          created_at,
          sender:profiles(id, full_name, avatar_url)
        ),
        conversation:conversations!inner(
          id,
          participants:conversation_participants!inner(profile_id)
        )
      `)
      .eq('conversation_id', conversationId)
      .eq('conversation.participants.profile_id', user.id)
      .eq('conversation.participants.is_active', true)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Mesajları getirme hatası:', error);
      return [];
    }

    if (!messages || messages.length === 0) {
      return [];
    }

    // Conversation bilgisini kaldır (sadece erişim kontrolü için kullanıldı)
    const cleanMessages = messages.map(({ conversation, ...message }) => message);

    return (cleanMessages as MessageWithSender[]).reverse();
  } catch (error) {
    console.error('Mesajları getirme hatası:', error);
    return [];
  }
}



/**
 * Kullanıcıları ara (mesajlaşma için) - mevcut konuşması olanları hariç tut
 */
export async function searchUsers(query: string, limit: number = 10) {
  try {
    const supabase = await createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return [];
    }

    // Kullanıcının mevcut konuşmalarındaki diğer katılımcıları al
    const { data: userParticipations } = await supabase
      .from('conversation_participants')
      .select('conversation_id')
      .eq('profile_id', user.id)
      .eq('is_active', true);

    let excludeUserIds = [user.id]; // Kendini her zaman hariç tut

    if (userParticipations && userParticipations.length > 0) {
      const conversationIds = userParticipations.map(p => p.conversation_id);

      // Bu konuşmalardaki diğer katılımcıları al
      const { data: existingParticipants } = await supabase
        .from('conversation_participants')
        .select('profile_id')
        .in('conversation_id', conversationIds)
        .neq('profile_id', user.id)
        .eq('is_active', true);

      if (existingParticipants) {
        const existingUserIds = existingParticipants.map(p => p.profile_id);
        excludeUserIds = [...excludeUserIds, ...existingUserIds];
      }
    }

    // Admin client ile kullanıcıları ara (mevcut konuşması olanları hariç tut)
    const adminSupabase = getSupabaseAdmin();
    const { data: users, error } = await adminSupabase
      .from('profiles')
      .select('id, full_name, avatar_url, email')
      .not('id', 'in', `(${excludeUserIds.join(',')})`)
      .or(`full_name.ilike.%${query}%, email.ilike.%${query}%`)
      .limit(limit);

    if (error) {
      console.error('Kullanıcı arama hatası:', error);
      return [];
    }

    return users || [];
  } catch (error) {
    console.error('Kullanıcı arama hatası:', error);
    return [];
  }
}

/**
 * Mesajı düzenle
 */
export async function editMessage(
  messageId: string,
  newContent: string
): Promise<{ success: boolean; error?: string; message?: MessageWithSender }> {
  try {
    const supabase = await createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Kimlik doğrulama hatası' };
    }

    // Mesajın sahibi olup olmadığını kontrol et
    const { data: existingMessage } = await supabase
      .from('messages')
      .select('sender_id')
      .eq('id', messageId)
      .single();

    if (!existingMessage || existingMessage.sender_id !== user.id) {
      return { success: false, error: 'Bu mesajı düzenleme yetkiniz yok' };
    }

    // Mesajı güncelle
    const { data: message, error } = await supabase
      .from('messages')
      .update({
        content: newContent.trim(),
        edited_at: new Date().toISOString(),
      })
      .eq('id', messageId)
      .select(`
        *,
        sender:profiles(id, full_name, avatar_url),
        reply_to:messages(
          *,
          sender:profiles(id, full_name, avatar_url)
        )
      `)
      .single();

    if (error) {
      console.error('Mesaj düzenleme hatası:', error);
      return { success: false, error: 'Mesaj düzenlenemedi' };
    }

    revalidatePath('/dashboard/messages');
    return { success: true, message: message as MessageWithSender };
  } catch (error) {
    console.error('Mesaj düzenleme hatası:', error);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}

/**
 * Mesajı sil
 */
export async function deleteMessage(
  messageId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Kimlik doğrulama hatası' };
    }

    // Mesajın sahibi olup olmadığını kontrol et
    const { data: existingMessage } = await supabase
      .from('messages')
      .select('sender_id')
      .eq('id', messageId)
      .single();

    if (!existingMessage || existingMessage.sender_id !== user.id) {
      return { success: false, error: 'Bu mesajı silme yetkiniz yok' };
    }

    // Mesajı sil
    const { error } = await supabase
      .from('messages')
      .delete()
      .eq('id', messageId);

    if (error) {
      console.error('Mesaj silme hatası:', error);
      return { success: false, error: 'Mesaj silinemedi' };
    }

    revalidatePath('/dashboard/messages');
    return { success: true };
  } catch (error) {
    console.error('Mesaj silme hatası:', error);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}
