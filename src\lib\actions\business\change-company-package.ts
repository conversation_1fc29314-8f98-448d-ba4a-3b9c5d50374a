'use server';

import { ApiResponse } from '@/types/global/api';
import { createAction } from '../core';
import { PLATFORM_PACKAGES_TAG } from '@/lib/cache/tags';
import { revalidateTag } from 'next/cache';

/**
 * Şirket paketi yükseltme / düşürme işlemi
 * @param newPackageId - Hedef platform paketi ID'si
 */
export async function changeCompanyPackage(
  newPackageId: string
): Promise<ApiResponse<{ success: boolean; message: string }>> {
  return createAction<{ success: boolean; message: string }>(
    async (_, supabase, userId) => {
      if (!newPackageId) {
        throw new Error('Yeni paket ID gereklidir.');
      }

      // Yöneticinin şirketini bul
      const { data: company, error: companyErr } = await supabase
        .from('companies')
        .select('id, platform_package_id, status')
        .eq('manager_profile_id', userId)
        .single();

      if (companyErr || !company) {
        throw new Error('Şirket bilgileri alınamadı.');
      }

      // Zaten istenen pakette mi?
      if (company.platform_package_id === newPackageId) {
        throw new Error('Şirket zaten bu paketi kullanıyor.');
      }

      // Hedef paket bilgisi
      const { data: targetPkg, error: pkgErr } = await supabase
        .from('platform_packages')
        .select('*')
        .eq('id', newPackageId)
        .eq('is_active', true)
        .single();

      if (pkgErr || !targetPkg) {
        throw new Error('Hedef paket bulunamadı veya aktif değil.');
      }

      // Varsayılan olarak tarihleri güncelle (bugünkü tarih başlangıç, süresine göre bitiş)
      const startDate = new Date();
      const endDate = new Date();
      if (targetPkg.duration === 'yearly') {
        endDate.setFullYear(endDate.getFullYear() + 1);
      } else if (targetPkg.duration === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1);
      } else if (targetPkg.duration === null) {
        // lifetime -> null bırak
      }

      const { error: updateErr } = await supabase
        .from('companies')
        .update({
          platform_package_id: newPackageId,
          subscription_start_date: startDate.toISOString(),
          subscription_end_date: targetPkg.duration
            ? endDate.toISOString()
            : null,
          status: 'active',
          updated_at: new Date().toISOString(),
        })
        .eq('id', company.id);

      if (updateErr) {
        throw new Error('Paket güncellenemedi: ' + updateErr.message);
      }

      // Cache invalidasyonu
      try {
        revalidateTag(PLATFORM_PACKAGES_TAG);
      } catch {}

      return {
        success: true,
        message: `${targetPkg.name} paketi başarıyla tanımlandı.`,
      };
    }
  );
}
