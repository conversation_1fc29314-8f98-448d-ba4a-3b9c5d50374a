'use client';

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Bell, X, Trash2 } from 'lucide-react';
import {
  getUnreadNotificationCount,
  getUserNotifications,
  deleteNotification,
  deleteAllNotifications,
  type Notification,
} from '@/lib/actions/notifications/notification-actions';
import { getManagerGymUnreadNotificationCount } from '@/lib/actions/notifications/notification-actions';
import { formatRelativeTime } from '@/lib/utils';

// Sabitler
const NOTIFICATION_LIMIT = 10;

// Notification item component - gör<PERSON><PERSON><PERSON><PERSON>e ve silme
const NotificationItem = ({
  notification,
  formatDate,
  onDelete,
}: {
  notification: Notification;
  formatDate: (dateString: string) => string;
  onDelete: (id: string) => void;
}) => (
  <DropdownMenuItem
    className={`hover:bg-accent/30 flex cursor-default flex-col items-start space-y-2 p-4 transition-colors ${
      !notification.is_read
        ? 'to-primary/5 border-primary/30 dark:to-primary/10 border-l-2 bg-gradient-to-r from-blue-50/80 dark:from-blue-950/30'
        : 'hover:bg-muted/30'
    }`}
  >
    <div className="flex w-full items-start justify-between gap-3">
      <div className="min-w-0 flex-1">
        <div className="mb-1 flex items-center gap-2">
          <p className="truncate text-sm leading-tight font-semibold">
            {notification.title}
          </p>
          {!notification.is_read && (
            <div className="bg-primary h-2 w-2 flex-shrink-0 animate-pulse rounded-full" />
          )}
        </div>
        <p className="text-muted-foreground text-xs leading-relaxed">
          {notification.message}
        </p>
      </div>
      <div className="flex flex-shrink-0 items-center gap-2">
        <span className="text-muted-foreground text-xs whitespace-nowrap">
          {notification.created_at && formatDate(notification.created_at)}
        </span>
        <Button
          variant="ghost"
          size="sm"
          onClick={e => {
            e.stopPropagation();
            onDelete(notification.id);
          }}
          className="h-6 w-6 p-0 opacity-60 transition-all duration-200 hover:bg-red-100 hover:text-red-600 hover:opacity-100 dark:hover:bg-red-950/20"
          title="Bildirimi sil"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    </div>
  </DropdownMenuItem>
);

export function NotificationsButton({ userId }: { userId: string }) {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [gymUnreadCount, setGymUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Veri yükleme fonksiyonu
  const loadNotificationData = useCallback(
    async (showLoading = false) => {
      if (!userId) return;

      if (showLoading) {
        setIsLoading(true);
      }

      try {
        const [notificationsResult, countResult, gymCountResult] =
          await Promise.all([
            getUserNotifications(NOTIFICATION_LIMIT),
            getUnreadNotificationCount(userId),
            getManagerGymUnreadNotificationCount(),
          ]);
        setNotifications(notificationsResult.data || []);
        setUnreadCount(countResult.data || 0);
        setGymUnreadCount(gymCountResult.data || 0);
      } catch (error) {
        console.error('Bildirim verileri yüklenirken hata:', error);
      } finally {
        if (showLoading) {
          setIsLoading(false);
        }
      }
    },
    [userId]
  );

  // Tek bildirim silme fonksiyonu
  const handleDeleteNotification = async (notificationId: string) => {
    try {
      const result = await deleteNotification({ notificationId, userId });
      if (result.success) {
        // Optimistic UI - bildirimi listeden kaldır
        setNotifications(prev => prev.filter(n => n.id !== notificationId));
        // Eğer silinecek bildirim okunmamışsa, sayıyı azalt
        const deletedNotification = notifications.find(
          n => n.id === notificationId
        );
        if (deletedNotification && !deletedNotification.is_read) {
          setUnreadCount(prev => Math.max(0, prev - 1));
        }
      } else {
        console.error('Bildirim silinemedi:', result.error);
      }
    } catch (error) {
      console.error('Bildirim silme hatası:', error);
    }
  };

  // Tüm bildirimleri silme fonksiyonu
  const handleDeleteAllNotifications = async () => {
    try {
      const result = await deleteAllNotifications(userId);
      if (result.success) {
        // Optimistic UI - tüm bildirimleri temizle
        setNotifications([]);
        setUnreadCount(0);
      } else {
        console.error('Bildirimler silinemedi:', result.error);
      }
    } catch (error) {
      console.error('Toplu bildirim silme hatası:', error);
    }
  };

  // Sayfa yenilendiğinde veri yükleme
  useEffect(() => {
    if (!userId) return;

    // Sadece sayfa yenilendiğinde veriyi yükle
    loadNotificationData(true);
  }, [userId, loadNotificationData]);

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex flex-col space-y-3 p-4">
          {/* Loading skeletons */}
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-start space-x-3">
              <div className="bg-muted/60 h-2 w-2 animate-pulse rounded-full" />
              <div className="flex-1 space-y-2">
                <div className="bg-muted/60 h-4 w-3/4 animate-pulse rounded" />
                <div className="bg-muted/40 h-3 w-full animate-pulse rounded" />
              </div>
              <div className="bg-muted/40 h-3 w-12 animate-pulse rounded" />
            </div>
          ))}
        </div>
      );
    }
    if (notifications.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <Bell className="text-muted-foreground/50 mb-3 h-8 w-8" />
          <p className="text-muted-foreground text-sm font-medium">
            Henüz bildirim yok
          </p>
          <p className="text-muted-foreground/70 text-xs">
            Yeni bildirimler burada görünecek
          </p>
        </div>
      );
    }
    return notifications.map(notification => (
      <NotificationItem
        key={notification.id}
        notification={notification}
        formatDate={formatRelativeTime}
        onDelete={handleDeleteNotification}
      />
    ));
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="hover:bg-accent/50 hover:ring-primary/20 relative h-9 w-9 transition-all duration-200 hover:ring-2"
        >
          <Bell className="h-5 w-5 transition-transform duration-200 hover:scale-110" />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 flex h-5 w-5 animate-pulse items-center justify-center rounded-full p-0 text-xs font-semibold shadow-lg"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
          <span className="sr-only">
            Bildirimler {unreadCount > 0 && `(${unreadCount} okunmamış)`}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-84" align="end" forceMount>
        <DropdownMenuLabel className="flex items-center justify-between p-4">
          <div className="flex items-center space-x-2">
            <Bell className="h-4 w-4" />
            <span className="font-semibold">Bildirimler</span>
            {unreadCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {unreadCount} yeni
              </Badge>
            )}
            {gymUnreadCount > 0 && (
              <Badge variant="outline" className="text-xs">
                Salon: {gymUnreadCount}
              </Badge>
            )}
          </div>
          {notifications.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDeleteAllNotifications}
              className="h-auto p-1.5 text-xs transition-colors hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-950/20"
              title="Tüm bildirimleri sil"
            >
              <Trash2 className="mr-1 h-3 w-3" />
              Tümünü Sil
            </Button>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <ScrollArea className="h-80">{renderContent()}</ScrollArea>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
