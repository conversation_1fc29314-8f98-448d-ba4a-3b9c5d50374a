/**
 * Common utility types
 *
 * Basic utility types used throughout the application
 */

/**
 * JSON value type - matches Supabase Json type
 */
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[];

/**
 * Make all properties optional
 */
export type Optional<T> = {
  [P in keyof T]?: T[P];
};

/**
 * Make specific properties optional
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Make specific properties required
 */
export type RequiredBy<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * Extract non-nullable properties
 */
export type NonNullable<T> = T extends null | undefined ? never : T;

/**
 * Deep partial type
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * ID type for database entities
 */
export type ID = string;

/**
 * Timestamp type for database dates
 */
export type Timestamp = string;

/**
 * Generic callback function type
 */
export type Callback<T = void> = (value: T) => void;

/**
 * Generic async callback function type
 */
export type AsyncCallback<T = void> = (value: T) => Promise<void>;

/**
 * Generic event handler type
 */
export type EventHandler<T = Event> = (event: T) => void;

/**
 * Generic async event handler type
 */
export type AsyncEventHandler<T = Event> = (event: T) => Promise<void>;
