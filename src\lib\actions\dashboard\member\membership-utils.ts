/**
 * Utility functions for membership operations
 * Following Clean Code principles - small, focused, single-responsibility functions
 */

import {
  MEMBERSHIP_ERROR_MESSAGES,
  MEMBERSHIP_DEFAULTS,
} from './membership-constants';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface GymData {
  id: string;
  name: string;
  address: string;
  phone: string;
  slug?: string;
}

export interface MembershipRecord {
  profile_id: string;
  gym_id: string;
  status: string;
  approved_at: string;
  created_at: string;
}

export interface PackageInfo {
  id: string;
  packageName: string;
  packageType: string | null;
  isActive: boolean | null;
  isExpired: boolean;
  purchasePrice: number;
  startDate: string;
  endDate: string | null;
}

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate user ID
 * Following Clean Code principles - single responsibility, clear naming
 */
export function validateUserId(userId: string): void {
  if (!userId || userId.trim() === '') {
    throw new Error(MEMBERSHIP_ERROR_MESSAGES.INVALID_USER_ID);
  }
}

/**
 * Validate gym ID
 */
export function validateGymId(gymId: string): void {
  if (!gymId || gymId.trim() === '') {
    throw new Error(MEMBERSHIP_ERROR_MESSAGES.INVALID_GYM_ID);
  }
}

/**
 * Validate package ID
 */
export function validatePackageId(packageId: string): void {
  if (!packageId || packageId.trim() === '') {
    throw new Error(MEMBERSHIP_ERROR_MESSAGES.INVALID_PACKAGE_ID);
  }
}

// ============================================================================
// DATA TRANSFORMATION FUNCTIONS
// ============================================================================

/**
 * Transform gym data from database to frontend format
 * Following Clean Code principles - focused transformation with clear naming
 */
export function transformGymData(gym: any): GymData {
  const gymData = Array.isArray(gym) ? gym[0] : gym;

  if (!gymData) {
    throw new Error('Gym data is required');
  }

  return {
    id: gymData.id,
    name: gymData.name,
    address: gymData.address,
    phone: gymData.gym_phone,
    slug: gymData.slug,
  };
}

/**
 * Create membership record data
 * Following Clean Code principles - clear data structure creation
 */
export function createMembershipRecordData(
  userId: string,
  gymId: string
): MembershipRecord {
  validateUserId(userId);
  validateGymId(gymId);

  const now = new Date().toISOString();

  return {
    profile_id: userId,
    gym_id: gymId,
    status: MEMBERSHIP_DEFAULTS.STATUS,
    approved_at: now,
    created_at: now,
  };
}

/**
 * Transform package data for frontend
 */
export function transformPackageData(packageData: any): PackageInfo {
  if (!packageData) {
    throw new Error('Package data is required');
  }

  return {
    id: packageData.id,
    packageName: packageData.name || packageData.packageName,
    packageType: packageData.package_type || packageData.packageType,
    isActive: packageData.is_active ?? packageData.isActive,
    isExpired: packageData.is_expired ?? packageData.isExpired ?? false,
    purchasePrice: packageData.purchase_price || packageData.purchasePrice || 0,
    startDate: packageData.start_date || packageData.startDate,
    endDate: packageData.end_date || packageData.endDate || null,
  };
}

// ============================================================================
// DATE UTILITY FUNCTIONS
// ============================================================================

/**
 * Check if package is expired
 * Following Clean Code principles - single responsibility, clear logic
 */
export function isPackageExpired(endDate: string | null): boolean {
  if (!endDate) return false;

  const end = new Date(endDate);
  const now = new Date();

  return end < now;
}

/**
 * Calculate days remaining for package
 */
export function calculateDaysRemaining(endDate: string | null): number {
  if (!endDate) return -1; // Unlimited

  const end = new Date(endDate);
  const now = new Date();

  if (end < now) return 0; // Expired

  const diffTime = end.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays;
}

/**
 * Format date for display
 */
export function formatDateForDisplay(dateString: string): string {
  if (!dateString) return '';

  const date = new Date(dateString);
  return date.toLocaleDateString('tr-TR');
}

// ============================================================================
// STATS CALCULATION FUNCTIONS
// ============================================================================

/**
 * Calculate total spent from memberships
 * Following Clean Code principles - focused calculation with clear purpose
 */
export function calculateTotalSpent(memberships: any[]): number {
  if (!Array.isArray(memberships)) return 0;

  return memberships.reduce((total, membership) => {
    if (!membership.packages || !Array.isArray(membership.packages)) {
      return total;
    }

    const membershipTotal = membership.packages.reduce(
      (packageTotal: number, pkg: any) => {
        const price = pkg.purchase_price || pkg.purchasePrice || 0;
        return packageTotal + price;
      },
      0
    );

    return total + membershipTotal;
  }, 0);
}

/**
 * Count active memberships
 */
export function countActiveMemberships(memberships: any[]): number {
  if (!Array.isArray(memberships)) return 0;

  return memberships.filter(
    membership => membership.status === MEMBERSHIP_DEFAULTS.STATUS
  ).length;
}

/**
 * Count passive memberships
 */
export function countPassiveMemberships(memberships: any[]): number {
  if (!Array.isArray(memberships)) return 0;

  return memberships.filter(membership => membership.status === 'passive')
    .length;
}

// ============================================================================
// ERROR HANDLING UTILITIES
// ============================================================================

/**
 * Create membership error with context
 * Following Clean Code principles - consistent error handling
 */
export function createMembershipError(
  baseMessage: string,
  details?: string
): Error {
  const message = details ? `${baseMessage}: ${details}` : baseMessage;
  return new Error(message);
}

/**
 * Handle database error for memberships
 */
export function handleMembershipDatabaseError(
  error: any,
  operation: string
): never {
  const message = error?.message || 'Unknown database error';
  throw createMembershipError(
    MEMBERSHIP_ERROR_MESSAGES.MEMBERSHIP_CREATION_FAILED,
    `${operation} - ${message}`
  );
}
