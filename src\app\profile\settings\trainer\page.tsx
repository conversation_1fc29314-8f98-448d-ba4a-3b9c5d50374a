import { TrainerInformation } from '@/components/profile-settings/TrainerInformation';
import { getTrainerDetail } from '@/lib/actions/user/trainer-actions';
import { getUserRoles } from '@/lib/auth/server-auth';
import { TrainerDetails } from '@/types/database/tables';
import { redirect } from 'next/navigation';

export default async function TrainerSettingsPage() {
  // Kullanıcının trainer rolü olup olmadığını kontrol et
  const userRoles = await getUserRoles();
  const isTrainer = userRoles.includes('trainer');

  // Trainer değilse profil sayfasına yönlendir
  if (!isTrainer) {
    redirect('/profile/settings/profile');
  }

  // Server action ile trainer details verilerini çek
  const trainerDetailsResult = await getTrainerDetail();

  const trainerDetails: TrainerDetails | null =
    trainerDetailsResult.success && trainerDetailsResult.data
      ? trainerDetailsResult.data
      : null;

  // Hata durumunda konsola log
  if (!trainerDetailsResult.success) {
    console.warn('Trainer details not found:', trainerDetailsResult.error);
  }

  return (
    <div className="flex-1 space-y-6 p-6 lg:p-8">
      {/* Sayfa başlığı */}
      <div className="space-y-2">
        <h1 className="text-2xl font-semibold tracking-tight">
          Antrenör Bilgileri
        </h1>
        <p className="text-muted-foreground">
          Uzmanlık alanınızı, sertifikalarınızı ve deneyimlerinizi belirtin
        </p>
      </div>

      {/* Antrenör bilgileri formu */}
      <div className="space-y-6">
        <TrainerInformation trainerDetails={trainerDetails} />
      </div>
    </div>
  );
}
