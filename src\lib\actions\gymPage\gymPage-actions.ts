'use server';

import { GymPackages } from '@/types/database/tables';
import { createAnonClient } from '@/lib/supabase/server';
import { createAction, createActionPublic } from '../core/core';
import { ApiResponse } from '@/types/global/api';

// Import utilities and constants
import {
  validateReviewData,
  createReviewRecordData,
  handleGymDatabaseError,
  type ReviewWithUser,
  type ReviewStats,
} from './gym-utils';

import { GYM_ERROR_MESSAGES, QUERY_CONFIG } from './gym-constants';

/**
 * Get gym packages
 * Following Clean Code principles - improved validation and error handling
 */
export async function getGymPackages(
  gymId: string
): Promise<ApiResponse<GymPackages[]>> {
  return await createActionPublic<GymPackages[]>(async (_, supabase) => {
    try {
      const { data: packages, error: packagesError } = await supabase
        .from('gym_packages')
        .select('*')
        .eq('gym_id', gymId)
        .order(QUERY_CONFIG.PACKAGES_ORDER_BY, {
          ascending: QUERY_CONFIG.ORDER_ASCENDING,
        });

      if (packagesError) {
        throw new Error(
          `${GYM_ERROR_MESSAGES.PACKAGES_FETCH_FAILED}: ${packagesError.message}`
        );
      }

      return packages || [];
    } catch (error) {
      throw new Error(
        error instanceof Error
          ? error.message
          : GYM_ERROR_MESSAGES.PACKAGES_FETCH_FAILED
      );
    }
  });
}

// Legacy type alias for backward compatibility
export interface ReviewSubmissionData {
  gym_id: string;
  rating: number;
  comment?: string;
}

/**
 * Submit user review for gym
 * Following Clean Code principles - improved validation and error handling
 */
export async function submitReview(
  reviewData: ReviewSubmissionData
): Promise<ApiResponse<any>> {
  return createAction<any>(
    async (_, supabase, userId) => {
      try {
        // Ensure user is authenticated at runtime (TS narrowing)
        if (!userId) {
          throw new Error('Giriş yapmanız gerekmektedir.');
        }
        // Validate review data using utility function
        const validation = validateReviewData(reviewData);
        if (!validation.isValid) {
          throw new Error(validation.error);
        }

        // Create review record using utility function
        const reviewRecord = createReviewRecordData(userId, reviewData);

        const { data: review, error: reviewError } = await supabase
          .from('gym_reviews')
          .insert(reviewRecord)
          .select()
          .single();

        if (reviewError) {
          handleGymDatabaseError(reviewError, 'submit review');
        }

        return review;
      } catch (error) {
        throw new Error(
          error instanceof Error
            ? error.message
            : GYM_ERROR_MESSAGES.REVIEW_SAVE_FAILED
        );
      }
    },
    {
      revalidatePaths: [`/gym/${reviewData.gym_id}`],
    }
  );
}

/**
 * Kullanıcının mevcut değerlendirmesini güncelle
 */
export async function updateReview(
  reviewId: string,
  updateData: { rating: number; comment?: string }
): Promise<ApiResponse<any>> {
  return createAction<any>(
    async (_, supabase, userId) => {
      const { rating, comment } = updateData;

      // Validasyon
      if (!rating || rating < 1 || rating > 5) {
        throw new Error('Geçersiz puan değeri');
      }

      // Değerlendirmenin kullanıcıya ait olduğunu kontrol et
      const { data: existingReview, error: checkError } = await supabase
        .from('gym_reviews')
        .select('profile_id, gym_id')
        .eq('id', reviewId)
        .single();

      if (checkError || !existingReview) {
        throw new Error('Değerlendirme bulunamadı');
      }

      if (existingReview.profile_id !== userId) {
        throw new Error('Bu değerlendirmeyi güncelleme yetkiniz yok');
      }

      // Değerlendirmeyi güncelle
      const { data: updatedReview, error: updateError } = await supabase
        .from('gym_reviews')
        .update({
          rating,
          comment: comment || null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', reviewId)
        .select()
        .single();

      if (updateError) {
        console.error('Review update error:', updateError);
        throw new Error('Değerlendirme güncellenirken bir hata oluştu');
      }

      return updatedReview;
    },
    {
      revalidatePaths: [`/gym/*`], // Tüm gym sayfalarını yenile
    }
  );
}

/**
 * Salon için tüm değerlendirmeleri getir
 */
export async function getReviewsByGymId(
  gymId: string,
  page: number = 1,
  limit: number = 10
): Promise<ApiResponse<{ reviews: ReviewWithUser[]; totalCount: number }>> {
  return await createActionPublic<{
    reviews: ReviewWithUser[];
    totalCount: number;
  }>(async (_, supabase) => {
    const offset = (page - 1) * limit;
    const { data: reviews, error: reviewsError } = await supabase
      .from('gym_reviews')
      .select(
        `
          id,
          rating,
          comment,
          created_at,
          updated_at,
          profile_id,
          gym_id,
          user:profiles!left(
            full_name
          )
        `
      )
      .eq('gym_id', gymId)
      .not('profile_id', 'is', null) // Sadece profile_id'si olan kayıtları al
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (reviewsError) {
      console.error('Reviews fetch error:', reviewsError);
      throw new Error('Değerlendirmeler getirilirken bir hata oluştu');
    }

    // Null user'lı kayıtları filtrele ve user array'ini düzelt
    const validReviews = (reviews || [])
      .filter((review: any) => review.user !== null)
      .map((review: any) => ({
        ...review,
        user: Array.isArray(review.user) ? review.user[0] : review.user,
      }));

    // Toplam sayıyı al (sadece profile_id'si olan kayıtlar)
    const { count, error: countError } = await supabase
      .from('gym_reviews')
      .select('*', { count: 'exact', head: true })
      .eq('gym_id', gymId)
      .not('profile_id', 'is', null);

    if (countError) {
      console.error('Reviews count error:', countError);
      throw new Error('Değerlendirme sayısı alınırken bir hata oluştu');
    }

    return {
      reviews: validReviews as ReviewWithUser[],
      totalCount: count || 0,
    };
  });
}

/**
 * Salon için değerlendirme istatistiklerini getir
 */
export async function getReviewStats(
  gymId: string
): Promise<ApiResponse<ReviewStats>> {
  return await createActionPublic<ReviewStats>(async (_, supabase) => {
    // Gym tablosundan ortalama puan ve toplam değerlendirme sayısını al
    const { data: gym, error: gymError } = await supabase
      .from('gyms')
      .select('average_rating, review_count')
      .eq('id', gymId)
      .single();

    if (gymError) {
      console.error('Gym stats error:', gymError);
      throw new Error('Salon bilgileri getirilirken bir hata oluştu');
    }

    return {
      averageRating: gym.average_rating || 0,
      totalReviews: gym.review_count || 0,
    };
  });
}

/**
 * Kullanıcının belirli salon için değerlendirmesini getir
 */
export async function getUserReviewForGym(
  gymId: string,
  userId: string
): Promise<ApiResponse<any | null>> {
  try {
    const supabase = await createAnonClient();

    const { data: review, error } = await supabase
      .from('gym_reviews')
      .select('*')
      .eq('gym_id', gymId)
      .eq('profile_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      // PGRST116 = no rows returned
      console.error('User review fetch error:', error);
      return {
        success: false,
        error: 'Değerlendirme getirilirken bir hata oluştu',
      };
    }

    return {
      success: true,
      data: review || null,
    };
  } catch (error) {
    console.error('Get user review error:', error);
    return {
      success: false,
      error: 'Değerlendirme getirilirken beklenmeyen bir hata oluştu',
    };
  }
}
