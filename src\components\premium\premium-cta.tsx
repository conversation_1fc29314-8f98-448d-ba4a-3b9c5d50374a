import Link from 'next/link';
import { AnimatedSection } from '@/components/ui/animated-section';
import { Button } from '@/components/ui/button';
import { ArrowRight, Sparkles, Zap } from 'lucide-react';

export function PremiumCTA() {
  return (
    <section className="relative isolate overflow-hidden py-24 md:py-32">
      {/* Enhanced background */}
      <div className="absolute inset-0 -z-10">
        <div className="from-primary/20 via-primary/10 absolute inset-0 bg-gradient-to-br to-transparent" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,hsl(var(--primary)/0.15),transparent_70%)]" />
        <div className="bg-grid-pattern absolute inset-0 opacity-20" />
      </div>

      <div className="container mx-auto px-4">
        <AnimatedSection animation="fade-in">
          <div className="mx-auto max-w-4xl text-center">
            {/* Premium badge */}
            <div className="border-primary/20 bg-primary/10 text-primary mb-8 inline-flex items-center gap-2 rounded-full border px-6 py-3 text-sm font-medium backdrop-blur-sm">
              <Sparkles className="h-4 w-4" />
              <span>Hemen Başlayın</span>
            </div>

            <h2 className="from-foreground via-foreground to-foreground/80 mb-6 bg-gradient-to-r bg-clip-text text-4xl font-bold text-balance text-transparent md:text-6xl">
              Sportiva ile tanışın
            </h2>
            <p className="text-muted-foreground mx-auto mt-6 max-w-[70ch] text-xl leading-relaxed">
              Kurumsal güvenlik, gelişmiş raporlama ve otomasyonla
              operasyonunuzu bir üst seviyeye taşıyın.
            </p>

            <div className="mt-12 flex flex-col items-center gap-4 sm:flex-row sm:justify-center">
              <Button
                asChild
                size="lg"
                className="group h-14 px-8 text-lg font-semibold shadow-xl transition-all duration-300 hover:shadow-2xl"
              >
                <Link
                  href="/auth/register"
                  aria-label="Ücretsiz denemeyi başlat"
                >
                  <Zap className="mr-2 h-5 w-5" />
                  Ücretsiz Denemeyi Başlat
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              <Button
                asChild
                size="lg"
                variant="outline"
                className="hover:bg-primary/5 h-14 border-2 px-8 text-lg font-semibold"
              >
                <Link href="/onboarding" aria-label="Planları görüntüle">
                  Planları Gör
                </Link>
              </Button>
            </div>

            {/* Trust indicators */}
            <div className="text-muted-foreground mt-12 flex flex-col items-center gap-6 text-sm sm:flex-row sm:justify-center">
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 rounded-full bg-green-500" />
                <span>14 gün ücretsiz deneme</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 rounded-full bg-blue-500" />
                <span>Kredi kartı gerekmez</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 rounded-full bg-purple-500" />
                <span>Anında kurulum</span>
              </div>
            </div>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}
