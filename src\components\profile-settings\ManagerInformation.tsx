'use client';

import { useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Crown } from 'lucide-react';
import { ManagerSubWithPackageDetails } from '@/lib/actions/user/profile-actions';

interface ManagerInformationProps {
  managerDetails: ManagerSubWithPackageDetails | null;
}

export const ManagerInformation = ({
  managerDetails,
}: ManagerInformationProps) => {
  // Abonelik durumu hesaplama
  const subscriptionStatus = useMemo(() => {
    if (!managerDetails?.company.subscription_end_date) {
      return { status: 'passive', daysLeft: 0, isExpired: true };
    }

    const endDate = new Date(managerDetails.company.subscription_end_date);
    const now = new Date();
    const diffTime = endDate.getTime() - now.getTime();
    const daysLeft = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return {
      status: daysLeft > 0 ? 'active' : 'expired',
      daysLeft: Math.max(0, daysLeft),
      isExpired: daysLeft <= 0,
    };
  }, [managerDetails?.company.subscription_end_date]);

  return (
    <div className="space-y-6">
      {/* Paket Bilgileri */}
      {managerDetails?.company.status === 'active' ? (
        <Card>
          <CardContent className="p-8">
            {/* Paket Adı - Büyük ve Öne Çıkan */}
            <div className="mb-8 text-center">
              <div className="mb-4 flex items-center justify-center gap-3">
                <Crown className="text-primary h-8 w-8" />
                <h2 className="text-primary text-3xl font-bold">
                  {managerDetails.package.name}
                </h2>
              </div>
              <p className="text-muted-foreground">
                {subscriptionStatus.isExpired
                  ? 'Aboneliğinizin süresi dolmuş'
                  : `${subscriptionStatus.daysLeft} gün kaldı`}
              </p>
            </div>

            {/* Paket Özellikleri ve Abonelik Detayları */}
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              {/* Paket Özellikleri */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Paket Özellikleri</h3>
                <div className="space-y-3">
                  <div className="border-border/50 flex items-center justify-between border-b py-2">
                    <span className="text-muted-foreground">
                      Maksimum Salon
                    </span>
                    <span className="font-medium">
                      {managerDetails.package.max_gyms || 'Sınırsız'}
                    </span>
                  </div>
                  <div className="border-border/50 flex items-center justify-between border-b py-2">
                    <span className="text-muted-foreground">Maksimum Üye</span>
                    <span className="font-medium">
                      {managerDetails.package.max_members || 'Sınırsız'}
                    </span>
                  </div>
                  <div className="border-border/50 flex items-center justify-between border-b py-2">
                    <span className="text-muted-foreground">Paket Tipi</span>
                    <span className="font-medium capitalize">
                      {managerDetails.package.tier}
                    </span>
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <span className="text-muted-foreground">Ücret</span>
                    <span className="font-medium">
                      ₺{managerDetails.package.price}
                    </span>
                  </div>
                </div>
              </div>

              {/* Abonelik Detayları */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Abonelik Detayları</h3>
                <div className="space-y-3">
                  <div className="border-border/50 flex items-center justify-between border-b py-2">
                    <span className="text-muted-foreground">
                      Başlangıç Tarihi
                    </span>
                    <span className="font-medium">
                      {managerDetails.company.subscription_start_date
                        ? new Date(
                            managerDetails.company.subscription_start_date
                          ).toLocaleDateString('tr-TR')
                        : 'Belirtilmemiş'}
                    </span>
                  </div>
                  <div className="border-border/50 flex items-center justify-between border-b py-2">
                    <span className="text-muted-foreground">Bitiş Tarihi</span>
                    <span className="font-medium">
                      {managerDetails.company.subscription_end_date
                        ? new Date(
                            managerDetails.company.subscription_end_date
                          ).toLocaleDateString('tr-TR')
                        : 'Belirtilmemiş'}
                    </span>
                  </div>
                  <div className="border-border/50 flex items-center justify-between border-b py-2">
                    <span className="text-muted-foreground">Durum</span>
                    <Badge
                      variant={
                        subscriptionStatus.isExpired ? 'destructive' : 'default'
                      }
                      className="capitalize"
                    >
                      {subscriptionStatus.isExpired ? 'Süresi Dolmuş' : 'Aktif'}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <span className="text-muted-foreground">
                      Faturalandırma
                    </span>
                    <span className="font-medium capitalize">
                      {managerDetails.package.duration === 'yearly'
                        ? 'Yıllık'
                        : 'Ömür boyu'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Paket Yükselt Butonu */}
            <div className="mt-8 text-center">
              <Button
                asChild
                size="lg"
                className="from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 rounded-lg bg-gradient-to-r px-8 py-3 font-semibold text-white shadow-lg transition-all duration-200 hover:shadow-xl"
              >
                <Link href="/onboarding">
                  <Crown className="mr-2 h-5 w-5" />
                  Paketimi Yükselt
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card className="border-2 border-dashed">
          <CardContent className="p-8 text-center">
            <Crown className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
            <h3 className="mb-2 text-lg font-semibold">
              Aktif Abonelik Bulunamadı
            </h3>
            <p className="text-muted-foreground mb-6 text-sm">
              Salon yönetimi özelliklerini kullanmak için bir abonelik planı
              seçmeniz gerekiyor.
            </p>
            <Button
              asChild
              size="lg"
              className="from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 rounded-lg bg-gradient-to-r px-8 py-3 font-semibold text-white shadow-lg transition-all duration-200 hover:shadow-xl"
            >
              <Link href="/onboarding">
                <Crown className="mr-2 h-5 w-5" />
                Planları İncele
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
