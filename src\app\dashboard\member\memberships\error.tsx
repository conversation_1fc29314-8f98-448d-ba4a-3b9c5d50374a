'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { logger } from '@/lib/logger';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import Link from 'next/link';
import { useEffect } from 'react';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function MembershipsError({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Error'u log'la (production'da error tracking service'e gönderebilirsin)
    logger.error('Memberships page error', error, {
      digest: error.digest,
      component: 'MembershipsPage',
    });
  }, [error]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Üyeliklerim & Paketlerim
          </h1>
          <p className="text-muted-foreground">
            Salon üyeliklerinizi, satın aldığınız paketleri ve ödeme
            bilgilerinizi görüntüleyin
          </p>
        </div>
        <div className="flex gap-2">
          <Link href="/dashboard/member/invitations">
            <Button variant="outline">Salon Davetleri</Button>
          </Link>
          <Link href="/findGym">
            <Button>Yeni Salon Bul</Button>
          </Link>
        </div>
      </div>

      <Card className="border-red-200">
        <CardContent className="p-8">
          <div className="space-y-4 text-center">
            <div className="flex justify-center">
              <AlertTriangle className="h-12 w-12 text-red-500" />
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-red-900">
                Bir Hata Oluştu
              </h3>
              <p className="mx-auto max-w-md text-red-700">
                Üyelikleriniz yüklenirken bir sorun oluştu. Lütfen tekrar
                deneyin.
              </p>
              {process.env.NODE_ENV === 'development' && (
                <details className="mt-4 text-left">
                  <summary className="cursor-pointer text-sm text-red-600 hover:text-red-800">
                    Hata Detayları (Geliştirici Modu)
                  </summary>
                  <pre className="mt-2 overflow-auto rounded border bg-red-50 p-3 text-xs">
                    {error.message}
                    {error.digest && `\nDigest: ${error.digest}`}
                  </pre>
                </details>
              )}
            </div>

            <div className="flex flex-col justify-center gap-3 sm:flex-row">
              <Button
                onClick={reset}
                variant="outline"
                className="border-red-300 text-red-700 hover:bg-red-50"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Tekrar Dene
              </Button>

              <Link href="/dashboard">
                <Button variant="secondary">Dashboard&apos;a Dön</Button>
              </Link>

              <Link href="/findGym">
                <Button>Salon Ara</Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Yardım Bölümü */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-3 text-center">
            <h4 className="font-medium">Sorun Devam Ediyor mu?</h4>
            <p className="text-muted-foreground text-sm">
              Eğer bu hata devam ederse, lütfen destek ekibimizle iletişime
              geçin.
            </p>
            <div className="flex justify-center gap-3">
              <Button variant="outline" size="sm" asChild>
                <Link href="/support">Destek Al</Link>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href="/contact">İletişim</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
