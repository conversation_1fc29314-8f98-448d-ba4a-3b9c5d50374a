

// Yeni basit chat sistemi tipleri
export type Chat = {
  id: string;
  user1_id: string;
  user2_id: string;
  created_at: string;
  updated_at: string;
};

export type ChatMessage = {
  id: string;
  chat_id: string;
  sender_id: string;
  content: string;
  created_at: string;
  updated_at: string | null;
};


// Genişletilmiş tipler
export interface ChatWithOtherUser extends Chat {
  other_user: {
    id: string;
    email: string;
    full_name: string | null;
    avatar_url: string | null;
  };
  last_message?: {
    content: string;
    created_at: string;
  };
}

export interface ChatMessageWithSender extends ChatMessage {
  sender: {
    id: string;
    email: string;
    full_name: string | null;
    avatar_url: string | null;
  } | null;
  // UI durumları (optimistic update için)
  status?: MessageStatus;
  isTemporary?: boolean;
  // Yanıt <PERSON>ğ<PERSON>
  reply_to?: {
    id: string;
    content: string;
    sender: {
      full_name: string | null;
    };
  } | null;
  // Düzenleme zamanı
  edited_at?: string | null;
}

// Mesaj durumları
export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'failed';

// Realtime event tipleri
export interface RealtimeMessageEvent {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new?: ChatMessage;
  old?: ChatMessage;
}

// Hook return tipleri
export interface UseMessagingReturn {
  conversations: ChatWithOtherUser[];
  activeConversation: ChatWithOtherUser | null;
  messages: ChatMessageWithSender[];
  isLoading: boolean;
  error: string | null;
  currentUser: any;
  setActiveConversation: (chat: ChatWithOtherUser | null) => void;
  sendMessage: (content: string) => Promise<void>;
  createConversation: (otherUserId: string) => Promise<string | undefined>;
  loadConversations: () => Promise<void>;
}



// Eski sistem ile uyumluluk için alias
export type MessageWithSender = ChatMessageWithSender;

// Conversation tipleri (eski sistem ile uyumluluk)
export interface ConversationWithParticipants {
  id: string;
  created_at: string;
  updated_at: string;
  participants: Array<{
    profile_id: string;
    profile: {
      id: string;
      full_name: string | null;
      avatar_url: string | null;
    };
  }>;
  last_message?: {
    content: string;
    created_at: string;
    sender_id: string;
  };
}

// API Response tipleri
export interface SendMessageResponse {
  success: boolean;
  message?: ChatMessageWithSender;
  error?: string;
}

export interface CreateChatResponse {
  success: boolean;
  chat?: Chat;
  error?: string;
}

export interface CreateConversationResponse {
  success: boolean;
  conversation?: ConversationWithParticipants | { id: string };
  error?: string;
}

// Form tipleri
export interface SendMessageForm {
  content: string;
}

export interface CreateChatForm {
  otherUserId: string;
}

// Notification tipleri
export interface MessageNotification {
  id: string;
  title: string;
  body: string;
  chatId: string;
  senderId: string;
  timestamp: string;
}
