'use client';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  acceptInvitation,
  rejectInvitation,
  cancelInvitation,
} from '@/lib/actions/gym_invitations/invitation-actions';
import {
  AlertTriangle,
  Calendar,
  CheckCircle,
  Clock,
  Mail,
  MapPin,
  MessageSquare,
  Phone,
  Send,
  XCircle,
  Check,
  X,
  Trash2,
} from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { toast } from 'sonner';
import { formatDateTime } from '@/lib/utils';
import {
  IncomingInvitation,
  OutgoingInvitation,
} from '@/lib/actions/gym_invitations/invitation-types';

interface IncomingInvitationCardProps {
  invitation: IncomingInvitation;
}

interface OutgoingInvitationCardProps {
  invitation: OutgoingInvitation;
}

// Utility Functions
export const useInvitationUtils = () => {
  const getStatusBadge = useCallback((status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge className="border-yellow-200 bg-yellow-100 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200">
            <Clock className="mr-1 h-3 w-3" />
            Beklemede
          </Badge>
        );
      case 'accepted':
        return (
          <Badge className="border-green-200 bg-green-100 text-green-800 dark:border-green-800 dark:bg-green-900/50 dark:text-green-200">
            <CheckCircle className="mr-1 h-3 w-3" />
            Kabul Edildi
          </Badge>
        );
      case 'rejected':
        return (
          <Badge className="border-red-200 bg-red-100 text-red-800 dark:border-red-800 dark:bg-red-900/50 dark:text-red-200">
            <XCircle className="mr-1 h-3 w-3" />
            Reddedildi
          </Badge>
        );
      case 'expired':
        return (
          <Badge className="border-gray-200 bg-gray-100 text-gray-800 dark:border-gray-700 dark:bg-gray-800/50 dark:text-gray-200">
            <AlertTriangle className="mr-1 h-3 w-3" />
            Süresi Dolmuş
          </Badge>
        );
      default:
        return (
          <Badge
            variant="outline"
            className="border-gray-200 dark:border-gray-700"
          >
            {status}
          </Badge>
        );
    }
  }, []);

  return { getStatusBadge };
};

// Invitation Actions Component
interface InvitationActionsProps {
  invitationId: string;
  type: 'incoming' | 'outgoing';
  status: string;
  isExpired: boolean;
}

export function InvitationActions({
  invitationId,
  type,
  status,
  isExpired,
}: InvitationActionsProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleAccept = useCallback(async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      const result = await acceptInvitation(invitationId);
      if (result.success) {
        toast.success('Davet kabul edildi!');
      } else {
        toast.error(result.error || 'Bir hata oluştu');
      }
    } catch (error) {
      toast.error('Bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  }, [invitationId, isLoading]);

  const handleReject = useCallback(async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      const result = await rejectInvitation(invitationId);
      if (result.success) {
        toast.success('Davet reddedildi');
      } else {
        toast.error(result.error || 'Bir hata oluştu');
      }
    } catch (error) {
      toast.error('Bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  }, [invitationId, isLoading]);

  const handleCancel = useCallback(async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      const result = await cancelInvitation(invitationId);
      if (result.success) {
        toast.success('Davet iptal edildi');
      } else {
        toast.error(result.error || 'Bir hata oluştu');
      }
    } catch (error) {
      toast.error('Bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  }, [invitationId, isLoading]);

  // Gelen davetler için butonlar
  if (type === 'incoming') {
    if (status === 'pending' && !isExpired) {
      return (
        <div className="flex gap-2">
          <Button
            onClick={handleAccept}
            disabled={isLoading}
            className="flex-1 bg-green-600 hover:bg-green-700"
          >
            <Check className="mr-2 h-4 w-4" />
            Kabul Et
          </Button>
          <Button
            onClick={handleReject}
            disabled={isLoading}
            variant="outline"
            className="flex-1 border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
          >
            <X className="mr-2 h-4 w-4" />
            Reddet
          </Button>
        </div>
      );
    }
    return null;
  }

  // Giden davetler için butonlar
  if (type === 'outgoing') {
    if (status === 'pending' && !isExpired) {
      return (
        <Button
          onClick={handleCancel}
          disabled={isLoading}
          variant="outline"
          className="w-full border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Daveti İptal Et
        </Button>
      );
    }
    return null;
  }

  return null;
}

// Incoming Invitation Card Component
export function IncomingInvitationCard({
  invitation,
}: IncomingInvitationCardProps) {
  const { getStatusBadge } = useInvitationUtils();

  const { isExpired, isPending } = useMemo(() => {
    const expired = invitation.expires_at
      ? new Date(invitation.expires_at) < new Date()
      : false;
    const pending = invitation.status === 'pending' && !expired;
    return { isExpired: expired, isPending: pending };
  }, [invitation.expires_at, invitation.status]);

  const cardClassName = useMemo(() => {
    return `hover:shadow-lg hover:shadow-blue-100/50 dark:hover:shadow-blue-900/20 transition-all duration-300 ${
      isPending
        ? 'border-blue-200 dark:border-blue-800 bg-blue-50/30 dark:bg-blue-950/20'
        : 'border-gray-200 dark:border-gray-700'
    }`;
  }, [isPending]);

  return (
    <Card className={cardClassName}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <CardTitle className="text-xl">{invitation.gym.name}</CardTitle>
            <div className="text-muted-foreground flex items-center gap-2 text-sm">
              <Mail className="h-4 w-4" />
              <span>
                Salon Daveti (
                {invitation.role === 'member' ? 'Üye' : 'Antrenör'})
              </span>
            </div>
          </div>
          {getStatusBadge(invitation.status)}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Gym Info */}
        <div className="space-y-2">
          {invitation.gym.address && (
            <div className="text-muted-foreground flex items-center text-sm">
              <MapPin className="mr-2 h-4 w-4 flex-shrink-0" />
              <span>{invitation.gym.address}</span>
            </div>
          )}
          {invitation.gym.gym_phone && (
            <div className="text-muted-foreground flex items-center text-sm">
              <Phone className="mr-2 h-4 w-4 flex-shrink-0" />
              <span>{invitation.gym.gym_phone}</span>
            </div>
          )}
        </div>

        {/* Message */}
        {invitation.message && (
          <div className="bg-muted/50 dark:bg-muted/30 border-muted dark:border-muted/50 rounded-lg border p-3">
            <div className="flex items-start gap-2">
              <MessageSquare className="text-muted-foreground mt-0.5 h-4 w-4 flex-shrink-0" />
              <p className="text-foreground text-sm">{invitation.message}</p>
            </div>
          </div>
        )}

        {/* Date Info */}
        <div className="text-muted-foreground flex items-center justify-between text-sm">
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            <span>Gönderildi: {formatDateTime(invitation.created_at)}</span>
          </div>
          {invitation.expires_at && (
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>Son: {formatDateTime(invitation.expires_at)}</span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <InvitationActions
          invitationId={invitation.id}
          type="incoming"
          status={invitation.status}
          isExpired={isExpired}
        />

        {invitation.responded_at && (
          <div className="text-muted-foreground text-sm">
            Yanıtlandı: {formatDateTime(invitation.responded_at)}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Outgoing Invitation Card Component
export function OutgoingInvitationCard({
  invitation,
}: OutgoingInvitationCardProps) {
  const { getStatusBadge } = useInvitationUtils();

  const { isExpired, isPending } = useMemo(() => {
    const expired = invitation.expires_at
      ? new Date(invitation.expires_at) < new Date()
      : false;
    const pending = invitation.status === 'pending' && !expired;
    return { isExpired: expired, isPending: pending };
  }, [invitation.expires_at, invitation.status]);

  const cardClassName = useMemo(() => {
    return `hover:shadow-lg hover:shadow-green-100/50 dark:hover:shadow-green-900/20 transition-all duration-300 ${
      isPending
        ? 'border-green-200 dark:border-green-800 bg-green-50/30 dark:bg-green-950/20'
        : 'border-gray-200 dark:border-gray-700'
    }`;
  }, [isPending]);

  return (
    <Card className={cardClassName}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <CardTitle className="text-xl">{invitation.gym.name}</CardTitle>
            <div className="text-muted-foreground flex items-center gap-2 text-sm">
              <Send className="h-4 w-4" />
              <span>
                Katılma İsteği (
                {invitation.role === 'member' ? 'Üye' : 'Antrenör'})
              </span>
            </div>
          </div>
          {getStatusBadge(invitation.status)}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Gym Info */}
        <div className="space-y-2">
          {invitation.gym.address && (
            <div className="text-muted-foreground flex items-center text-sm">
              <MapPin className="mr-2 h-4 w-4 flex-shrink-0" />
              <span>{invitation.gym.address}</span>
            </div>
          )}
          {invitation.gym.gym_phone && (
            <div className="text-muted-foreground flex items-center text-sm">
              <Phone className="mr-2 h-4 w-4 flex-shrink-0" />
              <span>{invitation.gym.gym_phone}</span>
            </div>
          )}
        </div>

        {/* Message */}
        {invitation.message && (
          <div className="bg-muted/50 dark:bg-muted/30 border-muted dark:border-muted/50 rounded-lg border p-3">
            <div className="flex items-start gap-2">
              <MessageSquare className="text-muted-foreground mt-0.5 h-4 w-4 flex-shrink-0" />
              <p className="text-foreground text-sm">{invitation.message}</p>
            </div>
          </div>
        )}

        {/* Date Info */}
        <div className="text-muted-foreground flex items-center justify-between text-sm">
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            <span>Gönderildi: {formatDateTime(invitation.created_at)}</span>
          </div>
          {invitation.expires_at && (
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>Son: {formatDateTime(invitation.expires_at)}</span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <InvitationActions
          invitationId={invitation.id}
          type="outgoing"
          status={invitation.status}
          isExpired={isExpired}
        />

        {invitation.responded_at && (
          <div className="text-muted-foreground text-sm">
            Yanıtlandı: {formatDateTime(invitation.responded_at)}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
