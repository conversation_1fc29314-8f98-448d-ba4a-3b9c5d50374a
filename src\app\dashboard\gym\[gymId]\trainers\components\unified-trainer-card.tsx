'use client';
import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { toast } from 'sonner';
import {
  User,
  Shield,
  Calendar,
  Users,
  Package,
  Crown,
  UserCheck,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Loader2,
} from 'lucide-react';

import {
  TrainerPermissionLevel,
  getAllPermissionLevels,
  getPermissionLevel,
  getPermissionLevelDefinition,
} from '@/lib/constants/trainer-permission-levels';
import { getTrainerSpecializationLabel } from '@/lib/constants/trainer-constants';
import { RemoveTrainerButton } from '@/components/trainer/remove-trainer-button';
import {
  GymTrainerWithPermissions,
  TrainerPermissions,
  updateTrainerPermissionLevel,
  updateTrainerPermissions,
} from '@/lib/actions/dashboard/company/trainer-permissions';
import { useRouter } from 'next/navigation';
interface UnifiedTrainerCardProps {
  trainer: GymTrainerWithPermissions;
  gymId: string;
}
export function UnifiedTrainerCard({
  trainer,
  gymId,
}: UnifiedTrainerCardProps) {
  const router = useRouter();
  const [isExpanded, setIsExpanded] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [localPermissions, setLocalPermissions] = useState(trainer.permissions);
  const [hasPendingChanges, setHasPendingChanges] = useState(false);
  const permissionLevels = getAllPermissionLevels();
  const currentLevel = getPermissionLevel(localPermissions);
  const levelDefinition = getPermissionLevelDefinition(currentLevel);

  // Antrenör çıkarıldığında sayfayı yenile
  const handleTrainerRemoved = () => {
    router.refresh();
  };
  const updatePermission = (
    category: keyof TrainerPermissions,
    action: string,
    value: boolean
  ) => {
    const newPermissions = {
      ...localPermissions,
      [category]: {
        ...(localPermissions[category] as any),
        [action]: value,
      },
    };
    setLocalPermissions(newPermissions);
    setHasPendingChanges(true);
  };
  const savePermissions = async () => {
    setIsUpdating(true);
    try {
      const result = await updateTrainerPermissions({
        gymId,
        trainerId: trainer.id,
        permissions: localPermissions,
      });

      if (result.success) {
        toast.success('Yetkiler başarıyla güncellendi.');
        setHasPendingChanges(false);
      } else {
        toast.error(result.error || 'Yetkiler güncellenirken hata oluştu.');
      }
    } catch (error) {
      toast.error('Bir hata oluştu.');
    } finally {
      setIsUpdating(false);
    }
  };
  const updatePermissionLevel = async (level: TrainerPermissionLevel) => {
    setIsUpdating(true);
    try {
      const result = await updateTrainerPermissionLevel({
        gymId,
        trainerId: trainer.id,
        level,
      });

      if (result.success) {
        const newLevelDefinition = getPermissionLevelDefinition(level);
        setLocalPermissions(newLevelDefinition.permissions);
        setHasPendingChanges(false);
        toast.success(
          `Yetki seviyesi "${newLevelDefinition.name}" olarak güncellendi.`
        );
      } else {
        toast.error(
          result.error || 'Yetki seviyesi güncellenirken hata oluştu.'
        );
      }
    } catch (error) {
      toast.error('Bir hata oluştu.');
    } finally {
      setIsUpdating(false);
    }
  };
  const getPermissionIcon = (category: keyof TrainerPermissions) => {
    const icons = {
      appointments: <Calendar className="h-5 w-5" />,
      members: <Users className="h-5 w-5" />,
      packages: <Package className="h-5 w-5" />,
    };
    return icons[category] || <Shield className="h-5 w-5" />;
  };
  const getPermissionLabel = (category: keyof TrainerPermissions) => {
    const labels = {
      appointments: 'Randevular',
      members: 'Üyeler',
      packages: 'Paketler',
    };
    return labels[category] || category;
  };
  const getActionLabel = (action: string) => {
    const labels = {
      read: 'Görüntüle',
      create: 'Oluştur',
      update: 'Güncelle',
      delete: 'Sil',
    };
    return labels[action as keyof typeof labels] || action;
  };
  const getLevelIcon = (level: TrainerPermissionLevel) => {
    const icons = {
      basic: <User className="h-4 w-4" />,
      standard: <UserCheck className="h-4 w-4" />,
      advanced: <Crown className="h-4 w-4" />,
    };
    return icons[level] || <Shield className="h-4 w-4" />;
  };
  return (
    <Card className="border-border/40 bg-card hover:border-border/80 rounded-xl border shadow-sm transition-all duration-300 ease-in-out hover:shadow-lg">
      <CardContent className="p-4 sm:p-6">
        {/* Desktop Layout */}
        <div className="hidden items-start justify-between gap-4 sm:flex">
          <div className="flex min-w-0 flex-1 items-center gap-5">
            <Avatar className="border-primary/20 h-16 w-16 border-2 shadow-sm sm:h-18 sm:w-18">
              <AvatarFallback className="text-primary bg-gradient-to-br from-blue-50 to-blue-100 text-lg font-bold sm:text-xl">
                {trainer.trainer.full_name
                  .split(' ')
                  .map(n => n[0])
                  .join('')
                  .slice(0, 2)
                  .toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <h3 className="text-foreground mb-1 truncate text-lg font-bold sm:text-xl">
                {trainer.trainer.full_name}
              </h3>
              <p className="text-muted-foreground mb-3 truncate text-sm">
                {trainer.trainer.email}
              </p>
              <div className="flex flex-wrap gap-2">
                {trainer.trainer.specialization && (
                  <Badge variant="secondary" className="text-xs font-medium">
                    {getTrainerSpecializationLabel(
                      trainer.trainer.specialization
                    )}
                  </Badge>
                )}
                {trainer.trainer.experience_years && (
                  <Badge variant="outline" className="text-xs font-medium">
                    {trainer.trainer.experience_years} yıl deneyim
                  </Badge>
                )}
              </div>
            </div>
          </div>
          <div className="flex flex-shrink-0 flex-col items-end gap-3">
            <Badge
              variant={
                currentLevel === 'advanced'
                  ? 'destructive'
                  : currentLevel === 'standard'
                    ? 'default'
                    : 'secondary'
              }
              className="flex items-center gap-2 px-3 py-1.5 text-sm font-semibold"
            >
              {getLevelIcon(currentLevel)}
              <span>{levelDefinition.name}</span>
            </Badge>
            <div className="flex gap-2">
              <RemoveTrainerButton
                gymId={gymId}
                trainerId={trainer.trainer_profile_id}
                trainerName={trainer.trainer.full_name}
                onSuccess={handleTrainerRemoved}
              />
              <Button
                onClick={() => setIsExpanded(!isExpanded)}
                variant="ghost"
                size="icon"
                className="hover:bg-muted/80 h-9 w-9"
              >
                {isExpanded ? (
                  <ChevronUp className="h-5 w-5" />
                ) : (
                  <ChevronDown className="h-5 w-5" />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="space-y-4 sm:hidden">
          <div className="flex items-center gap-3">
            <Avatar className="border-primary/20 h-14 w-14 border-2 shadow-sm">
              <AvatarFallback className="text-primary bg-gradient-to-br from-blue-50 to-blue-100 text-lg font-bold">
                {trainer.trainer.full_name
                  .split(' ')
                  .map(n => n[0])
                  .join('')
                  .slice(0, 2)
                  .toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <h3 className="text-foreground mb-1 truncate text-lg font-bold">
                {trainer.trainer.full_name}
              </h3>
              <p className="text-muted-foreground truncate text-sm">
                {trainer.trainer.email}
              </p>
            </div>
            <Button
              onClick={() => setIsExpanded(!isExpanded)}
              variant="ghost"
              size="icon"
              className="hover:bg-muted/80 h-8 w-8 flex-shrink-0"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex flex-wrap gap-2">
              {trainer.trainer.specialization && (
                <Badge variant="secondary" className="text-xs font-medium">
                  {getTrainerSpecializationLabel(
                    trainer.trainer.specialization
                  )}
                </Badge>
              )}
              {trainer.trainer.experience_years && (
                <Badge variant="outline" className="text-xs font-medium">
                  {trainer.trainer.experience_years} yıl deneyim
                </Badge>
              )}
            </div>
            <RemoveTrainerButton
              gymId={gymId}
              trainerId={trainer.trainer_profile_id}
              trainerName={trainer.trainer.full_name}
              onSuccess={handleTrainerRemoved}
            />
          </div>

          <div className="flex justify-center">
            <Badge
              variant={
                currentLevel === 'advanced'
                  ? 'destructive'
                  : currentLevel === 'standard'
                    ? 'default'
                    : 'secondary'
              }
              className="flex items-center gap-2 px-3 py-1.5 text-sm font-semibold"
            >
              {getLevelIcon(currentLevel)}
              <span>{levelDefinition.name}</span>
            </Badge>
          </div>
        </div>
      </CardContent>
      {isExpanded && (
        <CardContent className="border-border/40 bg-muted/20 border-t p-4 sm:p-6">
          <div className="mb-4 flex flex-col gap-3 sm:mb-6 sm:flex-row sm:items-center sm:justify-between">
            <h4 className="text-foreground text-lg font-semibold">
              Yetki Ayarları
            </h4>
            {hasPendingChanges && (
              <Button
                onClick={savePermissions}
                disabled={isUpdating}
                size="sm"
                className="w-full bg-green-600 font-medium text-white hover:bg-green-700 sm:w-auto"
              >
                {isUpdating ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <CheckCircle className="mr-2 h-4 w-4" />
                )}
                Kaydet
              </Button>
            )}
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4">
            {Object.entries(localPermissions).map(([category, permissions]) => (
              <div
                key={category}
                className="border-border/50 bg-card rounded-xl border p-4 shadow-sm transition-shadow hover:shadow-md sm:p-5"
              >
                <div className="mb-3 flex items-center gap-3 sm:mb-4">
                  <div className="bg-primary/10 text-primary flex h-10 w-10 items-center justify-center rounded-xl sm:h-12 sm:w-12">
                    {getPermissionIcon(category as keyof TrainerPermissions)}
                  </div>
                  <h5 className="text-foreground text-sm font-semibold sm:text-base">
                    {getPermissionLabel(category as keyof TrainerPermissions)}
                  </h5>
                </div>
                <div className="space-y-3 sm:space-y-4">
                  {Object.entries(permissions as Record<string, boolean>).map(
                    ([action, enabled]) => (
                      <div
                        key={action}
                        className="flex items-center justify-between py-1"
                      >
                        <label
                          htmlFor={`${trainer.id}-${category}-${action}`}
                          className="text-muted-foreground cursor-pointer text-sm font-medium"
                        >
                          {getActionLabel(action)}
                        </label>
                        <Switch
                          id={`${trainer.id}-${category}-${action}`}
                          checked={enabled}
                          onCheckedChange={value =>
                            updatePermission(
                              category as keyof TrainerPermissions,
                              action,
                              value
                            )
                          }
                        />
                      </div>
                    )
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 sm:mt-8">
            <h5 className="text-foreground mb-3 text-lg font-semibold sm:mb-4">
              Yetki Seviyeleri
            </h5>
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4 lg:grid-cols-3">
              {permissionLevels.map(level => (
                <button
                  key={level.id}
                  onClick={() => !isUpdating && updatePermissionLevel(level.id)}
                  disabled={isUpdating}
                  className={`rounded-xl border-2 p-4 text-left transition-all hover:shadow-lg disabled:cursor-not-allowed disabled:opacity-50 sm:p-5 ${
                    currentLevel === level.id
                      ? 'border-primary bg-primary/5 shadow-md'
                      : 'border-border/50 bg-card hover:border-primary/50'
                  }`}
                >
                  <div className="mb-2 flex items-center gap-3 sm:mb-3">
                    <div
                      className={`flex h-8 w-8 items-center justify-center rounded-full sm:h-10 sm:w-10 ${level.color} text-white shadow-sm`}
                    >
                      {getLevelIcon(level.id)}
                    </div>
                    <h6 className="text-foreground text-sm font-bold sm:text-base">
                      {level.name}
                    </h6>
                  </div>
                  <p className="text-muted-foreground text-xs leading-relaxed sm:text-sm">
                    {level.description}
                  </p>
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
