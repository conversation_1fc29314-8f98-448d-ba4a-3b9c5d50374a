import type { NextConfig } from 'next';

// Helper function to extract hostname from Supabase URL
function getSupabaseHostname(): string {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (!supabaseUrl) {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL environment variable is required');
  }

  try {
    const url = new URL(supabaseUrl);
    return url.hostname;
  } catch (error) {
    throw new Error('Invalid NEXT_PUBLIC_SUPABASE_URL format');
  }
}

const nextConfig: NextConfig = {
  // Performance optimizations
  experimental: {
    useCache: true, // Enable stable 'use cache' API in Next.js 15.4
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-icons',
      'date-fns',
      'recharts',
      'react-day-picker',
    ],
    viewTransition: true,
    clientSegmentCache: true,
    browserDebugInfoInTerminal: true,
    devtoolSegmentExplorer: true,
    optimizeRouterScrolling: true,
    reactCompiler: true,
    ppr: 'incremental', // Incremental Partial Prerendering
    // npx react-compiler-healthcheck@latest
  },

  // Image optimization
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: getSupabaseHostname(),
        port: '',
        pathname: '/storage/**',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        pathname: '/a/**',
      },
    ],
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Compression
  compress: true,

  // Headers for security and performance
  async headers() {
    const isDev = process.env.NODE_ENV === 'development';
    const supabaseHostname = getSupabaseHostname();

    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=(), payment=()',
          },
          {
            key: 'Strict-Transport-Security',
            value: isDev ? '' : 'max-age=31536000; includeSubDomains; preload',
          },
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              isDev
                ? "script-src 'self' 'unsafe-eval' 'unsafe-inline'"
                : "script-src 'self' 'unsafe-inline' https://vercel.live",
              "style-src 'self' 'unsafe-inline'",
              `img-src 'self' data: blob: https://${supabaseHostname} https://lh3.googleusercontent.com`,
              "font-src 'self' data:",
              `connect-src 'self' https://${supabaseHostname} wss://${supabaseHostname}`,
              "media-src 'self'",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'none'",
              isDev ? 'upgrade-insecure-requests' : '',
            ]
              .filter(Boolean)
              .join('; '),
          },
        ].filter(header => header.value !== ''),
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=300, stale-while-revalidate=60',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
