import { Metadata } from 'next';
import { Suspense } from 'react';
import { transformIncomingInvitation, transformOutgoingInvitation } from '@/components/invitations';
import { getUserIncomingInvites, getUserOutgoingRequests } from '@/lib/actions/gym_invitations/invitation-actions';
import { IncomingInvitation, OutgoingInvitation } from '@/lib/actions/gym_invitations/invitation-types';
import { MemberInvitationsActions } from './components/member-invitations-actions';

export const metadata: Metadata = {
  title: 'Salon Davetleri - Sportiva',
  description:
    'Aldığınız ve gönderdiğiniz salon davetlerini görüntüleyin ve yönetin.',
};

// Server component to fetch invitations data
async function MemberInvitationsData() {
  try {
    const [incomingResult, outgoingResult] = await Promise.all([
      getUserIncomingInvites('member'),
      getUserOutgoingRequests('member'),
    ]);

    const incomingInvitations = (incomingResult.success ? incomingResult.data : []) as IncomingInvitation[];
    const outgoingInvitations = (outgoingResult.success ? outgoingResult.data : []) as OutgoingInvitation[];

    // Transform to unified format
    const unifiedIncoming = incomingInvitations.map(transformIncomingInvitation);
    const unifiedOutgoing = outgoingInvitations.map(transformOutgoingInvitation);

    return (
      <MemberInvitationsActions
        incomingInvitations={unifiedIncoming}
        outgoingInvitations={unifiedOutgoing}
      />
    );
  } catch (error) {
    console.error('Error fetching member invitations:', error);
    return (
      <MemberInvitationsActions
        incomingInvitations={[]}
        outgoingInvitations={[]}
      />
    );
  }
}

export default async function MemberInvitationsPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-6 border-b pb-6">
        <h1 className="text-3xl font-bold tracking-tight">Salon Davetleri</h1>
        <p className="mt-1 text-muted-foreground">
          Aldığınız ve gönderdiğiniz salon davetlerini görüntüleyin ve yönetin.
        </p>
      </div>

      {/* Invitations with Streaming */}
      <Suspense
        fallback={
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 animate-pulse rounded-lg bg-muted" />
            ))}
          </div>
        }
      >
        <MemberInvitationsData />
      </Suspense>
    </div>
  );
}
