'use server';

import { createClient } from '@/lib/supabase/server';
import { createNotification } from '@/lib/actions/notifications/notification-actions';

interface ActionResult {
  success: boolean;
  error?: string;
}

/**
 * Cancel appointment - for members and trainers
 */
export async function cancelAppointment(
  appointmentId: string
): Promise<ActionResult> {
  try {
    const supabase = await createClient();

    // Get current user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Oturum açmanız gerekiyor' };
    }

    // Update appointment status to cancelled
    const { data: cancelledAppointment, error: updateError } = await supabase
      .from('appointments')
      .update({
        status: 'cancelled',
        updated_at: new Date().toISOString(),
      })
      .eq('id', appointmentId)
      .select(
        `
        *,
        trainer_profile:profiles!appointments_trainer_profile_id_fkey(id, full_name),
        gym:gyms!appointments_gym_id_fkey(id, name)
      `
      )
      .single();

    if (updateError) {
      return { success: false, error: 'Randevu iptal edilemedi' };
    }

    // Cancel all participants as well
    const { data: cancelledParts, error: partErr } = await supabase
      .from('appointment_participants')
      .update({ status: 'cancelled' })
      .eq('appointment_id', appointmentId).select(`
        id,
        membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
          membership:gym_memberships!gym_membership_packages_membership_id_fkey(profile_id),
          gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(name)
        )
      `);
    if (partErr) {
      console.error('Katılımcılar iptal edilirken hata:', partErr);
    } else {
      try {
        const aptDate = (cancelledAppointment as any)?.appointment_date as
          | string
          | undefined;
        const gymId = (cancelledAppointment as any)?.gym_id as
          | string
          | undefined;
        const gymName = (cancelledAppointment as any)?.gym?.name as
          | string
          | undefined;
        const trainerName = (cancelledAppointment as any)?.trainer_profile
          ?.full_name as string | undefined;

        for (const p of cancelledParts || []) {
          const recipientUserId = (p as any)?.membership_package?.membership
            ?.profile_id as string | undefined;
          const packageName = (p as any)?.membership_package?.gym_package
            ?.name as string | undefined;
          if (!recipientUserId) continue;

          const parts: string[] = [];
          if (aptDate) parts.push(new Date(aptDate).toLocaleString('tr-TR'));
          if (gymName) parts.push(`Salon: ${gymName}`);
          if (trainerName) parts.push(`Antrenör: ${trainerName}`);
          if (packageName) parts.push(`Paket: ${packageName}`);
          const detail = parts.length ? ` (${parts.join(' • ')})` : '';

          await createNotification({
            recipientUserId,
            gymId,
            title: 'Randevu İptali',
            message: `Randevu iptal edildi${detail}`,
          });
        }
      } catch (e) {
        console.error('Randevu iptal bildirimleri gönderilirken hata:', e);
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Cancel appointment error:', error);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}

/**
 * Update appointment status - for trainers
 */
export async function updateAppointmentStatus(
  appointmentId: string,
  status: string
): Promise<ActionResult> {
  try {
    const supabase = await createClient();

    // Get current user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Oturum açmanız gerekiyor' };
    }

    // Update appointment status
    const { error: updateError } = await supabase
      .from('appointments')
      .update({
        status: status as any,
        updated_at: new Date().toISOString(),
      })
      .eq('id', appointmentId);

    if (updateError) {
      return { success: false, error: 'Randevu durumu güncellenemedi' };
    }

    // If appointment cancelled => cancel all participants
    if (status === 'cancelled') {
      const { error: partErr } = await supabase
        .from('appointment_participants')
        .update({ status: 'cancelled' })
        .eq('appointment_id', appointmentId);
      if (partErr) {
        console.error('Katılımcılar iptal edilirken hata:', partErr);
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Update appointment status error:', error);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}

/**
 * Update participant status - for trainers
 */
export async function updateAppointmentParticipantStatus(
  appointmentParticipantId: string,
  status: 'confirmed' | 'cancelled' | 'no_show' | 'completed'
): Promise<ActionResult> {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user)
      return { success: false, error: 'Oturum açmanız gerekiyor' };

    // Update
    const { data, error } = await supabase
      .from('appointment_participants')
      .update({ status })
      .eq('id', appointmentParticipantId)
      .select(
        '*, appointment:appointments!appointment_participants_appointment_id_fkey(id, appointment_date), membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(membership:gym_memberships!gym_membership_packages_membership_id_fkey(profile_id))'
      )
      .single();

    if (error || !data)
      return { success: false, error: 'Katılımcı durumu güncellenemedi' };

    // Bildirim: sadece cancelled için gönder
    try {
      if (status === 'cancelled') {
        const recipientUserId = (data as any)?.membership_package?.membership
          ?.profile_id as string | undefined;
        const aptDate = (data as any)?.appointment?.appointment_date as
          | string
          | undefined;

        // Zengin içerik için ekstra detaylar (bu minimal fonksiyonda join yok; sadece tarih eklenir)
        const parts: string[] = [];
        if (aptDate) parts.push(new Date(aptDate).toLocaleString('tr-TR'));
        const detail = parts.length ? ` (${parts.join(' • ')})` : '';

        if (recipientUserId) {
          await createNotification({
            recipientUserId,
            title: 'Randevu İptali',
            message: `Randevu katılımınız iptal edildi${detail}`,
          });
        }
      }
    } catch {}

    return { success: true };
  } catch (e) {
    console.error('Update participant status error:', e);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}
