'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Upload, X, Image as ImageIcon, Loader2 } from 'lucide-react';
import Image from 'next/image';
import { toast } from 'sonner';

interface ImageUploadProps {
  currentImageUrl?: string;
  onImageUpload: (file: File) => Promise<void>;
  onImageRemove?: () => void;
  isUploading?: boolean;
  disabled?: boolean;
  aspectRatio?: 'square' | 'video' | 'auto';
  maxSizeMB?: number;
  acceptedTypes?: string[];
  uploadText?: string;
  className?: string;
}

export function ImageUpload({
  currentImageUrl,
  onImageUpload,
  onImageRemove,
  isUploading = false,
  disabled = false,
  aspectRatio = 'auto',
  maxSizeMB = 2,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp'],
  uploadText = 'Görse<PERSON>',
  className,
}: ImageUploadProps) {
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (file: File) => {
    // Dosya boyutu kontrolü
    if (file.size > maxSizeMB * 1024 * 1024) {
      toast.error(`Dosya boyutu ${maxSizeMB}MB'dan küçük olmalıdır.`);
      return;
    }

    // Dosya tipi kontrolü
    if (!acceptedTypes.includes(file.type)) {
      toast.error(`Desteklenen formatlar: ${acceptedTypes.join(', ')}`);
      return;
    }

    await onImageUpload(file);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);

    if (disabled || isUploading) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      await handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled && !isUploading) {
      setDragActive(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleFileInputChange = async (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      await handleFileSelect(files[0]);
    }
  };

  const handleRemoveImage = () => {
    // File input'u temizle
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    // Parent component'teki state'i güncelle
    if (onImageRemove) {
      onImageRemove();
    }
  };

  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case 'square':
        return 'aspect-square';
      case 'video':
        return 'aspect-video';
      default:
        return '';
    }
  };

  return (
    <div className={cn('relative', className)}>
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedTypes.join(',')}
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled || isUploading}
      />

      <div
        className={cn(
          'rounded-lg border-2 border-dashed transition-colors',
          'hover:border-primary/50 cursor-pointer',
          'w-full overflow-hidden',
          dragActive && 'border-primary bg-primary/5',
          disabled && 'cursor-not-allowed opacity-50',
          getAspectRatioClass()
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() =>
          !disabled && !isUploading && fileInputRef.current?.click()
        }
      >
        {currentImageUrl ? (
          <div className="relative h-full min-h-[160px] w-full">
            <Image
              src={currentImageUrl}
              alt="Yüklenen görsel"
              fill
              className="rounded-lg object-cover"
            />
            {!disabled && !isUploading && (
              <Button
                type="button"
                variant="destructive"
                size="sm"
                className="absolute top-2 right-2 z-10 h-6 w-6 p-0"
                onClick={e => {
                  e.stopPropagation();
                  handleRemoveImage();
                }}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
            {isUploading && (
              <div className="absolute inset-0 z-10 flex items-center justify-center rounded-lg bg-black/50">
                <Loader2 className="h-6 w-6 animate-spin text-white" />
              </div>
            )}
          </div>
        ) : (
          <div className="flex min-h-[160px] flex-col items-center justify-center p-4 text-center sm:p-6">
            {isUploading ? (
              <>
                <Loader2 className="text-primary mb-2 h-6 w-6 animate-spin sm:h-8 sm:w-8" />
                <p className="text-muted-foreground text-xs sm:text-sm">
                  Yükleniyor...
                </p>
              </>
            ) : (
              <>
                <div className="bg-muted mb-2 flex h-10 w-10 items-center justify-center rounded-lg sm:mb-3 sm:h-12 sm:w-12">
                  <ImageIcon className="text-muted-foreground h-5 w-5 sm:h-6 sm:w-6" />
                </div>
                <p className="mb-1 text-xs font-medium sm:text-sm">
                  {uploadText}
                </p>
                <p className="text-muted-foreground mb-2 hidden text-xs sm:mb-3 sm:block">
                  Sürükle bırak veya tıkla
                </p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  disabled={disabled}
                  className="text-xs sm:text-sm"
                >
                  <Upload className="mr-1 h-3 w-3 sm:mr-2 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">Dosya Seç</span>
                  <span className="sm:hidden">Seç</span>
                </Button>
                <p className="text-muted-foreground mt-2 text-xs break-words">
                  Max: {maxSizeMB}MB
                  <br className="sm:hidden" />
                  <span className="hidden sm:inline">, </span>
                  {acceptedTypes.map(type => type.split('/')[1]).join(', ')}
                </p>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
