'use server';

import { createClient } from '@/lib/supabase/server';
import { createAction } from '../../core';
import { ApiResponse } from '@/types/global/api';
import {
  Appointments,
  AppointmentParticipants,
  GymMembershipPackages,
  GymPackages,
  Profiles,
} from '@/types/database/tables';
import { AppointmentStatus } from '@/lib/supabase/types';

// Types for responses
interface MemberAppointmentWithDetails extends AppointmentParticipants {
  appointment?: Appointments & {
    trainer_profile?: Profiles;
  };
  membership_package?: GymMembershipPackages & {
    gym_package?: GymPackages;
  };
}

/**
 * Get member's appointments
 */
export async function getMemberAppointments(
  memberId: string,
  filters?: {
    startDate?: string;
    endDate?: string;
    status?: AppointmentStatus;
    gymId?: string;
  }
): Promise<ApiResponse<MemberAppointmentWithDetails[]>> {
  try {
    const supabase = await createClient();

    // Get current user to ensure they can only see their own appointments
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user || memberId !== user.id) {
      return {
        success: false,
        error: 'Bu randevuları görüntüleme yetkiniz yok',
      };
    }

    // İlk olarak üyenin membership package'larını al
    const { data: membershipPackages, error: packagesError } = await supabase
      .from('gym_membership_packages')
      .select(
        `
        id,
        membership:gym_memberships!gym_membership_packages_membership_id_fkey(
          profile_id
        )
      `
      )
      .eq('membership.profile_id', memberId);

    if (packagesError) {
      return {
        success: false,
        error: `Üyelik paketleri getirilemedi: ${packagesError.message}`,
      };
    }

    if (!membershipPackages || membershipPackages.length === 0) {
      return { success: true, data: [] };
    }

    const packageIds = membershipPackages.map(pkg => pkg.id);

    // Appointment participants'ları al
    const participantsQuery = supabase
      .from('appointment_participants')
      .select('*')
      .in('gym_membership_package_id', packageIds)
      .order('created_at', { ascending: false });

    const { data: participants, error: participantsError } =
      await participantsQuery;

    if (participantsError) {
      return {
        success: false,
        error: `Katılımcı bilgileri getirilemedi: ${participantsError.message}`,
      };
    }

    if (!participants || participants.length === 0) {
      return { success: true, data: [] };
    }

    // Appointment'ları al
    const appointmentIds = participants.map(p => p.appointment_id);
    let appointmentsQuery = supabase
      .from('appointments')
      .select(
        `
        *,
        trainer_profile:profiles!appointments_trainer_profile_id_fkey(
          id,
          full_name,
          avatar_url
        )
      `
      )
      .in('id', appointmentIds)
      .order('appointment_date', { ascending: false });

    // Apply filters
    if (filters?.startDate) {
      appointmentsQuery = appointmentsQuery.gte(
        'appointment_date',
        filters.startDate
      );
    }
    if (filters?.endDate) {
      appointmentsQuery = appointmentsQuery.lte(
        'appointment_date',
        filters.endDate
      );
    }
    if (filters?.status) {
      appointmentsQuery = appointmentsQuery.eq('status', filters.status);
    }
    if (filters?.gymId) {
      appointmentsQuery = appointmentsQuery.eq('gym_id', filters.gymId);
    }

    const { data: appointments, error: appointmentsError } =
      await appointmentsQuery;

    if (appointmentsError) {
      return {
        success: false,
        error: `Randevular getirilemedi: ${appointmentsError.message}`,
      };
    }

    // Membership package detaylarını al
    const { data: packageDetails, error: packageDetailsError } = await supabase
      .from('gym_membership_packages')
      .select(
        `
        *,
        gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(
          name,
          package_type,
          session_duration_minutes
        )
      `
      )
      .in('id', packageIds);

    if (packageDetailsError) {
      console.warn(
        'Paket detayları getirilemedi:',
        packageDetailsError.message
      );
    }

    // Combine data
    const appointmentMap = (appointments || []).reduce(
      (acc, appointment) => {
        acc[appointment.id] = appointment;
        return acc;
      },
      {} as Record<string, any>
    );

    const packageMap = (packageDetails || []).reduce(
      (acc, pkg) => {
        acc[pkg.id] = pkg;
        return acc;
      },
      {} as Record<string, any>
    );

    const result = participants
      .filter(participant => appointmentMap[participant.appointment_id])
      .map(participant => ({
        ...participant,
        appointment: appointmentMap[participant.appointment_id],
        membership_package: packageMap[participant.gym_membership_package_id],
      })) as MemberAppointmentWithDetails[];

    return { success: true, data: result };
  } catch (error) {
    console.error('getMemberAppointments error:', error);
    return {
      success: false,
      error: `Randevular yüklenirken hata oluştu: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
    };
  }
}

/**
 * Get member's upcoming appointments (next 30 days)
 */
export async function getMemberUpcomingAppointments(
  memberId: string
): Promise<ApiResponse<MemberAppointmentWithDetails[]>> {
  return createAction<MemberAppointmentWithDetails[]>(
    async (_, supabase, userId) => {
      // Ensure member can only see their own appointments
      if (memberId !== userId) {
        throw new Error('Bu randevuları görüntüleme yetkiniz yok');
      }

      const now = new Date();
      const nextMonth = new Date();
      nextMonth.setDate(now.getDate() + 30);

      const { data, error } = await supabase
        .from('appointment_participants')
        .select(
          `
          *,
          appointment:appointments!appointment_participants_appointment_id_fkey(
            *,
            trainer_profile:profiles!appointments_trainer_profile_id_fkey(
              id,
              full_name,
              avatar_url
            )
          ),
          membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
            *,
            membership:gym_memberships!gym_membership_packages_membership_id_fkey(
              profile_id
            ),
            gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(
              name,
              package_type,
              session_duration_minutes
            )
          )
        `
        )
        .eq('membership_package.membership.profile_id', memberId)
        .eq('appointment.status', 'scheduled')
        .gte('appointment.appointment_date', now.toISOString())
        .lte('appointment.appointment_date', nextMonth.toISOString())
        .order('appointment.appointment_date', { ascending: true });

      if (error) {
        throw new Error(`Yaklaşan randevular getirilemedi: ${error.message}`);
      }

      return data as MemberAppointmentWithDetails[];
    },
    { requireAuth: true }
  );
}

/**
 * Get member's appointment history with statistics
 */
export async function getMemberAppointmentStats(
  memberId: string,
  period: 'month' | 'quarter' | 'year' = 'month'
): Promise<
  ApiResponse<{
    total: number;
    completed: number;
    cancelled: number;
    no_show: number;
    upcoming: number;
    sessionsUsed: number;
    totalSessionsPurchased: number;
  }>
> {
  return createAction<{
    total: number;
    completed: number;
    cancelled: number;
    no_show: number;
    upcoming: number;
    sessionsUsed: number;
    totalSessionsPurchased: number;
  }>(
    async (_, supabase, userId) => {
      // Ensure member can only see their own stats
      if (memberId !== userId) {
        throw new Error('Bu istatistikleri görüntüleme yetkiniz yok');
      }

      const now = new Date();
      let startDate: Date;

      switch (period) {
        case 'month':
          startDate = new Date();
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          startDate = new Date();
          startDate.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          startDate = new Date();
          startDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      // Get appointment participants data
      const { data: participantData, error: participantError } = await supabase
        .from('appointment_participants')
        .select(
          `
          *,
          appointment:appointments!appointment_participants_appointment_id_fkey(
            status,
            appointment_date
          ),
          membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
            membership:gym_memberships!gym_membership_packages_membership_id_fkey(
              profile_id
            )
          )
        `
        )
        .eq('membership_package.membership.profile_id', memberId)
        .gte('appointment.appointment_date', startDate.toISOString());

      if (participantError) {
        throw new Error(
          `İstatistikler getirilemedi: ${participantError.message}`
        );
      }

      // Get membership package stats
      const { data: packageData, error: packageError } = await supabase
        .from('gym_membership_packages')
        .select('total_sessions, used_sessions')
        .eq('membership.profile_id', memberId)
        .eq('status', 'active');

      if (packageError) {
        throw new Error(
          `Paket istatistikleri getirilemedi: ${packageError.message}`
        );
      }

      const stats = {
        total: participantData.length,
        completed: 0,
        cancelled: 0,
        no_show: 0,
        upcoming: 0,
        sessionsUsed: 0,
        totalSessionsPurchased: 0,
      };

      // Calculate appointment stats
      participantData.forEach(participant => {
        const appointment = participant.appointment;
        if (!appointment) return;

        if (appointment.status === 'completed') {
          stats.completed++;
        } else if (appointment.status === 'cancelled') {
          stats.cancelled++;
        } else if (appointment.status === 'no_show') {
          stats.no_show++;
        } else if (
          appointment.status === 'scheduled' &&
          new Date(appointment.appointment_date) > now
        ) {
          stats.upcoming++;
        }
      });

      // Calculate session stats
      packageData.forEach(pkg => {
        stats.totalSessionsPurchased += pkg.total_sessions || 0;
        stats.sessionsUsed += pkg.used_sessions || 0;
      });

      return stats;
    },
    { requireAuth: true }
  );
}

/**
 * Get member's active membership packages with remaining sessions
 */
export async function getMemberActivePackages(memberId: string): Promise<
  ApiResponse<
    (GymMembershipPackages & {
      gym_package?: GymPackages;
    })[]
  >
> {
  return createAction<
    (GymMembershipPackages & {
      gym_package?: GymPackages;
    })[]
  >(
    async (_, supabase, userId) => {
      // Ensure member can only see their own packages
      if (memberId !== userId) {
        throw new Error('Bu paketleri görüntüleme yetkiniz yok');
      }

      const { data, error } = await supabase
        .from('gym_membership_packages')
        .select(
          `
          *,
          gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(
            name,
            package_type,
            session_count,
            session_duration_minutes
          )
        `
        )
        .eq('membership.profile_id', memberId)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Aktif paketler getirilemedi: ${error.message}`);
      }

      const packages = (data || []) as (GymMembershipPackages & {
        gym_package?: GymPackages;
      })[];
      const ids = packages.map(p => p.id);
      const { data: stats, error: statsError } = await supabase
        .from('v_gym_membership_packages_with_stats')
        .select('id, computed_remaining_sessions')
        .in('id', ids);
      if (statsError) {
        throw new Error(
          `Paket istatistikleri getirilemedi: ${statsError.message}`
        );
      }
      const map = new Map<string, number>();
      (stats || []).forEach((row: any) =>
        map.set(row.id, row.computed_remaining_sessions ?? 0)
      );
      const enriched = packages
        .map(p => ({
          ...p,
          remaining_sessions: map.get(p.id) ?? p.remaining_sessions,
          computed_remaining_sessions: map.get(p.id) ?? p.remaining_sessions,
        }))
        .filter(
          p => (p.computed_remaining_sessions ?? p.remaining_sessions ?? 0) > 0
        );

      return enriched as (GymMembershipPackages & {
        gym_package?: GymPackages;
      })[];
    },
    { requireAuth: true }
  );
}
