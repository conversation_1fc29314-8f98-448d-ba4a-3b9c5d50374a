/**
 * Trainer dashboard types
 *
 * Extracted from trainer dashboard components
 */

/**
 * Trainer dashboard overview data
 */
export interface TrainerDashboardOverview {
  totalAppointments: number;
  todayAppointments: number;
  totalClients: number;
  monthlyEarnings: number;
  upcomingAppointments: {
    id: string;
    clientName: string;
    startTime: string;
    endTime: string;
    type: 'personal' | 'group';
    status: 'scheduled' | 'confirmed';
  }[];
}

/**
 * Trainer schedule data
 */
export interface TrainerSchedule {
  date: string;
  appointments: {
    id: string;
    startTime: string;
    endTime: string;
    clientName: string;
    clientId: string;
    type: 'personal' | 'group';
    status: 'scheduled' | 'completed' | 'cancelled';
    notes?: string;
  }[];
  availableSlots: {
    startTime: string;
    endTime: string;
    isBookable: boolean;
  }[];
}

/**
 * Client management data
 */
export interface ClientManagementData {
  id: string;
  fullName: string;
  email: string;
  phone?: string;
  joinDate: string;
  lastAppointment?: string;
  totalAppointments: number;
  fitnessGoals?: string[];
  notes?: string;
  status: 'active' | 'passive';
}

/**
 * Trainer performance metrics
 */
export interface TrainerPerformance {
  appointmentCompletionRate: number;
  clientRetentionRate: number;
  averageRating: number;
  totalReviews: number;
  monthlyAppointments: number;
  monthlyEarnings: number;
  performanceTrend: {
    month: string;
    appointments: number;
    earnings: number;
    rating: number;
  }[];
}

/**
 * Trainer availability settings
 */
export interface TrainerAvailability {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  isAvailable: boolean;
  startTime: string;
  endTime: string;
  breakTimes?: {
    startTime: string;
    endTime: string;
  }[];
}
