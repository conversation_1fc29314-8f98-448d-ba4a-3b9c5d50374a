import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Building2, Mail, MapPin, Calendar, MessageSquare, Send, User } from 'lucide-react';
import { formatDateTime } from '@/lib/utils';
import { InvitationStatusBadge } from './invitation-status-badge';
import { InvitationActions } from './invitation-actions';
import { getInvitationTypeLabel, isInvitationExpired } from './types';
import type { InvitationCardProps } from './types';

export function InvitationCard({ 
  invitation, 
  onAction, 
  showActions = true, 
  compact = false 
}: InvitationCardProps) {
  const expired = isInvitationExpired(invitation);
  const isPending = invitation.status === 'pending' && !expired;
  const isIncoming = invitation.direction === 'incoming';
  
  const typeLabel = getInvitationTypeLabel(invitation.type, invitation.role);
  
  const cardClassName = `transition-all duration-300 ${
    expired 
      ? 'opacity-60' 
      : isPending 
        ? isIncoming
          ? 'border-info/30 bg-info/5 hover:shadow-lg hover:shadow-info/10'
          : 'border-warning/30 bg-warning/5 hover:shadow-lg hover:shadow-warning/10'
        : 'hover:shadow-md'
  }`;

  return (
    <Card className={cardClassName}>
      <CardHeader className={compact ? 'pb-3' : undefined}>
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-2">
            <CardTitle className={`flex items-center ${compact ? 'text-lg' : 'text-xl'}`}>
              <Building2 className={`mr-2 ${compact ? 'h-4 w-4' : 'h-5 w-5'}`} />
              {invitation.gym.name}
            </CardTitle>
            
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              {isIncoming ? (
                <Mail className="h-4 w-4" />
              ) : (
                <Send className="h-4 w-4" />
              )}
              <span>{typeLabel}</span>
            </div>
            
            {invitation.gym.address && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <MapPin className="h-3 w-3" />
                <span>{invitation.gym.address}</span>
              </div>
            )}
          </div>
          
          <InvitationStatusBadge invitation={invitation} />
        </div>
      </CardHeader>

      <CardContent className={compact ? 'pt-0' : undefined}>
        <div className="space-y-3">
          {/* Profile info for outgoing invitations */}
          {invitation.profile && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <User className="h-3 w-3" />
              <span>{invitation.profile.full_name}</span>
              {invitation.profile.email && (
                <>
                  <span>•</span>
                  <span>{invitation.profile.email}</span>
                </>
              )}
            </div>
          )}
          
          {/* Message */}
          {invitation.message && (
            <div className="rounded-md bg-muted/50 p-3">
              <div className="flex items-start gap-2">
                <MessageSquare className="h-4 w-4 mt-0.5 text-muted-foreground" />
                <p className="text-sm">{invitation.message}</p>
              </div>
            </div>
          )}
          
          {/* Dates */}
          <div className="flex flex-wrap gap-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>Gönderildi: {formatDateTime(invitation.created_at)}</span>
            </div>
            
            {invitation.expires_at && (
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>
                  {expired ? 'Süresi doldu' : 'Son tarih'}: {formatDateTime(invitation.expires_at)}
                </span>
              </div>
            )}
            
            {invitation.responded_at && (
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>Yanıtlandı: {formatDateTime(invitation.responded_at)}</span>
              </div>
            )}
          </div>
          
          {/* Actions */}
          {showActions && (
            <div className="flex justify-end pt-2">
              <InvitationActions 
                invitation={invitation} 
                onAction={onAction} 
                compact={compact}
              />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
