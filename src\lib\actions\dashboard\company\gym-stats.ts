'use server';

import { createAction } from '../../core/core';
import { SupabaseClient } from '@supabase/supabase-js';
import { ApiResponse } from '@/types/global/api';
import { GymStats, GymRevenueReport } from './dashboard-types';
import { MembershipPackageStatus } from '@/lib/supabase/types';

/**
 * Salon istatistiklerini getirir
 * Following Clean Code principles - single responsibility for gym stats
 */
export async function getGymStats(
  gymId: string
): Promise<ApiResponse<GymStats>> {
  return createAction<GymStats>(async (_, supabase, _userId) => {
    return await computeGymStats(supabase, gymId);
  });
}

/**
 * Salon gelir raporunu getirir - tüm satış kayıtları
 * Following Clean Code principles - focused revenue reporting
 */
export async function getGymRevenueReport(
  gymId: string
): Promise<ApiResponse<GymRevenueReport>> {
  return createAction(async (_, supabase, _userId) => {
    const { data: revenueRecords, error } = await supabase
      .from('gym_membership_packages')
      .select(
        `
      id,
      purchase_price,
      start_date,
      end_date,
      status,
      created_at,
      gym_memberships!inner(
        profile_id,
        profiles!inner(full_name)
      ),
      gym_packages!inner(name)
    `
      )
      .eq('gym_id', gymId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Gelir kayıtları alınamadı: ${error.message}`);
    }

    const records = (revenueRecords || []).map((record: any) => ({
      id: record.id,
      memberName:
        record.gym_memberships?.profiles?.full_name || 'Bilinmeyen Üye',
      packageName: record.gym_packages?.name || 'Bilinmeyen Paket',
      purchasePrice: Number(record.purchase_price) || 0,
      startDate: record.start_date,
      endDate: record.end_date,
      status: record.status as MembershipPackageStatus,
      createdAt: record.created_at,
    }));

    const totalRevenue = records.reduce(
      (sum: number, record) => sum + record.purchasePrice,
      0
    );

    return {
      totalRevenue,
      totalRecords: records.length,
      records,
    };
  });
}

/**
 * Salon istatistiklerini (aktif üye sayısı, paket sayısı, toplam gelir) hesaplar.
 * Following Clean Code principles - parallel data fetching for performance
 */
async function computeGymStats(
  supabase: SupabaseClient,
  gymId: string
): Promise<GymStats> {
  // Paralel veri çekme için Promise.all kullan
  const [activeMembersResult, packagesResult, revenueResult] =
    await Promise.all([
      getActiveMembersCount(supabase, gymId),
      getTotalPackagesCount(supabase, gymId),
      getTotalRevenue(supabase, gymId),
    ]);

  return {
    activeMembers: activeMembersResult,
    totalPackages: packagesResult,
    totalRevenue: revenueResult,
  };
}

/**
 * Aktif üye sayısını getirir
 */
async function getActiveMembersCount(
  supabase: SupabaseClient,
  gymId: string
): Promise<number> {
  const { count, error } = await supabase
    .from('gym_memberships')
    .select('*', { count: 'exact', head: true })
    .eq('gym_id', gymId)
    .eq('status', 'active');

  if (error) {
    throw new Error(`Üye sayısı alınamadı: ${error.message}`);
  }

  return count || 0;
}

/**
 * Toplam paket sayısını getirir
 */
async function getTotalPackagesCount(
  supabase: SupabaseClient,
  gymId: string
): Promise<number> {
  const { data, error } = await supabase
    .from('gym_packages')
    .select('id')
    .eq('gym_id', gymId);

  if (error) {
    throw new Error(`Paket bilgileri alınamadı: ${error.message}`);
  }

  return data?.length || 0;
}

/**
 * Toplam geliri getirir
 */
async function getTotalRevenue(
  supabase: SupabaseClient,
  gymId: string
): Promise<number> {
  const { data, error } = await supabase
    .from('gym_membership_packages')
    .select('purchase_price')
    .eq('gym_id', gymId);

  if (error) {
    throw new Error(`Gelir bilgileri alınamadı: ${error.message}`);
  }

  return (data || []).reduce(
    (sum, mp: { purchase_price: number | null }) =>
      sum + (Number(mp.purchase_price) || 0),
    0
  );
}
