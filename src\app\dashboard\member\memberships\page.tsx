import { Metadata } from 'next';
import { MembershipCardActions } from './components/membership-card-actions';

// Force dynamic rendering

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import {
  Dumbbell,
  MapPin,
  Phone,
  Calendar,
  Package,
  CreditCard,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Search,
  Plus,
  ExternalLink,
} from 'lucide-react';

import { formatDate } from '@/lib/utils';
import {
  getMemberMembershipsWithPackages,
  MemberMembershipWithPackages,
} from '@/lib/actions/all-actions';

export const metadata: Metadata = {
  title: 'Üyeliklerim & Paketlerim - Sportiva',
  description:
    'Salon üyeliklerinizi, satın aldığınız paketleri ve ödeme bilgilerinizi görüntüleyin.',
};

// Empty State Component
function EmptyMembershipsState() {
  return (
    <Card className="border-primary/20 from-primary/5 to-primary/10 border-2 border-dashed bg-gradient-to-br">
      <CardContent className="px-8 py-16 text-center">
        <div className="mx-auto max-w-md space-y-6">
          {/* Icon */}
          <div className="relative">
            <div className="bg-primary/10 mx-auto flex h-20 w-20 items-center justify-center rounded-full">
              <Dumbbell className="text-primary h-10 w-10" />
            </div>
            <div className="absolute -top-1 -right-1 flex h-6 w-6 items-center justify-center rounded-full bg-orange-500">
              <span className="text-xs font-bold text-white">!</span>
            </div>
          </div>

          {/* Content */}
          <div className="space-y-3">
            <h3 className="text-foreground text-2xl font-bold">
              Henüz Üyeliğiniz Yok
            </h3>
            <p className="text-muted-foreground text-lg leading-relaxed">
              Fitness yolculuğunuza başlamak için bir salona üye olun. Size en
              uygun salonu bulun ve hemen başlayın!
            </p>
          </div>

          {/* Benefits */}
          <div className="space-y-2 rounded-lg bg-white/50 p-4 dark:bg-gray-800/50">
            <h4 className="text-foreground mb-3 text-sm font-semibold">
              Üye olduğunuzda:
            </h4>
            <div className="text-muted-foreground space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Salon imkanlarından faydalanın</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Özel paketler satın alın</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>İlerlemenizi takip edin</span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col gap-3 pt-2 sm:flex-row">
            <Link href="/findGym" className="flex-1">
              <Button size="lg" className="w-full">
                <Search className="mr-2 h-4 w-4" />
                Salon Ara
              </Button>
            </Link>
            <Link href="/dashboard/member/invitations" className="flex-1">
              <Button variant="outline" size="lg" className="w-full">
                <Package className="mr-2 h-4 w-4" />
                Davetleri Görüntüle
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Membership Card Component
function MembershipCard({
  membership,
}: {
  membership: MemberMembershipWithPackages;
}) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge className="bg-success text-success-foreground">
            <CheckCircle className="mr-1 h-3 w-3" />
            Aktif
          </Badge>
        );
      case 'passive':
        return (
          <Badge className="bg-muted text-muted-foreground">
            <Clock className="mr-1 h-3 w-3" />
            Pasif
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            <AlertTriangle className="mr-1 h-3 w-3" />
            {status}
          </Badge>
        );
    }
  };

  const getPackageStatusBadge = (
    isActive: boolean | null,
    isExpired: boolean
  ) => {
    if (isActive) {
      return (
        <Badge className="bg-info text-info-foreground">
          <CheckCircle className="mr-1 h-3 w-3" />
          Aktif
        </Badge>
      );
    }
    if (isExpired) {
      return (
        <Badge className="bg-warning text-warning-foreground">
          <XCircle className="mr-1 h-3 w-3" />
          Süresi Dolmuş
        </Badge>
      );
    }
    return (
      <Badge variant="secondary">
        <Clock className="mr-1 h-3 w-3" />
        Pasif
      </Badge>
    );
  };

  return (
    <Card
      className={`transition-shadow hover:shadow-lg ${membership.status === 'active' ? 'border-success/30' : 'border-border'}`}
    >
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-xl">{membership.gym.name}</CardTitle>
            <div className="flex items-center gap-2">
              {getStatusBadge(membership.status)}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-muted-foreground text-right text-sm">
              <div>Katılım: {formatDate(membership.created_at)}</div>
              {membership.approved_at && (
                <div>Onay: {formatDate(membership.approved_at)}</div>
              )}
            </div>
            <MembershipCardActions
              membershipId={membership.id}
              currentStatus={membership.status}
              gymName={membership.gym.name}
            />
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Gym Info */}
        <div className="space-y-2">
          {membership.gym.address && (
            <div className="text-muted-foreground flex items-center text-sm">
              <MapPin className="mr-2 h-4 w-4 flex-shrink-0" />
              <span>{membership.gym.address}</span>
            </div>
          )}
          {membership.gym.phone && (
            <div className="text-muted-foreground flex items-center text-sm">
              <Phone className="mr-2 h-4 w-4 flex-shrink-0" />
              <span>{membership.gym.phone}</span>
            </div>
          )}
        </div>

        {/* Packages */}
        {membership.packages && membership.packages.length > 0 ? (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Package className="text-muted-foreground h-4 w-4" />
              <span className="text-sm font-medium">
                Paketlerim ({membership.packages.length})
              </span>
            </div>
            <div className="space-y-2">
              {membership.packages.map(pkg => (
                <div
                  key={pkg.id}
                  className="bg-muted/50 space-y-2 rounded-lg p-3"
                >
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="font-medium">{pkg.packageName}</div>
                      <div className="text-muted-foreground text-sm">
                        {pkg.packageType}
                      </div>
                    </div>
                    {getPackageStatusBadge(pkg.isActive, pkg.isExpired)}
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-1">
                      <CreditCard className="text-muted-foreground h-3 w-3" />
                      <span>{pkg.purchasePrice.toLocaleString('tr-TR')} ₺</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="text-muted-foreground h-3 w-3" />
                      <span>{formatDate(pkg.startDate)}</span>
                    </div>
                  </div>

                  {pkg.endDate && (
                    <div className="text-muted-foreground text-sm">
                      Bitiş: {formatDate(pkg.endDate)}
                    </div>
                  )}

                  {pkg.description && (
                    <div className="text-muted-foreground text-sm">
                      {pkg.description}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-muted-foreground py-4 text-center">
            <Package className="mx-auto mb-2 h-8 w-8 opacity-50" />
            <p className="text-sm">Henüz paket satın alınmamış</p>
          </div>
        )}

        {/* Salon Detay Butonu */}
        <div className="border-t pt-4">
          <Link
            href={
              membership.gym.slug
                ? `/gym/${membership.gym.slug}`
                : `/gym/${membership.gym.id}`
            }
            target="_blank"
            rel="noopener noreferrer"
          >
            <Button variant="outline" size="sm" className="w-full">
              <ExternalLink className="mr-2 h-4 w-4" />
              Salon Detayını Gör
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}

// Main Page Component (Server Component)
export default async function MemberMembershipsPage() {
  const membershipsResult = await getMemberMembershipsWithPackages();

  if (!membershipsResult.success) {
    throw new Error(membershipsResult.error || 'Üyelik verileri yüklenemedi');
  }

  // Tip güvenliği sağlamak için memberships dizisini açıkça MemberMembershipWithPackages[] olarak belirtiyoruz
  const memberships = (membershipsResult.data ??
    []) as MemberMembershipWithPackages[];

  // Üyelik yoksa empty state göster
  if (memberships.length === 0) {
    return <EmptyMembershipsState />;
  }

  // Üyelikleri duruma göre ayır
  const activeMemberships = memberships.filter(
    (m: MemberMembershipWithPackages) => m.status === 'active'
  );
  const passiveMemberships = memberships.filter(
    (m: MemberMembershipWithPackages) => m.status === 'passive'
  );
  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Üyeliklerim & Paketlerim
          </h1>
          <p className="text-muted-foreground">
            Salon üyeliklerinizi, satın aldığınız paketleri ve ödeme
            bilgilerinizi görüntüleyin
          </p>
        </div>
        <div className="flex gap-2">
          <Link href="/dashboard/member/invitations">
            <Button variant="outline">Salon Davetleri</Button>
          </Link>
          <Link href="/findGym">
            <Button>Yeni Salon Bul</Button>
          </Link>
        </div>
      </div>
      <div className="space-y-8">
        {/* Stats Overview */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Toplam Üyelik
              </CardTitle>
              <Dumbbell className="text-muted-foreground h-4 w-4" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{memberships.length}</div>
              <p className="text-muted-foreground text-xs">
                Tüm salon üyelikleriniz
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Aktif Üyelik
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {activeMemberships.length}
              </div>
              <p className="text-muted-foreground text-xs">
                Kullanabileceğiniz salonlar
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Toplam Paket
              </CardTitle>
              <Package className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {memberships.reduce<number>(
                  (total: number, m: MemberMembershipWithPackages) =>
                    total + (m.packages?.length || 0),
                  0
                )}
              </div>
              <p className="text-muted-foreground text-xs">
                Satın aldığınız paketler
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Active Memberships */}
        {activeMemberships.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Aktif Üyelikler</h2>
              <Badge className="bg-green-100 text-green-800">
                {activeMemberships.length} Salon
              </Badge>
            </div>
            <div className="grid gap-6 md:grid-cols-2">
              {activeMemberships.map(
                (membership: MemberMembershipWithPackages) => (
                  <MembershipCard key={membership.id} membership={membership} />
                )
              )}
            </div>
          </div>
        )}

        {/* Passive Memberships */}
        {passiveMemberships.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Pasif Üyelikler</h2>
              <Badge variant="secondary">
                {passiveMemberships.length} Salon
              </Badge>
            </div>
            <div className="grid gap-6 md:grid-cols-2">
              {passiveMemberships.map(
                (membership: MemberMembershipWithPackages) => (
                  <MembershipCard key={membership.id} membership={membership} />
                )
              )}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <Card className="from-primary/5 to-primary/10 border-primary/20 bg-gradient-to-r">
          <CardContent className="p-6">
            <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
              <div className="text-center md:text-left">
                <h3 className="mb-2 text-lg font-semibold">
                  Daha Fazla Salon Keşfedin
                </h3>
                <p className="text-muted-foreground">
                  Yeni salonlara üye olun ve fitness deneyiminizi genişletin
                </p>
              </div>
              <div className="flex gap-3">
                <Link href="/findGym">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Yeni Salon Bul
                  </Button>
                </Link>
                <Link href="/dashboard/member/invitations">
                  <Button variant="outline">Davetleri Görüntüle</Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
