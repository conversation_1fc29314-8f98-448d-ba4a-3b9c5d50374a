'use client';

import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';

export interface CreateNotificationParams {
  recipientUserId: string;
  title: string;
  message: string;
  gymId?: string;
}

export interface NotificationResponse {
  success: boolean;
  error?: string;
  data?: any;
}

/**
 * Client-side bildirim oluşturma
 */
export async function createClientNotification(
  params: CreateNotificationParams
): Promise<NotificationResponse> {
  try {
    const supabase = createClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Kimlik doğrulama hatası' };
    }

    const { recipientUserId, title, message, gymId } = params;

    // Alıcı kullanıcının var olup olmadığını kontrol et
    const { data: recipient, error: recipientError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', recipientUserId)
      .single();

    if (recipientError || !recipient) {
      return { success: false, error: 'Alıcı kullanıcı bulunamadı' };
    }

    // Bildirimi oluştur
    const { data: notification, error } = await supabase
      .from('notifications')
      .insert({
        user_id: recipientUserId,
        gym_id: gymId || null,
        title,
        message,
        is_read: false,
      })
      .select()
      .single();

    if (error) {
      console.error('Bildirim oluşturma hatası:', error);
      return { success: false, error: 'Bildirim oluşturulamadı' };
    }

    return { success: true, data: notification };
  } catch (error) {
    console.error('Bildirim oluşturma hatası:', error);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}

/**
 * Toplu bildirim oluşturma
 */
export async function createBulkClientNotifications(
  notifications: CreateNotificationParams[]
): Promise<NotificationResponse> {
  try {
    const supabase = createClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Kimlik doğrulama hatası' };
    }

    if (notifications.length === 0) {
      return { success: false, error: 'En az bir bildirim gereklidir' };
    }

    // Tüm alıcı kullanıcıların var olup olmadığını kontrol et
    const recipientIds = [...new Set(notifications.map(n => n.recipientUserId))];
    const { data: recipients, error: recipientError } = await supabase
      .from('profiles')
      .select('id')
      .in('id', recipientIds);

    if (recipientError || !recipients || recipients.length !== recipientIds.length) {
      return { success: false, error: 'Bazı alıcı kullanıcılar bulunamadı' };
    }

    // Bildirimleri hazırla
    const insertData = notifications.map(n => ({
      user_id: n.recipientUserId,
      gym_id: n.gymId || null,
      title: n.title,
      message: n.message,
      is_read: false,
    }));

    // Bildirimleri oluştur
    const { data, error } = await supabase
      .from('notifications')
      .insert(insertData)
      .select();

    if (error) {
      console.error('Toplu bildirim oluşturma hatası:', error);
      return { success: false, error: 'Bildirimler oluşturulamadı' };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Toplu bildirim oluşturma hatası:', error);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}

/**
 * Mesajlaşma bildirimi oluştur
 */
export async function createMessageNotification(
  recipientUserId: string,
  senderName: string,
  messageContent: string,
): Promise<NotificationResponse> {
  const truncatedContent = messageContent.length > 50 
    ? messageContent.substring(0, 50) + '...' 
    : messageContent;

  return createClientNotification({
    recipientUserId,
    title: 'Yeni Mesaj',
    message: `${senderName}: ${truncatedContent}`,
  });
}

/**
 * Salon daveti bildirimi oluştur
 */
export async function createGymInvitationNotification(
  recipientUserId: string,
  gymName: string,
  inviterName: string,
  role: 'member' | 'trainer',
  gymId: string
): Promise<NotificationResponse> {
  const roleText = role === 'trainer' ? 'antrenör' : 'üye';
  
  return createClientNotification({
    recipientUserId,
    title: 'Salon Daveti',
    message: `${inviterName} sizi "${gymName}" salonuna ${roleText} olarak davet etti.`,
    gymId,
  });
}

/**
 * Randevu bildirimi oluştur
 */
export async function createAppointmentNotification(
  recipientUserId: string,
  appointmentType: 'created' | 'cancelled' | 'reminder',
  appointmentDate: string,
  gymName: string,
  gymId: string
): Promise<NotificationResponse> {
  let title = '';
  let message = '';

  switch (appointmentType) {
    case 'created':
      title = 'Yeni Randevu';
      message = `${gymName} salonunda ${appointmentDate} tarihinde randevunuz oluşturuldu.`;
      break;
    case 'cancelled':
      title = 'Randevu İptal Edildi';
      message = `${gymName} salonundaki ${appointmentDate} tarihli randevunuz iptal edildi.`;
      break;
    case 'reminder':
      title = 'Randevu Hatırlatması';
      message = `${gymName} salonunda ${appointmentDate} tarihinde randevunuz var.`;
      break;
  }

  return createClientNotification({
    recipientUserId,
    title,
    message,
    gymId,
  });
}

/**
 * Üyelik bildirimi oluştur
 */
export async function createMembershipNotification(
  recipientUserId: string,
  notificationType: 'approved' | 'rejected' | 'expired' | 'expiring',
  gymName: string,
  gymId: string,
  additionalInfo?: string
): Promise<NotificationResponse> {
  let title = '';
  let message = '';

  switch (notificationType) {
    case 'approved':
      title = 'Üyelik Onaylandı';
      message = `"${gymName}" salonuna üyeliğiniz onaylandı. Artık salon hizmetlerinden yararlanabilirsiniz.`;
      break;
    case 'rejected':
      title = 'Üyelik Reddedildi';
      message = `"${gymName}" salonuna üyelik başvurunuz reddedildi.`;
      if (additionalInfo) {
        message += ` Sebep: ${additionalInfo}`;
      }
      break;
    case 'expired':
      title = 'Üyelik Süresi Doldu';
      message = `"${gymName}" salonundaki üyeliğinizin süresi doldu. Yenilemek için salon ile iletişime geçin.`;
      break;
    case 'expiring':
      title = 'Üyelik Süresi Doluyor';
      message = `"${gymName}" salonundaki üyeliğinizin süresi yakında dolacak. Yenilemek için salon ile iletişime geçin.`;
      break;
  }

  return createClientNotification({
    recipientUserId,
    title,
    message,
    gymId,
  });
}

/**
 * Sistem bildirimi oluştur
 */
export async function createSystemNotification(
  recipientUserId: string,
  title: string,
  message: string
): Promise<NotificationResponse> {
  return createClientNotification({
    recipientUserId,
    title,
    message,
  });
}

/**
 * Toast bildirim göster (sadece UI için)
 */
export function showToastNotification(
  type: 'success' | 'error' | 'info' | 'warning',
  title: string,
  message?: string,
  duration?: number
) {
  const options = {
    description: message,
    duration: duration || 4000,
  };

  switch (type) {
    case 'success':
      toast.success(title, options);
      break;
    case 'error':
      toast.error(title, options);
      break;
    case 'info':
      toast.info(title, options);
      break;
    case 'warning':
      toast.warning(title, options);
      break;
  }
}
