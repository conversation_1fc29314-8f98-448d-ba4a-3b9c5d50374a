'use client';

import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLeft, Settings } from 'lucide-react';
import { useSettingsTitle } from '@/hooks/use-settings-title';

export function SettingsHeader() {
  const title = useSettingsTitle();

  return (
    <div className="mx-auto px-0">
      <div className="flex h-16 items-center justify-between px-4">
        {/* Sol taraf - Panel'e dön butonu (sadece desktop'ta görünür) */}
        <div className="hidden items-center gap-4 lg:flex">
          <Link href="/dashboard">
            <Button variant="outline" size="sm" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Panel&apos;e Dön
            </Button>
          </Link>
        </div>

        {/* Merkez - Dinamik Başlık */}
        <div className="flex flex-1 items-center justify-center gap-3">
          <div className="bg-primary/10 rounded-full p-2">
            <Settings className="text-primary h-5 w-5" />
          </div>
          <div>
            <h1 className="text-lg font-semibold lg:text-xl">{title}</h1>
          </div>
        </div>
      </div>
    </div>
  );
}
