import { Suspense } from 'react';
import { getGymAppointments } from '@/lib/actions/dashboard/company/appointment-actions';
import { AppointmentsFilters } from './components/appointments-filters';
import { AppointmentsHeader } from './components/appointments-header';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { AppointmentsView } from './components/appointments-view';
import { getGymById } from '@/lib/actions/dashboard/company/gym-actions';

interface PageProps {
  params: {
    gymId: string;
  };
  searchParams: {
    startDate?: string;
    endDate?: string;
    trainerId?: string;
    status?: string;
  };
}

export default async function AppointmentsPage({
  params,
  searchParams,
}: PageProps) {
  // Auth and role check
  const resolvedParams = await params;
  const { gymId } = resolvedParams;

  return (
    <div className="space-y-6">
      <AppointmentsHeader gymId={gymId} />
      <div className="grid gap-6">
        <Suspense>
          <ListFiltersWrapper gymId={gymId} searchParams={searchParams} />
        </Suspense>
        <Suspense fallback={<AppointmentsListSkeleton />}>
          <AppointmentsContent gymId={gymId} searchParams={searchParams} />
        </Suspense>
      </div>
    </div>
  );
}
async function ListFiltersWrapper({
  gymId,
  searchParams,
}: {
  gymId: string;
  searchParams: PageProps['searchParams'];
}) {
  const resolvedParams = await searchParams;
  const view =
    (resolvedParams as any)?.view === 'calendar' ? 'calendar' : 'list';
  if (view !== 'list') return null;
  return (
    <Card>
      <CardContent>
        <Suspense fallback={<AppointmentsFiltersSkeleton />}>
          <AppointmentsFilters gymId={gymId} searchParams={resolvedParams} />
        </Suspense>
      </CardContent>
    </Card>
  );
}

async function AppointmentsContent({
  gymId,
  searchParams,
}: {
  gymId: string;
  searchParams: PageProps['searchParams'];
}) {
  const { startDate, endDate, trainerId, status } = await searchParams;
  const filters = {
    startDate: startDate,
    endDate: endDate,
    trainerId: trainerId,
    status: status as any,
  };

  const appointmentsResult = await getGymAppointments(gymId, filters);
  const gymResult = await getGymById(gymId);

  if (!appointmentsResult.success) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="space-y-4 text-center">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <svg
              className="h-6 w-6 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Randevular Yüklenemedi
            </h3>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Randevular yüklenirken bir sorun oluştu. Lütfen sayfayı yenileyin
              veya daha sonra tekrar deneyin.
            </p>
            {process.env.NODE_ENV === 'development' && (
              <p className="mt-2 font-mono text-xs text-red-600">
                Hata: {appointmentsResult.error}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }

  const timeSlots = Array.isArray(gymResult.data?.time_slots)
    ? (gymResult.data!.time_slots as string[])
    : undefined;
  const resolvedParams = await searchParams;
  const view =
    (resolvedParams as any)?.view === 'calendar' ? 'calendar' : 'list';
  return (
    <AppointmentsView
      appointments={appointmentsResult.data || []}
      view={view}
      timeSlots={timeSlots}
    />
  );
}

function AppointmentsListSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="rounded-lg border p-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-4 w-48" />
              <Skeleton className="h-3 w-32" />
            </div>
            <div className="flex items-center space-x-2">
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-8 w-8" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

function AppointmentsFiltersSkeleton() {
  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
    </div>
  );
}
