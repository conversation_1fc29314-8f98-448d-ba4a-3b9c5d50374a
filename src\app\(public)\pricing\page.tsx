export const dynamic = 'force-static';

import { PricingHero } from '@/components/public/pricing/pricing-hero';
import { PricingComparison } from '@/components/public/pricing/pricing-comparison';
import { PricingFAQ } from '@/components/public/pricing/pricing-faq';
import { PricingCTA } from '@/components/public/pricing/pricing-cta';
import { fetchAllPlatformPackages } from '@/lib/actions/business/platform-packages';
import { PricingModern } from '@/components/public/home/<USER>';

export default async function PricingPage() {
   const res = await fetchAllPlatformPackages();
   const packages = res.data ?? [];
  return (
    <main className="flex-1">
      <PricingHero />
      <PricingModern showHeader={false} compact={false} packages={packages} />
      <PricingComparison packages={packages} /> <PricingFAQ />
      <PricingCTA />
    </main>
  );
}
