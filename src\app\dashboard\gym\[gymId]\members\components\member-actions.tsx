'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Eye } from 'lucide-react';
import Link from 'next/link';

interface MemberActionsProps {
  membershipId: string;
  gymId: string;
}

export function MemberActions({ membershipId, gymId }: MemberActionsProps) {
  return (
    <>
      <div className="flex items-center justify-end gap-1">
        {/* View Details Button */}
        <Button variant="ghost" size="sm" asChild className="h-8">
          <Link href={`/dashboard/gym/${gymId}/members/${membershipId}`}>
            <Eye className="mr-1 h-3 w-3" />
            <span className="hidden sm:inline">Detaylar</span>
          </Link>
        </Button>
      </div>
    </>
  );
}
