import { <PERSON><PERSON><PERSON>and<PERSON> } from '../manager/components/toast-handler';
import { Suspense } from 'react';
import { DashboardHeader } from '@/components/dashboard/shared/dashboard-header';
import { getGymManagerGyms, getGymManagerStats } from '@/lib/actions/dashboard/gym-manager/gym-manager-actions';
import { GymManagerDashboard } from './components/GymManagerDashboard';

export default async function GymManagerDashboardPage() {
  const [gymsResult, statsResult] = await Promise.all([
    getGymManagerGyms(),
    getGymManagerStats(),
  ]);

  if (!gymsResult.success) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="space-y-4 text-center">
          <h2 className="text-xl font-semibold">
            Salon Bilgileri Yüklenemedi
          </h2>
          <p className="text-muted-foreground">{gymsResult.error}</p>
        </div>
      </div>
    );
  }

  const gyms = (gymsResult.success && gymsResult.data) ? gymsResult.data : [];
  const stats = (statsResult.success && statsResult.data) ? statsResult.data : null;

  return (
    <div className="space-y-6">
      <ToastHandler />
      <DashboardHeader mode="manager" />
      <div>
        <Suspense fallback={<div>Loading...</div>}>
          <GymManagerDashboard gyms={gyms} stats={stats} />
        </Suspense>
      </div>
    </div>
  );
}
