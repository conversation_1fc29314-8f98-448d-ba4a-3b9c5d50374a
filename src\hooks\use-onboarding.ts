'use client';

import { useState, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { PlatformRoles } from '@/types/database/enums';
import type {
  PersonalData,
  MemberData,
  TrainerData,
  ManagerData,
  OnboardingFieldErrors,
  OnboardingStep,
} from '@/types/onboarding';
import { updateFullname } from '@/lib/actions/user/profile-actions';
import { upsertMemberDetails } from '@/lib/actions/user/member-actions';
import { createTrainerDetails } from '@/lib/actions/user/create-user-actions';
import { createPassiveCompany } from '@/lib/actions/dashboard/company/company-actions';

interface UseOnboardingProps {
  currentRoles: PlatformRoles[];
  hasFullName: boolean;
}

export function useOnboarding({
  currentRoles,
  hasFullName,
}: UseOnboardingProps) {
  const router = useRouter();

  // State Management
  const [step, setStep] = useState<OnboardingStep>('welcome');
  const [selectedRoles, setSelectedRoles] = useState<PlatformRoles[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState<OnboardingFieldErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);


  // Form Data States
  const [personalData, setPersonalData] = useState<PersonalData>({
    first_name: '',
    last_name: '',
  });
  const [memberData, setMemberData] = useState<MemberData>({
    age: '',
    gender: '',
    height_cm: '',
    weight_kg: '',
    fitness_goal: '',
  });
  const [trainerData, setTrainerData] = useState<TrainerData>({
    specialization: '',
    certification_level: '',
    experience_years: '',
    bio: '',
  });
  const [managerData, setManagerData] = useState<ManagerData>({
    companyName: '',
    companyPhone: '',
    companyEmail: '',
  });

  // Derived State
  const currentRolesSet = useMemo(() => new Set(currentRoles), [currentRoles]);



  const isRoleSelectable = useCallback(
    (role: PlatformRoles): boolean => {
      if (currentRolesSet.has(role)) return false;
      return true;
    },
    [currentRolesSet]
  );

  const getRoleWarningMessage = useCallback(
    (): string | null => {
      // Artık çakışma yok
      return null;
    },
    []
  );

  const validateRoleCombination = useCallback(
    (): { isValid: boolean; message?: string } => {
      // Artık çakışma yok, tüm kombinasyonlar geçerli
      return { isValid: true };
    },
    []
  );

  // Form Validation Logic
  const validateForms = useCallback(() => {
    const errors: OnboardingFieldErrors = {};
    // Personal Data
    if (!hasFullName) {
      if (!personalData.first_name.trim())
        errors.personal = 'Ad alanı zorunludur.';
      else if (!personalData.last_name.trim())
        errors.personal = 'Soyad alanı zorunludur.';
    }
    // Member Data
    if (selectedRoles.includes('member')) {
      if (!memberData.age || !memberData.gender)
        errors.member = 'Yaş ve cinsiyet zorunludur.';
    }
    // Trainer Data
    if (selectedRoles.includes('trainer')) {
      if (!trainerData.specialization || !trainerData.certification_level)
        errors.trainer = 'Uzmanlık ve sertifika zorunludur.';
    }
    // Manager Data
    if (selectedRoles.includes('manager')) {
      if (!managerData.companyName.trim())
        errors.manager = 'Şirket adı zorunludur.';
    }
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  }, [
    hasFullName,
    personalData,
    memberData,
    trainerData,
    managerData,
    selectedRoles,
  ]);

  // Actions
  const handleRoleToggle = (role: PlatformRoles) => {
    setSelectedRoles(prev =>
      prev.includes(role) ? prev.filter(r => r !== role) : [...prev, role]
    );
  };

  const navigateToStep = (targetStep: OnboardingStep) => {
    setError(null);
    setFieldErrors({});
    setStep(targetStep);
  };

  const handleContinueToForms = () => {
    const validation = validateRoleCombination();
    if (selectedRoles.length > 0 && validation.isValid) {
      navigateToStep('forms');
    } else {
      setError(validation.message || 'Lütfen en az bir rol seçin.');
    }
  };

  const handleSubmit = async () => {
    if (!validateForms()) {
      setError('Lütfen tüm zorunlu alanları doldurun.');
      return;
    }
    setIsSubmitting(true);
    setError(null);

    try {
      if (!hasFullName) {
        const fullName = `${personalData.first_name.trim()} ${personalData.last_name.trim()}`;
        const nameForm = new FormData();
        nameForm.append('full_name', fullName);
        const result = await updateFullname(nameForm);
        if (!result.success)
          throw new Error(result.error || 'Profil güncellenemedi.');
      }

      if (selectedRoles.includes('member')) {
        const formData = new FormData();
        Object.entries(memberData).forEach(
          ([key, value]) => value && formData.append(key, value)
        );
        const result = await upsertMemberDetails(formData);
        if (!result.success)
          throw new Error(result.error || 'Üye bilgileri kaydedilemedi.');
      }

      if (selectedRoles.includes('trainer')) {
        const result = await createTrainerDetails({
          ...trainerData,
          experience_years: trainerData.experience_years
            ? parseInt(trainerData.experience_years)
            : null,
          bio: trainerData.bio || null,
        });
        if (!result.success)
          throw new Error(result.error || 'Antrenör bilgileri kaydedilemedi.');
      }

      if (selectedRoles.includes('manager')) {
        const formData = new FormData();
        formData.append('name', managerData.companyName);
        if (managerData.companyPhone)
          formData.append('phone', managerData.companyPhone);
        if (managerData.companyEmail)
          formData.append('email', managerData.companyEmail);
        const result = await createPassiveCompany(formData);
        if (!result.success)
          throw new Error(result.error || 'Şirket kaydedilemedi.');
        // Şirket oluşturulduktan sonra merkezi ödeme sayfasına yönlendir
        router.push('/dashboard/manager/billing?from=onboarding');
      } else {
        router.push('/dashboard');
      }
    } catch (e) {
      setError(e instanceof Error ? e.message : 'Bir hata oluştu.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    step,
    selectedRoles,
    error,
    fieldErrors,
    isSubmitting,
    personalData,
    memberData,
    trainerData,
    managerData,

    // Computed
    isRoleSelectable,
    getRoleWarningMessage,
    validateRoleCombination,
    isFormValid: Object.keys(fieldErrors).length === 0,

    // Actions
    handleRoleToggle,
    setPersonalData,
    setMemberData,
    setTrainerData,
    setManagerData,
    navigateToStep,
    handleContinueToForms,
    handleSubmit,
  };
}
