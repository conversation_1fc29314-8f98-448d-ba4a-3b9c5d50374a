'use client';

import { useEffect, useState } from 'react';
import { AnimatedSection } from '@/components/ui/animated-section';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Clock, DollarSign, Users, Sparkles } from 'lucide-react';

export function PremiumROICalculator() {
  const [memberCount, setMemberCount] = useState<number>(250);
  const [monthlyRevenue, setMonthlyRevenue] = useState<number>(35000);
  const [gymType, setGymType] = useState<string>('1.3');
  const [results, setResults] = useState({
    currentRevenue: 35000,
    potentialRevenue: 42000,
    timeSaved: 15,
    efficiencyGain: 20,
    memberSatisfaction: 25,
  });

  useEffect(() => {
    const currentRevenue = Math.max(0, monthlyRevenue || 0);
    const multiplier = Number.isFinite(parseFloat(gymType))
      ? parseFloat(gymType)
      : 1.0;
    const potentialRevenue = Math.round(currentRevenue * multiplier);
    const timeSaved = Math.max(0, Math.round(memberCount * 0.06)); // üye başına 0.06 saat
    const efficiencyGain =
      currentRevenue > 0
        ? Math.round(
            ((potentialRevenue - currentRevenue) / currentRevenue) * 100
          )
        : 0;
    const memberSatisfaction = Math.min(
      Math.max(0, Math.round(memberCount * 0.1)),
      30
    );

    setResults({
      currentRevenue,
      potentialRevenue,
      timeSaved,
      efficiencyGain,
      memberSatisfaction,
    });
  }, [memberCount, monthlyRevenue, gymType]);

  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <AnimatedSection animation="fade-in">
          <div className="mx-auto mb-8 max-w-2xl text-center">
            <h2 className="text-3xl font-bold text-balance md:text-4xl">
              Yatırım Getirisi (ROI)
            </h2>
            <p className="text-muted-foreground mt-2">
              Otomasyon ve analitik ile kazancınızı nasıl artıracağınızı
              hesaplayın.
            </p>
          </div>
        </AnimatedSection>
        <AnimatedSection animation="fade-up" delay={150}>
          <div className="mx-auto max-w-5xl">
            <div className="relative mt-2 rounded-3xl border border-white/10 bg-white/5 p-8 shadow-2xl backdrop-blur supports-[backdrop-filter]:bg-white/10">
              <Sparkles
                className="absolute top-6 right-6 size-5 text-white/40"
                aria-hidden
              />

              <div className="grid gap-10 md:grid-cols-2">
                {/* Form */}
                <div className="space-y-6">
                  <div>
                    <Label className="text-foreground mb-2 block text-sm font-medium">
                      Mevcut Üye Sayısı
                    </Label>
                    <Input
                      type="number"
                      inputMode="numeric"
                      value={memberCount}
                      onChange={e => setMemberCount(Number(e.target.value))}
                      className="w-full"
                      placeholder="250"
                      aria-label="Mevcut üye sayısı"
                    />
                  </div>

                  <div>
                    <Label className="text-foreground mb-2 block text-sm font-medium">
                      Aylık Ortalama Gelir (₺)
                    </Label>
                    <Input
                      type="number"
                      inputMode="numeric"
                      value={monthlyRevenue}
                      onChange={e => setMonthlyRevenue(Number(e.target.value))}
                      className="w-full"
                      placeholder="35000"
                      aria-label="Aylık ortalama gelir"
                    />
                  </div>

                  <div>
                    <Label className="text-foreground mb-2 block text-sm font-medium">
                      Salon Tipi
                    </Label>
                    <Select value={gymType} onValueChange={v => setGymType(v)}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Seçiniz" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1.1">Küçük Salon (x1.1)</SelectItem>
                        <SelectItem value="1.2">Orta Salon (x1.2)</SelectItem>
                        <SelectItem value="1.3">Büyük Salon (x1.3)</SelectItem>
                        <SelectItem value="1.4">
                          Premium Salon (x1.4)
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <p className="text-muted-foreground text-xs">
                    Tahminler örnek amaçlıdır. Gerçek sonuçlar işletmenize göre
                    değişebilir.
                  </p>
                </div>

                {/* Results */}
                <div className="space-y-4">
                  <h3 className="text-primary text-xl font-semibold">
                    Potansiyel Faydalar
                  </h3>

                  <div className="space-y-4">
                    <div className="rounded-lg border border-white/10 bg-white/5 p-4">
                      <div className="mb-2 flex items-center gap-2">
                        <DollarSign className="text-primary size-5" />
                        <span className="font-semibold">Gelir Potansiyeli</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Mevcut:</span>
                        <span className="font-semibold">
                          ₺{results.currentRevenue.toLocaleString('tr-TR')}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">
                          Potansiyel:
                        </span>
                        <span className="text-success font-semibold">
                          ₺{results.potentialRevenue.toLocaleString('tr-TR')}
                        </span>
                      </div>
                      <div className="text-muted-foreground mt-1 text-xs">
                        Verim artışı:{' '}
                        <span className="text-primary font-medium">
                          %{results.efficiencyGain}
                        </span>
                      </div>
                    </div>

                    <div className="rounded-lg border border-white/10 bg-white/5 p-4">
                      <div className="mb-2 flex items-center gap-2">
                        <Clock className="text-primary size-5" />
                        <span className="font-semibold">Zaman Tasarrufu</span>
                      </div>
                      <p className="text-muted-foreground text-sm">
                        Haftalık{' '}
                        <span className="text-primary font-semibold">
                          {results.timeSaved} saat
                        </span>{' '}
                        tasarruf
                      </p>
                    </div>

                    <div className="rounded-lg border border-white/10 bg-white/5 p-4">
                      <div className="mb-2 flex items-center gap-2">
                        <Users className="text-primary size-5" />
                        <span className="font-semibold">Üye Memnuniyeti</span>
                      </div>
                      <p className="text-muted-foreground text-sm">
                        Potansiyel{' '}
                        <span className="text-primary font-semibold">
                          %{results.memberSatisfaction}
                        </span>{' '}
                        artış
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}
