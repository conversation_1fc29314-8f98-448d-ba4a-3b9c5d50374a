import { AnimatedSection } from '@/components/ui/animated-section';
import { Star } from 'lucide-react';

export function PremiumLogosMarquee() {
  const brands = [
    'FitClub',
    'GymPro',
    'Athletica',
    'PowerZone',
    'Stronger',
    'MoveWell',
    'Flex<PERSON>ab',
    'BodyForge',
  ];
  return (
    <section className="from-muted/30 via-background to-muted/30 bg-gradient-to-r py-16 md:py-20">
      <div className="container mx-auto px-4">
        <AnimatedSection animation="fade-in">
          <div className="mx-auto mb-12 max-w-3xl text-center">
            <div className="border-primary/20 bg-primary/10 text-primary mb-4 inline-flex items-center gap-2 rounded-full border px-4 py-2 text-sm font-medium backdrop-blur-sm">
              <Star className="h-4 w-4" />
              <span>Güvenilir Ortaklar</span>
            </div>
            <h3 className="from-foreground to-foreground/80 bg-gradient-to-r bg-clip-text text-3xl font-bold text-balance text-transparent md:text-4xl">
              Güvenilir Markalarla <PERSON>
            </h3>
            <p className="text-muted-foreground mt-4 text-lg">
              Türkiye&apos;nin önde gelen spor salonları Sportiva&apos;yı tercih
              ediyor
            </p>
          </div>
        </AnimatedSection>
        <AnimatedSection animation="fade-up" delay={150}>
          <h2 id="logos-heading" className="sr-only">
            Güvenen markalar
          </h2>
          <div className="relative overflow-hidden [mask-image:linear-gradient(to_right,transparent,black_10%,black_90%,transparent)]">
            <div className="animate-scroll flex w-max gap-16 whitespace-nowrap">
              {[...brands, ...brands].map((brand, i) => (
                <div
                  key={`${brand}-${i}`}
                  className="border-border/50 from-background/80 to-background/60 hover:border-primary/30 flex items-center justify-center rounded-xl border bg-gradient-to-br px-8 py-4 shadow-sm backdrop-blur transition-all duration-300 hover:shadow-md"
                >
                  <span className="text-foreground/80 hover:text-primary text-xl font-bold tracking-wide transition-colors">
                    {brand}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}
