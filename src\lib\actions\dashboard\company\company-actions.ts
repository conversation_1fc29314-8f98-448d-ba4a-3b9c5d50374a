'use server';
// Slug artık kullanılmıyor - kaldırıldı
import { Companies, Gyms } from '@/types/database/tables';
import { ApiResponse } from '@/types/global/api';
import {
  createAction,
  companySetupSchema,
  companySettingsSchema,
  validateFormData,
} from '../../core';
import { createClient } from '@/lib/supabase/server';

/**
 * Şirket bilgilerini getirir
 */
export async function getCompanyById(
  companyId: string
): Promise<ApiResponse<Companies>> {
  try {
    const supabase = await createClient();

    // Company sahipliği kontrolü
    const { data: company, error } = await supabase
      .from('companies')
      .select('*')
      .eq('id', companyId)
      .single();

    if (error || !company) {
      throw new Error('Şirket bulunamadı');
    }

    // Manager sahipliği kontrolü
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user || company.manager_profile_id !== user.id) {
      throw new Error('Bu şirkete erişim yetkiniz yok');
    }

    return { success: true, data: company };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'Bilinmeyen bir hata oluştu',
    };
  }
}

/**
 * Manager'ın şirketini getirir (tek şirket mantığı)
 */
export async function getManagerCompany(): Promise<
  ApiResponse<Companies | null>
> {
  return createAction<Companies | null>(async (_, supabase, userId) => {
    const { data: company, error } = await supabase
      .from('companies')
      .select('*')
      .eq('manager_profile_id', userId!)
      .eq('status', 'active')
      .single();

    if (error) {
      // Şirket bulunamadı durumu normal (setup aşaması)
      if (error.code === 'PGRST116') {
        return null;
      }
      throw new Error(`Şirket getirilirken hata: ${error.message}`);
    }

    return company;
  });
}

/**
 * Manager'ın şirketinin şubelerini getirir
 */
export async function getManagerCompanyBranches(): Promise<
  ApiResponse<Gyms[]>
> {
  return createAction<Gyms[]>(async (_, supabase, userId) => {
    // Önce manager'ın şirketini bul
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('id')
      .eq('manager_profile_id', userId!)
      .eq('status', 'active')
      .single();

    if (companyError) {
      if (companyError.code === 'PGRST116') {
        return []; // Şirket yoksa boş array döndür
      }
      throw new Error(`Şirket bulunamadı: ${companyError.message}`);
    }

    // Şirketin şubelerini getir
    const { data: branches, error } = await supabase
      .from('gyms')
      .select('*')
      .eq('company_id', company.id)
      .eq('status', 'active')
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Şubeler getirilirken hata: ${error.message}`);
    }

    return branches || [];
  });
}

/**
 * Onboarding sırasında passive şirket oluşturur
 */
export async function createPassiveCompany(
  formData: FormData
): Promise<ApiResponse<Companies>> {
  return createAction<Companies>(async (_, supabase, userId) => {
    // Kullanıcının zaten bir şirketi var mı kontrol et
    const { data: existingCompany, error: existingError } = await supabase
      .from('companies')
      .select('id')
      .eq('manager_profile_id', userId)
      .maybeSingle();

    if (existingError && existingError.code !== 'PGRST116') {
      throw new Error(
        `Mevcut şirket kontrolü yapılamadı: ${existingError.message}`
      );
    }

    if (existingCompany) {
      throw new Error('Zaten bir şirketiniz bulunmaktadır.');
    }

    // Form verilerini doğrula
    const validation = await validateFormData(formData, companySetupSchema);
    if (validation.error) {
      throw new Error(validation.error);
    }

    const companyData = validation.data!;

    // Şirket verilerini hazırla
    const insertData = {
      manager_profile_id: userId!,
      name: companyData.name,
      phone: companyData.phone || null,
      email: companyData.email || null,
      logo_url: companyData.logo_url ? companyData.logo_url : null,
      status: 'passive',
      social_links: {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Şirketi oluştur
    const { data: company, error } = await supabase
      .from('companies')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      throw new Error(`Şirket oluşturulamadı: ${error.message}`);
    }

    return company;
  });
}

/**
 * Manager'ın şirket ayarlarını günceller (tek şirket mantığı)
 */
export async function updateManagerCompanySettings(
  formData: FormData
): Promise<ApiResponse<Companies>> {
  return createAction<Companies>(
    async (_, supabase, userId) => {
      // Form verilerini doğrula - artık tüm alanları içeren schema kullan
      const validation = await validateFormData(
        formData,
        companySettingsSchema
      );
      if (validation.error) {
        throw new Error(validation.error);
      }

      const updateData = validation.data!;

      // Sosyal medya linklerini al ve parse et
      const socialLinksRaw = formData.get('social_links') as string | null;
      let socialLinks = {};

      if (socialLinksRaw) {
        try {
          socialLinks = JSON.parse(socialLinksRaw);
        } catch (error) {
          console.error('Social links parse error:', error);
          // Hata durumunda boş obje kullan
          socialLinks = {};
        }
      }

      // Güncelleme verilerini hazırla
      const updateObj: any = {
        ...updateData,
        social_links: socialLinks,
        updated_at: new Date().toISOString(),
      };

      // Boş string'leri null'a çevir
      Object.keys(updateObj).forEach(key => {
        if (updateObj[key] === '') {
          updateObj[key] = null;
        }
      });

      // Manager'ın şirketini güncelle (status kontrolü kaldırıldı çünkü status'u da güncelleyebiliyoruz)
      const { data: updatedCompany, error: updateError } = await supabase
        .from('companies')
        .update(updateObj)
        .eq('manager_profile_id', userId!)
        .select()
        .single();

      if (updateError) {
        throw new Error(`Şirket güncellenirken hata: ${updateError.message}`);
      }

      return updatedCompany;
    },
    {
      revalidatePaths: ['/dashboard/manager', '/dashboard/manager/settings'],
    }
  );
}

/**
 * Şirket logosu yükler ve veritabanını günceller
 */
export async function uploadCompanyLogo(
  file: File
): Promise<{ success: boolean; url?: string; error?: string }> {
  return createAction<{ success: boolean; url?: string; error?: string }>(
    async (_, supabase, userId) => {
      const fileExt = file.name.split('.').pop();
      const fileName = `logo_${Date.now()}.${fileExt}`;
      const bucket = 'company-logos';

      // Dosyayı yükle
      const { error: uploadError } = await supabase.storage
        .from(bucket)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true,
        });

      if (uploadError) {
        throw new Error(`Logo yüklenirken hata: ${uploadError.message}`);
      }

      // Public URL al
      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(fileName);

      const imageUrl = urlData.publicUrl;

      // Veritabanını güncelle
      const { error: updateError } = await supabase
        .from('companies')
        .update({
          logo_url: imageUrl,
          updated_at: new Date().toISOString(),
        })
        .eq('manager_profile_id', userId!);

      if (updateError) {
        throw new Error(
          `Veritabanı güncellenirken hata: ${updateError.message}`
        );
      }

      return { success: true, url: imageUrl };
    },
    {
      revalidatePaths: ['/dashboard/manager', '/dashboard/manager/settings'],
    }
  ).then(result => {
    if (result.success) {
      return result.data!;
    }
    return { success: false, error: result.error };
  });
}
