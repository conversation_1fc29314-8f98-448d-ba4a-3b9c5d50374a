'use client';

import { useMemo, useRef, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

import { Progress } from '@/components/ui/progress';
import { Label } from '@/components/ui/label';
import { AlertCircle, FileSpreadsheet, Loader2 } from 'lucide-react';
import <PERSON> from 'papaparse';
import { toast } from 'sonner';
import { importMemberRowAction } from '@/lib/actions/dashboard/company/member-import-actions';

interface ImportMembersClientProps {
  gymId: string;
}

type CsvRow = Record<string, string>;

interface ImportResultRow {
  index: number;
  status: 'success' | 'updated' | 'skipped' | 'error' | 'conflict';
  message?: string;
  email?: string | null;
}

function hasNameColumns(headers: string[]): boolean {
  const lower = headers.map(h => h.toLowerCase());
  const hasFull = lower.includes('full_name');
  const hasFirstLast =
    (lower.includes('first_name') && lower.includes('last_name')) ||
    (lower.includes('firstname') && lower.includes('lastname')) ||
    (lower.includes('firstName'.toLowerCase()) &&
      lower.includes('lastName'.toLowerCase()));
  return hasFull || hasFirstLast;
}

function getNormalizedFullName(row: CsvRow): string | null {
  const fn = row['full_name']?.trim();
  if (fn) return fn;
  const first = (
    row['first_name'] ||
    row['firstname'] ||
    (row as any)['firstName'] ||
    ''
  ).trim();
  const last = (
    row['last_name'] ||
    row['lastname'] ||
    (row as any)['lastName'] ||
    ''
  ).trim();
  const combined = [first, last].filter(Boolean).join(' ').trim();
  return combined.length > 0 ? combined : null;
}

function getNormalizedEmail(row: CsvRow): string | null {
  const cands = [
    row['email'],
    (row as any)['Email'],
    row['mail'],
    row['e-mail'],
    row['email_address'],
    (row as any)['emailAddress'],
  ];
  const found = cands.find(v => typeof v === 'string' && v.trim().length > 0);
  return found ? found.trim() : null;
}

function findByPattern(row: CsvRow, patterns: string[]): string | null {
  const entries = Object.entries(row);
  for (const [key, value] of entries) {
    if (typeof value !== 'string') continue;
    const normKey = key.toLowerCase().replace(/[^a-z0-9]/g, ''); // harf/rakam dışını temizle
    if (patterns.some(p => normKey.includes(p))) {
      if (value.trim().length > 0) return value.trim();
    }
  }
  return null;
}

function getNormalizedPhone(row: CsvRow): string | null {
  // Önce bilinen anahtarlarla dene
  const direct = (
    row['phone_number'] ||
    row['phone'] ||
    (row as any)['Phone'] ||
    row['phonenumber'] ||
    (row as any)['phoneNumber'] ||
    row['tel'] ||
    row['telephone'] ||
    row['gsm'] ||
    row['mobile'] ||
    row['msisdn'] ||
    ''
  )
    .toString()
    .trim();
  const found =
    direct.length > 0
      ? direct
      : findByPattern(row, ['phone', 'telefon', 'gsm', 'cep']);
  if (!found) return null;

  // Hafif normalizasyon: boşluk, tire, parantezleri kaldır ve + işaretini koru
  let raw = found.replace(/[\s\-()]/g, '');

  // TR E.164 düzeltmesi
  if (raw.startsWith('+')) {
    // already e164-ish
  } else if (raw.startsWith('0') && raw.length >= 11) {
    raw = '+90' + raw.slice(1);
  } else if (raw.startsWith('90') && raw.length >= 12) {
    raw = '+' + raw;
  } else if (raw.startsWith('5') && raw.length === 10) {
    raw = '+90' + raw;
  }

  return raw.length > 0 ? raw : null;
}

function parseCsv(content: string): CsvRow[] {
  const result = Papa.parse(content, {
    header: true,
    skipEmptyLines: true,
    dynamicTyping: false,
    transformHeader: (h: string) => h.trim(),
  });
  if (result.errors && result.errors.length > 0) {
    // Papa Parse hatalarını toplayıp kullanıcıya gösterebiliriz
    // Şimdilik ilk hatayı konsolda sessizce not almayacağız; kullanıcıya toast var.
  }
  const data = (result.data as any[]).map(row => {
    const normalized: Record<string, string> = {};
    Object.entries(row).forEach(([k, v]) => {
      normalized[String(k)] =
        typeof v === 'string' ? v.trim() : (v ?? '').toString();
    });
    return normalized as CsvRow;
  });
  return data;
}

export function ImportMembersClient({ gymId }: ImportMembersClientProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [rows, setRows] = useState<CsvRow[]>([]);
  // Duplicate güncelleme kaldırıldı: her zaman profil güncelleme yok, sadece üyelik kontrolü
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState<ImportResultRow[]>([]);

  const successCount = useMemo(
    () =>
      results.filter(r => r.status === 'success' || r.status === 'updated')
        .length,
    [results]
  );
  const errorCount = useMemo(
    () => results.filter(r => r.status === 'error').length,
    [results]
  );
  const skippedCount = useMemo(
    () => results.filter(r => r.status === 'skipped').length,
    [results]
  );

  const handleFile = async (file: File) => {
    const text = await file.text();
    const parsed = parseCsv(text);
    if (parsed.length === 0) {
      toast.error('CSV içerik boş veya okunamadı');
      return;
    }
    // sütun kontrolü
    const headers = Object.keys(parsed[0]);
    if (!hasNameColumns(headers)) {
      toast.error(
        'Zorunlu sütunlar eksik: full_name veya (first_name + last_name)'
      );
      return;
    }
    setRows(parsed);
    toast.success(`${parsed.length} satır yüklendi. İçe aktarmaya hazırsınız.`);
  };

  const onImport = async () => {
    if (rows.length === 0) {
      toast.error('Önce bir CSV yükleyin');
      return;
    }
    setIsProcessing(true);
    setResults([]);

    // Sıralı işleme: kullanıcı auth+RLS ve serverless limitleri açısından güvenli
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];

      // Minimum doğrulama
      const full_name = getNormalizedFullName(row);
      const email = getNormalizedEmail(row);
      // Telefon sadece rakam ve 11 haneye normalize edilir (TR baseline)
      const phoneRaw = getNormalizedPhone(row);
      const phone_number = phoneRaw
        ? phoneRaw.replace(/\D/g, '').slice(-11)
        : null;

      if (!full_name || (!email && !phone_number)) {
        setResults(prev => [
          ...prev,
          {
            index: i + 1,
            status: 'skipped',
            message:
              'Ad Soyad zorunlu; e-posta ya da telefon numarasından en az biri gerekli',
          },
        ]);
        continue;
      }

      const profileData = { full_name };

      try {
        const res = await importMemberRowAction({
          gymId,
          userData: { email, phone_number, profileData },
        });

        if (res.success && res.data != null) {
          const updated = (res.data as any).updated === true;
          const message = (res.data as any).message as string | undefined;
          const status = (res.data as any).status as
            | 'created'
            | 'updated'
            | 'skipped'
            | 'conflict'
            | undefined;

          const mapped:
            | 'success'
            | 'updated'
            | 'skipped'
            | 'error'
            | 'conflict' =
            status === 'updated'
              ? 'updated'
              : status === 'skipped'
                ? 'skipped'
                : status === 'conflict'
                  ? 'conflict'
                  : updated
                    ? 'updated'
                    : 'success';

          const source = email ? 'email' : phone_number ? 'phone' : 'unknown';
          const messageWithSource = message
            ? `${message} (source=${source})`
            : undefined;
          setResults(prev => [
            ...prev,
            {
              index: i + 1,
              status: mapped,
              message: messageWithSource,
              email: email,
            },
          ]);
        } else {
          setResults(prev => [
            ...prev,
            {
              index: i + 1,
              status: 'error',
              message: res.error || 'Bilinmeyen hata',
            },
          ]);
        }
      } catch (e: any) {
        setResults(prev => [
          ...prev,
          { index: i + 1, status: 'error', message: e?.message || 'Hata' },
        ]);
      }
    }

    setIsProcessing(false);

    toast.success('İçe aktarma işlemi tamamlandı');
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>CSV Yükle</Label>
        <div className="flex items-center gap-2">
          <Input
            ref={fileInputRef}
            type="file"
            accept=".csv"
            onChange={e => {
              const f = e.target.files?.[0];
              if (f) handleFile(f);
            }}
          />
          <Button
            variant="secondary"
            onClick={() => fileInputRef.current?.click()}
          >
            <FileSpreadsheet className="mr-2 h-4 w-4" /> Dosya Seç
          </Button>
        </div>
        <p className="text-muted-foreground text-sm">
          Zorunlu sütunlar: full_name veya first_name + last_name. Ayrıca email
          veya phone_number&apos;dan en az biri gereklidir.
        </p>
      </div>

      {rows.length > 0 && (
        <div className="rounded-md border p-4">
          <div className="text-muted-foreground mb-3 text-sm">
            İşlenecek satır: {rows.length}
          </div>
          <Button onClick={onImport} disabled={isProcessing}>
            {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            İçe Aktarmayı Başlat
          </Button>
        </div>
      )}

      {isProcessing && (
        <div className="space-y-2">
          <Progress value={(results.length / rows.length) * 100} />
          <div className="text-muted-foreground text-sm">
            {results.length}/{rows.length} işlendi — Başarılı: {successCount},
            Hata: {errorCount}, Atlanan: {skippedCount}
          </div>
        </div>
      )}

      {results.length > 0 && (
        <div className="rounded-md border p-4">
          <div className="mb-2 flex items-center justify-between">
            <div className="text-sm font-medium">Sonuçlar</div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const problemRows = results.filter(
                  r =>
                    r.status === 'error' ||
                    r.status === 'conflict' ||
                    r.status === 'skipped'
                );
                if (problemRows.length === 0) {
                  toast.info('Problemli satır yok');
                  return;
                }
                const header = [
                  'row',
                  'status',
                  'message',
                  'email',
                  'phone_number',
                  'conflict_reason',
                ];
                const lines = [header.join(',')];
                for (const pr of problemRows) {
                  const phone = rows[pr.index - 1]
                    ? getNormalizedPhone(rows[pr.index - 1]) || ''
                    : '';
                  const cols = [
                    String(pr.index),
                    pr.status,
                    pr.message || '',
                    pr.email || '',
                    phone,
                    // conflict_reason: res.data.status==='conflict' mesajından parse edilmişti; pr.message içeriyor.
                    pr.status === 'conflict'
                      ? (pr.message || '').replace(/^Conflict:\s*/, '')
                      : '',
                  ];
                  // Basit CSV kaçış: virgül ve tırnaklar için tırnak içine al
                  const safe = cols.map(v => {
                    if (v.includes(',') || v.includes('"')) {
                      return '"' + v.replace(/"/g, '""') + '"';
                    }
                    return v;
                  });
                  lines.push(safe.join(','));
                }
                const blob = new Blob([lines.join('\n')], {
                  type: 'text/csv;charset=utf-8;',
                });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'error.csv';
                a.click();
                URL.revokeObjectURL(url);
              }}
            >
              Hata/Çatışma Satırlarını İndir (error.csv)
            </Button>
          </div>
          <ul className="space-y-1 text-sm">
            {results.map((r, idx) => (
              <li key={idx} className="flex items-start gap-2">
                <span className="text-muted-foreground">Satır {r.index}:</span>
                <span
                  className={
                    r.status === 'success'
                      ? 'text-green-600'
                      : r.status === 'updated'
                        ? 'text-blue-600'
                        : r.status === 'skipped'
                          ? 'text-amber-600'
                          : r.status === 'conflict'
                            ? 'text-orange-600'
                            : 'text-red-600'
                  }
                >
                  {r.status}
                </span>
                {r.message && (
                  <span className="text-muted-foreground">— {r.message}</span>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}

      <div className="text-muted-foreground flex items-center gap-2 text-xs">
        <AlertCircle className="h-4 w-4" />
        Not: Büyük dosyalarda performans için daha gelişmiş ayrıştırma ve arka
        plan işleme ekleyebiliriz.
      </div>
    </div>
  );
}
