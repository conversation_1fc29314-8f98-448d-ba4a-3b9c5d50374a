import { Profiles } from '@/types';
import React from 'react';

/**
 * Header Types - Temizlenmiş ve sadece gere<PERSON> olan<PERSON>
 */

// 🏋️ Role & Gym Types
export interface RoleOption {
  role: string;
  label: string;
  href: string;
  icon: React.ReactNode;
}

export interface GymData {
  id: string;
  name: string;
  slug: string;
  city: string;
  district: string;
}

// 🔐 Auth Status - Tek source of truth
export interface HeaderAuthStatus {
  authenticated: boolean;
  profile: Profiles | null;
  roleOptions: RoleOption[];
  managedGyms: GymData[];
  trainerGyms: GymData[];
  isManager: boolean;
  isTrainer: boolean;
  isMember: boolean;
}
