@import 'tw-animate-css';
@import 'tailwindcss';
@custom-variant dark (&:is(.dark *));

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* 404 Page Animation Utilities */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glitch-1 {
    animation: glitch-1 0.3s ease-in-out infinite alternate;
  }

  .animate-glitch-2 {
    animation: glitch-2 0.3s ease-in-out infinite alternate-reverse;
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
    opacity: 0;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

/* Modern Auth Animations */
@keyframes slide-in-from-bottom-4 {
  from {
    transform: translateY(1rem);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-top-2 {
  from {
    transform: translateY(-0.5rem);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-right-2 {
  from {
    transform: translateX(0.5rem);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-top-1 {
  from {
    transform: translateY(-0.25rem);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 404 Page Animations */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) rotate(2deg);
  }
  50% {
    transform: translateY(-20px) rotate(0deg);
  }
  75% {
    transform: translateY(-10px) rotate(-2deg);
  }
}

@keyframes glitch-1 {
  0%,
  100% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
}

@keyframes glitch-2 {
  0%,
  100% {
    transform: translate(0);
  }
  20% {
    transform: translate(2px, -2px);
  }
  40% {
    transform: translate(2px, 2px);
  }
  60% {
    transform: translate(-2px, -2px);
  }
  80% {
    transform: translate(-2px, 2px);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Grid pattern for background */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(var(--primary) / 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(var(--primary) / 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }

  /* Focus styles for better accessibility */
  *:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Reduced motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    html {
      scroll-behavior: auto;
    }

    *,
    *::before,
    *::after {
      animation-duration: 0.01ms;
      animation-iteration-count: 1;
      transition-duration: 0.01ms;
    }
  }
}
:root {
  --background: oklch(0.9885 0.0057 84.5659);
  --foreground: oklch(0.3660 0.0251 49.6085);
  --card: oklch(0.9686 0.0091 78.2818);
  --card-foreground: oklch(0.3660 0.0251 49.6085);
  --popover: oklch(0.9686 0.0091 78.2818);
  --popover-foreground: oklch(0.3660 0.0251 49.6085);
  --primary: oklch(0.5553 0.1455 48.9975);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.8276 0.0752 74.4400);
  --secondary-foreground: oklch(0.4444 0.0096 73.6390);
  --muted: oklch(0.9363 0.0218 83.2637);
  --muted-foreground: oklch(0.5534 0.0116 58.0708);
  --accent: oklch(0.9000 0.0500 74.9889);
  --accent-foreground: oklch(0.4444 0.0096 73.6390);
  --destructive: oklch(0.4437 0.1613 26.8994);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8866 0.0404 89.6994);
  --input: oklch(0.8866 0.0404 89.6994);
  --ring: oklch(0.5553 0.1455 48.9975);
  --chart-1: oklch(0.5553 0.1455 48.9975);
  --chart-2: oklch(0.5534 0.0116 58.0708);
  --chart-3: oklch(0.5538 0.1207 66.4416);
  --chart-4: oklch(0.5534 0.0116 58.0708);
  --chart-5: oklch(0.6806 0.1423 75.8340);
  --sidebar: oklch(0.9363 0.0218 83.2637);
  --sidebar-foreground: oklch(0.3660 0.0251 49.6085);
  --sidebar-primary: oklch(0.5553 0.1455 48.9975);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.5538 0.1207 66.4416);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.8866 0.0404 89.6994);
  --sidebar-ring: oklch(0.5553 0.1455 48.9975);

  /* Semantic Colors - Light Theme */
  --success: oklch(0.5486 0.1455 142.4975);
  --success-foreground: oklch(1 0 0);
  --warning: oklch(0.6806 0.1423 75.834);
  --warning-foreground: oklch(0.2161 0.0061 56.0434);
  --error: oklch(0.4437 0.1613 26.8994);
  --error-foreground: oklch(1 0 0);
  --info: oklch(0.5553 0.1455 48.9975);
  --info-foreground: oklch(1 0 0);

  /* Role-specific colors */
  --member: oklch(0.5553 0.1455 48.9975);
  --member-foreground: oklch(1 0 0);
  --trainer: oklch(0.6806 0.1423 75.834);
  --trainer-foreground: oklch(0.2161 0.0061 56.0434);
  --manager: oklch(0.5538 0.1207 66.4416);
  --manager-foreground: oklch(1 0 0);

  --font-sans: Oxanium, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Fira Code, monospace;
  --radius: 0.3rem;
  --shadow-2xs: 0px 2px 3px 0px hsl(28 18% 25% / 0.09);
  --shadow-xs: 0px 2px 3px 0px hsl(28 18% 25% / 0.09);
  --shadow-sm: 0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 1px 2px -1px hsl(28 18% 25% / 0.18);
  --shadow: 0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 1px 2px -1px hsl(28 18% 25% / 0.18);
  --shadow-md: 0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 2px 4px -1px hsl(28 18% 25% / 0.18);
  --shadow-lg: 0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 4px 6px -1px hsl(28 18% 25% / 0.18);
  --shadow-xl: 0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 8px 10px -1px hsl(28 18% 25% / 0.18);
  --shadow-2xl: 0px 2px 3px 0px hsl(28 18% 25% / 0.45);
  --shadow-color: hsl(28 18% 25%);
  --shadow-opacity: 0.18;
  --shadow-blur: 3px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 2px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --tracking-normal: 0em;
}

.dark {
  --background: oklch(0.2161 0.0061 56.0434);
  --foreground: oklch(0.9699 0.0013 106.4238);
  --card: oklch(0.2685 0.0063 34.2976);
  --card-foreground: oklch(0.9699 0.0013 106.4238);
  --popover: oklch(0.2685 0.0063 34.2976);
  --popover-foreground: oklch(0.9699 0.0013 106.4238);
  --primary: oklch(0.7049 0.1867 47.6044);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.4444 0.0096 73.6390);
  --secondary-foreground: oklch(0.9232 0.0026 48.7171);
  --muted: oklch(0.2685 0.0063 34.2976);
  --muted-foreground: oklch(0.7161 0.0091 56.2590);
  --accent: oklch(0.3598 0.0497 229.3202);
  --accent-foreground: oklch(0.9232 0.0026 48.7171);
  --destructive: oklch(0.5771 0.2152 27.3250);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.3741 0.0087 67.5582);
  --input: oklch(0.3741 0.0087 67.5582);
  --ring: oklch(0.7049 0.1867 47.6044);
  --chart-1: oklch(0.7049 0.1867 47.6044);
  --chart-2: oklch(0.6847 0.1479 237.3225);
  --chart-3: oklch(0.7952 0.1617 86.0468);
  --chart-4: oklch(0.7161 0.0091 56.2590);
  --chart-5: oklch(0.5534 0.0116 58.0708);
  --sidebar: oklch(0.2685 0.0063 34.2976);
  --sidebar-foreground: oklch(0.9699 0.0013 106.4238);
  --sidebar-primary: oklch(0.7049 0.1867 47.6044);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.6847 0.1479 237.3225);
  --sidebar-accent-foreground: oklch(0.2839 0.0734 254.5378);
  --sidebar-border: oklch(0.3741 0.0087 67.5582);
  --sidebar-ring: oklch(0.7049 0.1867 47.6044);

  /* Semantic Colors - Dark Theme */
  --success: oklch(0.6486 0.1455 142.4975);
  --success-foreground: oklch(0.2161 0.0061 56.0434);
  --warning: oklch(0.7806 0.1423 75.834);
  --warning-foreground: oklch(0.2161 0.0061 56.0434);
  --error: oklch(0.5771 0.2152 27.325);
  --error-foreground: oklch(1 0 0);
  --info: oklch(0.7049 0.1867 47.6044);
  --info-foreground: oklch(1 0 0);

  /* Role-specific colors - Dark Theme */
  --member: oklch(0.7049 0.1867 47.6044);
  --member-foreground: oklch(1 0 0);
  --trainer: oklch(0.7806 0.1423 75.834);
  --trainer-foreground: oklch(0.2161 0.0061 56.0434);
  --manager: oklch(0.6847 0.1479 237.3225);
  --manager-foreground: oklch(1 0 0);

  --font-sans: Oxanium, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Fira Code, monospace;
  --radius: 0.3rem;
  --shadow-2xs: 0px 2px 3px 0px hsl(0 0% 5% / 0.09);
  --shadow-xs: 0px 2px 3px 0px hsl(0 0% 5% / 0.09);
  --shadow-sm: 0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 1px 2px -1px hsl(0 0% 5% / 0.18);
  --shadow: 0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 1px 2px -1px hsl(0 0% 5% / 0.18);
  --shadow-md: 0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 2px 4px -1px hsl(0 0% 5% / 0.18);
  --shadow-lg: 0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 4px 6px -1px hsl(0 0% 5% / 0.18);
  --shadow-xl: 0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 8px 10px -1px hsl(0 0% 5% / 0.18);
  --shadow-2xl: 0px 2px 3px 0px hsl(0 0% 5% / 0.45);
  --shadow-color: hsl(0 0% 5%);
  --shadow-opacity: 0.18;
  --shadow-blur: 3px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 2px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Semantic Colors */
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-error: var(--error);
  --color-error-foreground: var(--error-foreground);
  --color-info: var(--info);
  --color-info-foreground: var(--info-foreground);

  /* Role-specific Colors */
  --color-member: var(--member);
  --color-member-foreground: var(--member-foreground);
  --color-trainer: var(--trainer);
  --color-trainer-foreground: var(--trainer-foreground);
  --color-manager: var(--manager);
  --color-manager-foreground: var(--manager-foreground);

  --font-sans: Oxanium, sans-serif;
  --font-mono: Fira Code, monospace;
  --font-serif: Merriweather, serif;

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
  --radius: 0.3rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
}

/* ========================================
   CUSTOM ANIMATIONS FOR LANDING PAGE
   ======================================== */

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-left {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes counter {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation Classes */
.animate-scroll {
  animation: scroll 20s linear infinite;
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-slide-up {
  animation: slide-up 0.8s ease-out;
}

.animate-slide-left {
  animation: slide-left 1s ease-out 0.2s both;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float 3s ease-in-out infinite 1.5s;
}

.animate-counter {
  animation: counter 0.6s ease-out 0.5s both;
}

/* Hover Effects */
.hover-lift:hover {
  transform: translateY(-4px);
  transition: transform 0.3s ease;
}

.transition-smooth {
  transition: all 0.25s ease-out;
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    hsl(var(--primary) / 0.6) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Shadow Glow */
.shadow-glow {
  box-shadow: 0 10px 25px -5px hsl(var(--primary) / 0.3);
}

/* Font Size Variations */
[data-font-size='small'] {
  font-size: 0.875rem; /* 14px */
}

[data-font-size='small'] .text-sm {
  font-size: 0.75rem; /* 12px */
}

[data-font-size='small'] .text-lg {
  font-size: 1rem; /* 16px */
}

[data-font-size='large'] {
  font-size: 1.125rem; /* 18px */
}

[data-font-size='large'] .text-sm {
  font-size: 1rem; /* 16px */
}

[data-font-size='large'] .text-lg {
  font-size: 1.25rem; /* 20px */
}

/* Wizard Animations */
@keyframes step-slide-in {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes step-slide-out {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-20px);
  }
}

@keyframes progress-fill {
  from {
    width: 0%;
  }
  to {
    width: var(--progress-width);
  }
}

@keyframes card-hover {
  from {
    transform: translateY(0) scale(1);
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  }
  to {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

@keyframes success-bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.animate-step-slide-in {
  animation: step-slide-in 0.4s ease-out;
}

.animate-step-slide-out {
  animation: step-slide-out 0.3s ease-in;
}

.animate-card-hover {
  transition: all 0.2s ease-out;
}

.animate-card-hover:hover {
  animation: card-hover 0.2s ease-out forwards;
}

.animate-pulse-ring {
  animation: pulse-ring 1.5s ease-out infinite;
}

.animate-success-bounce {
  animation: success-bounce 1s ease-out;
}

/* Wizard Step Transitions */
.wizard-step-enter {
  opacity: 0;
  transform: translateX(20px);
  transition: all 0.3s ease-out;
}

.wizard-step-enter-active {
  opacity: 1;
  transform: translateX(0);
}

.wizard-step-exit {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease-in;
}

.wizard-step-exit-active {
  opacity: 0;
  transform: translateX(-20px);
}

/* Animation Controls */
[data-animations='false'] * {
  animation-duration: 0.01ms;
  animation-iteration-count: 1;
  transition-duration: 0.01ms;
}

[data-animations='false'] *::before,
[data-animations='false'] *::after {
  animation-duration: 0.01ms;
  animation-iteration-count: 1;
  transition-duration: 0.01ms;
}