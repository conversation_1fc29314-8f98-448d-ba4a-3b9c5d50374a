'use client';

import { useActionState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Building, MapPin, Star, Clock } from 'lucide-react';
import { CityDistrictSelector } from '@/components/ui/city-district-selector';
import { toast } from 'sonner';
import { useEffect, useState, useCallback, useMemo, memo, useRef } from 'react';
import { Gyms } from '@/types/database/tables';
import { ApiResponse } from '@/types/global/api';
import { FEATURE_GROUPS, GYM_TYPES } from '@/lib/constants';

interface GymSettingsFormProps {
  gym: Gyms | null;
  updateAction: (
    prevState: any,
    formData: FormData
  ) => Promise<ApiResponse<Gyms>>;
}

const initialState: ApiResponse<Gyms> = {
  success: false,
  message: '',
  error: undefined,
  data: undefined,
};

// Form verilerini karşılaştırmak için utility fonksiyonu
const areArraysEqual = (arr1: string[], arr2: string[]): boolean => {
  if (arr1.length !== arr2.length) return false;
  const sorted1 = [...arr1].sort();
  const sorted2 = [...arr2].sort();
  return sorted1.every((item, index) => item === sorted2[index]);
};

const hasFormChanged = (
  gym: Gyms | null,
  formData: any,
  features: string[],
  city: string,
  district: string
): boolean => {
  if (!gym) return false;
  // Form alanları karşılaştırması
  const originalFormData = {
    name: gym.name || '',
    gym_type: gym.gym_type || '',
    slug: gym.slug || '',
    status: gym.status || 'active',
    description: gym.description || '',
    address: gym.address || '',
    gym_phone: gym.gym_phone || '',
    max_capacity: gym.max_capacity?.toString() || '',
    opening_time: gym.opening_time ? gym.opening_time.substring(0, 5) : '',
    closing_time: gym.closing_time ? gym.closing_time.substring(0, 5) : '',
  };

  for (const [key, value] of Object.entries(formData)) {
    if (value !== originalFormData[key as keyof typeof originalFormData]) {
      return true;
    }
  }

  // Özellikler karşılaştırması
  if (!areArraysEqual(features, gym.features || [])) return true;

  // Şehir ve ilçe karşılaştırması
  if (city !== (gym.city || '')) return true;
  if (district !== (gym.district || '')) return true;

  return false;
};

// Memoized FeatureCheckbox component for better performance
const FeatureCheckbox = memo(
  ({
    feature,
    isChecked,
    onToggle,
  }: {
    feature: string;
    isChecked: boolean;
    onToggle: (feature: string) => void;
  }) => (
    <div className="border-border hover:bg-muted/50 flex items-center space-x-3 rounded-lg border p-3 transition-colors">
      <Checkbox
        id={feature}
        checked={isChecked}
        onCheckedChange={() => onToggle(feature)}
      />
      <Label
        htmlFor={feature}
        className="flex-1 cursor-pointer text-sm font-normal"
      >
        {feature}
      </Label>
    </div>
  )
);

FeatureCheckbox.displayName = 'FeatureCheckbox';

export function GymSettingsForm({ gym, updateAction }: GymSettingsFormProps) {
  // Form ref'i
  const formRef = useRef<HTMLFormElement>(null);

  // Wrapper function to handle the action state pattern
  const wrappedAction = async (
    prevState: ApiResponse<Gyms>,
    formData: FormData
  ) => {
    return await updateAction(prevState, formData);
  };

  const [state, formAction, pending] = useActionState(
    wrappedAction,
    initialState
  );
  const [features, setFeatures] = useState<string[]>(gym?.features || []);
  const [city, setCity] = useState(gym?.city || '');
  const [district, setDistrict] = useState(gym?.district || '');

  const [formData, setFormData] = useState({
    name: gym?.name || '',
    gym_type: gym?.gym_type || '',
    slug: gym?.slug || '',
    status: gym?.status || 'active',
    description: gym?.description || '',
    address: gym?.address || '',
    gym_phone: gym?.gym_phone || '',
    max_capacity: gym?.max_capacity?.toString() || '',
    opening_time: gym?.opening_time ? gym.opening_time.substring(0, 5) : '',
    closing_time: gym?.closing_time ? gym.closing_time.substring(0, 5) : '',
  });

  // Gym prop'u değiştiğinde local state'i güncelle
  useEffect(() => {
    if (!gym) return;

    setFeatures(gym.features || []);
    setCity(gym.city || '');
    setDistrict(gym.district || '');

    setFormData({
      name: gym.name || '',
      gym_type: gym.gym_type || '',
      slug: gym.slug || '',
      status: gym.status || 'active',
      description: gym.description || '',
      address: gym.address || '',
      gym_phone: gym.gym_phone || '',
      max_capacity: gym.max_capacity?.toString() || '',
      opening_time: gym.opening_time ? gym.opening_time.substring(0, 5) : '',
      closing_time: gym.closing_time ? gym.closing_time.substring(0, 5) : '',
    });
  }, [gym]);

  // Handle server action response
  useEffect(() => {
    if (state.success) {
      toast.success('Salon ayarları başarıyla güncellendi');

      // Server'dan dönen güncellenmiş veriyle local state'i senkronize et
      if (state.data) {
        setFeatures(state.data.features || []);
        setCity(state.data.city || '');
        setDistrict(state.data.district || '');

        setFormData({
          name: state.data.name || '',
          gym_type: state.data.gym_type || '',
          slug: state.data.slug || '',
          status: state.data.status || 'active',
          description: state.data.description || '',
          address: state.data.address || '',
          gym_phone: state.data.gym_phone || '',
          max_capacity: state.data.max_capacity?.toString() || '',
          opening_time: state.data.opening_time
            ? state.data.opening_time.substring(0, 5)
            : '',
          closing_time: state.data.closing_time
            ? state.data.closing_time.substring(0, 5)
            : '',
        });
      }
    } else if (state.error) {
      toast.error(state.error);
    }
  }, [state]);

  // Features set'ini memoize et - daha hızlı lookup için
  const featuresSet = useMemo(() => new Set(features), [features]);

  // Form değişiklik durumunu kontrol et
  const isFormChanged = useMemo(() => {
    return hasFormChanged(gym, formData, features, city, district);
  }, [gym, formData, features, city, district]);

  const handleInputChange = useCallback((field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  }, []);

  const handleFeatureToggle = useCallback((feature: string) => {
    setFeatures(prev =>
      prev.includes(feature)
        ? prev.filter(f => f !== feature)
        : [...prev, feature]
    );
  }, []);

  const handleCityChange = (newCity: string) => {
    setCity(newCity);
    setDistrict(''); // Reset district when city changes
  };

  const handleDistrictChange = (newDistrict: string) => {
    setDistrict(newDistrict);
  };

  if (!gym) {
    return <div>Salon bilgileri yükleniyor...</div>;
  }

  return (
    <form ref={formRef} action={formAction} className="space-y-6">
      {/* Hidden gym ID */}
      <input type="hidden" name="gymId" value={gym.id} />

      {/* Hidden features array */}
      <input type="hidden" name="features" value={JSON.stringify(features)} />

      {/* Hidden city and district */}
      <input type="hidden" name="city" value={city} />
      <input type="hidden" name="district" value={district} />

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            Temel Bilgiler
          </CardTitle>
          <CardDescription>
            Salonunuzun temel bilgilerini güncelleyin
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Salon Adı</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={e => handleInputChange('name', e.target.value)}
                placeholder="Salon adı"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="gym_type">Salon Türü</Label>
              <Select
                name="gym_type"
                value={formData.gym_type}
                onValueChange={value => handleInputChange('gym_type', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Salon türü seçin" />
                </SelectTrigger>
                <SelectContent>
                  {GYM_TYPES.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="slug">Salon URL Slug</Label>
              <Input
                id="slug"
                name="slug"
                value={formData.slug}
                onChange={e => handleInputChange('slug', e.target.value)}
                placeholder="salon-url-slug"
              />
              <p className="text-muted-foreground text-sm">
                Salon URL&apos;inde kullanılacak benzersiz isim (sadece küçük
                harf, rakam ve tire)
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">Salon Durumu</Label>
              <Select
                name="status"
                value={formData.status}
                onValueChange={value => handleInputChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Durum seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Aktif</SelectItem>
                  <SelectItem value="passive">Pasif</SelectItem>
                  <SelectItem value="deleted">Silinmiş</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Açıklama</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={e => handleInputChange('description', e.target.value)}
              placeholder="Salon hakkında açıklama..."
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="max_capacity">Maksimum Kapasite</Label>
            <Input
              id="max_capacity"
              name="max_capacity"
              type="number"
              min="1"
              value={formData.max_capacity}
              onChange={e => handleInputChange('max_capacity', e.target.value)}
              placeholder="Örn: 100"
            />
            <p className="text-muted-foreground text-sm">
              Salonunuzun aynı anda alabileceği maksimum kişi sayısı
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Location Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Konum Bilgileri
          </CardTitle>
          <CardDescription>
            Salonunuzun konum bilgilerini güncelleyin
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <CityDistrictSelector
            cityValue={city}
            districtValue={district}
            onCityChange={handleCityChange}
            onDistrictChange={handleDistrictChange}
          />

          <div className="space-y-2">
            <Label htmlFor="address">Adres</Label>
            <Textarea
              id="address"
              name="address"
              value={formData.address}
              onChange={e => handleInputChange('address', e.target.value)}
              placeholder="Detaylı adres bilgisi..."
              rows={2}
            />
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="gym_phone">Telefon</Label>
              <Input
                id="gym_phone"
                name="gym_phone"
                type="tel"
                value={formData.gym_phone}
                onChange={e => handleInputChange('gym_phone', e.target.value)}
                placeholder="Salon telefonu"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Working Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Çalışma Saatleri
          </CardTitle>
          <CardDescription>
            Salonunuzun çalışma saatlerini belirleyin
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="opening_time">Açılış Saati</Label>
              <Input
                id="opening_time"
                name="opening_time"
                type="time"
                value={formData.opening_time}
                onChange={e =>
                  handleInputChange('opening_time', e.target.value)
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="closing_time">Kapanış Saati</Label>
              <Input
                id="closing_time"
                name="closing_time"
                type="time"
                value={formData.closing_time}
                onChange={e =>
                  handleInputChange('closing_time', e.target.value)
                }
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Features */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            Salon Özellikleri
          </CardTitle>
          <CardDescription>
            Salonunuzda bulunan ekipman ve hizmetleri seçin
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {Object.entries(FEATURE_GROUPS).map(([key, group]) => (
            <div key={key} className="space-y-4">
              <h4 className="text-foreground text-base font-medium">
                {group.label}
              </h4>
              <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
                {group.features.map(feature => (
                  <FeatureCheckbox
                    key={feature}
                    feature={feature}
                    isChecked={featuresSet.has(feature)}
                    onToggle={handleFeatureToggle}
                  />
                ))}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" disabled={pending || !isFormChanged}>
          {pending ? 'Kaydediliyor...' : 'Değişiklikleri Kaydet'}
        </Button>
      </div>
    </form>
  );
}
