/**
 * Date utility types
 *
 * Types for date handling, formatting, and time-related operations
 */

/**
 * Date format options
 */
export type DateFormat =
  | 'short'
  | 'medium'
  | 'long'
  | 'full'
  | 'iso'
  | 'relative';

/**
 * Time period types
 */
export type TimePeriod = 'day' | 'week' | 'month' | 'quarter' | 'year';

/**
 * Date range interface
 */
export interface DateRange {
  start: Date;
  end: Date;
}

/**
 * Time zone type
 */
export type TimeZone = string; // e.g., 'Europe/Istanbul', 'UTC'

/**
 * Calendar event interface
 */
export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  allDay?: boolean;
  description?: string;
}

/**
 * Working hours interface
 */
export interface WorkingHours {
  day: number; // 0-6 (Sunday-Saturday)
  start: string; // HH:mm format
  end: string; // HH:mm format
  isWorkingDay: boolean;
}
