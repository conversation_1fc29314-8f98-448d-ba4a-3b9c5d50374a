'use client';

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Building2, 
  Users, 
  TrendingUp, 
  Calendar,
  MapPin,
  Phone,
  Clock,
  Settings,
  Eye
} from 'lucide-react';
import Link from 'next/link';
import { Gyms } from '@/types/database/tables';

interface GymManagerStats {
  totalGyms: number;
  totalMembers: number;
  totalRevenue: number;
  todayAppointments: number;
}

interface GymManagerDashboardProps {
  gyms: Gyms[];
  stats: GymManagerStats | null;
}

export function GymManagerDashboard({ gyms, stats }: GymManagerDashboardProps) {
  return (
    <div className="container mx-auto space-y-8 px-4 md:px-6">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Salon Yöneticisi Paneli</h1>
        <p className="text-muted-foreground">
          Yönettiğiniz salonların genel durumunu görüntüleyin ve yönetin.
        </p>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Toplam Salon</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalGyms}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Toplam Üye</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalMembers}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Toplam Gelir</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">₺{stats.totalRevenue.toLocaleString()}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Bugünkü Randevular</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.todayAppointments}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Gyms List */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold tracking-tight">Yönettiğiniz Salonlar</h2>
        </div>
        
        {gyms.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">Henüz salon yönetmiyorsunuz</h3>
              <p className="text-muted-foreground text-center">
                Şirket yöneticisi tarafından size salon yöneticiliği atanması bekleniyor.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {gyms.map((gym) => (
              <Card key={gym.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <CardTitle className="text-lg">{gym.name}</CardTitle>
                      <Badge variant={gym.status === 'active' ? 'default' : 'secondary'}>
                        {gym.status === 'active' ? 'Aktif' : 'Pasif'}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Gym Info */}
                  <div className="space-y-2 text-sm">
                    {gym.address && (
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">{gym.address}</span>
                      </div>
                    )}
                    
                    {gym.gym_phone && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">{gym.gym_phone}</span>
                      </div>
                    )}
                    
                    {(gym.opening_time && gym.closing_time) && (
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">
                          {gym.opening_time} - {gym.closing_time}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <Button asChild size="sm" className="flex-1">
                      <Link href={`/dashboard/gym/${gym.id}`}>
                        <Eye className="h-4 w-4 mr-2" />
                        Görüntüle
                      </Link>
                    </Button>
                    
                    <Button asChild variant="outline" size="sm" className="flex-1">
                      <Link href={`/dashboard/gym/${gym.id}/settings`}>
                        <Settings className="h-4 w-4 mr-2" />
                        Ayarlar
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
