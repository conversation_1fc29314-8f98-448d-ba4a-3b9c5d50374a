import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { CheckCircle, Clock, Inbox, Search, Send } from 'lucide-react';
import Link from 'next/link';
import {
  IncomingInvitationCard,
  OutgoingInvitationCard,
} from './invitation-card-components';
import {
  EmptyIncomingInvitations,
  EmptyOutgoingInvitations,
} from './invitation-empty-states';

import { getAuthenticatedUser } from '@/lib/auth/server-auth';
import {
  getUserIncomingInvites,
  getUserOutgoingRequests,
} from '@/lib/actions/gym_invitations/invitation-actions';
import {
  IncomingInvitation,
  OutgoingInvitation,
} from '@/lib/actions/gym_invitations/invitation-types';

export async function MemberInvitationsHeader() {
  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
      <div>
        <h1 className="text-primary text-4xl font-bold">Salon Davetleri</h1>
        <p className="text-muted-foreground">
          Aldığınız ve gönderdiğiniz salon davetlerini görüntüleyin ve yönetin
        </p>
      </div>
      <Link href="/findGym">
        <Button className="shadow-lg duration-300">
          <Search className="mr-2 h-4 w-4" />
          Yeni Salon Bul
        </Button>
      </Link>
    </div>
  );
}

// Incoming Invitations Server Component
export async function IncomingInvitationsData() {
  let invitations: IncomingInvitation[] = [];
  try {
    const user = await getAuthenticatedUser();
    if (!user?.id) {
      return <EmptyIncomingInvitations />;
    }

    const result = await getUserIncomingInvites('member');
    invitations =
      (result.success
        ? (result.data as unknown as IncomingInvitation[])
        : []) ?? [];
  } catch (error) {
    return <EmptyIncomingInvitations />;
  }

  if (!invitations || invitations.length === 0) {
    return <EmptyIncomingInvitations />;
  }

  // Davetleri duruma göre ayır
  const pendingInvitations = invitations.filter(inv => {
    const isExpired = inv.expires_at && new Date(inv.expires_at) < new Date();
    return inv.status === 'pending' && !isExpired;
  });
  const processedInvitations = invitations.filter(inv => {
    const isExpired = inv.expires_at && new Date(inv.expires_at) < new Date();
    return inv.status !== 'pending' || isExpired;
  });

  return (
    <div className="space-y-6">
      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="bg-muted/30 rounded-xl">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Davet</CardTitle>
            <Inbox className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{invitations.length}</div>
            <p className="text-muted-foreground text-xs">
              Aldığınız tüm davetler
            </p>
          </CardContent>
        </Card>

        <Card className="bg-muted/30 rounded-xl">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bekleyen</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {pendingInvitations.length}
            </div>
            <p className="text-muted-foreground text-xs">Yanıt bekleniyor</p>
          </CardContent>
        </Card>

        <Card className="bg-muted/30 rounded-xl">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">İşlenen</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {processedInvitations.length}
            </div>
            <p className="text-muted-foreground text-xs">Yanıtlanan davetler</p>
          </CardContent>
        </Card>
      </div>

      {/* Pending Invitations */}
      {pendingInvitations.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Bekleyen Davetler</h2>
            <Badge className="border-yellow-200 bg-yellow-100 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200">
              {pendingInvitations.length} Davet
            </Badge>
          </div>
          <div className="space-y-4">
            {pendingInvitations.map(invitation => (
              <IncomingInvitationCard
                key={invitation.id}
                invitation={invitation}
              />
            ))}
          </div>
        </div>
      )}

      {/* Processed Invitations */}
      {processedInvitations.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Geçmiş Davetler</h2>
            <Badge variant="secondary">
              {processedInvitations.length} Davet
            </Badge>
          </div>
          <div className="space-y-4">
            {processedInvitations.map(invitation => (
              <IncomingInvitationCard
                key={invitation.id}
                invitation={invitation}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Outgoing Invitations Server Component
export async function OutgoingInvitationsData() {
  let invitations: OutgoingInvitation[] = [];
  try {
    const user = await getAuthenticatedUser();
    if (!user?.id) {
      return <EmptyOutgoingInvitations />;
    }

    const result = await getUserOutgoingRequests('member');
    invitations =
      (result.success
        ? (result.data as unknown as OutgoingInvitation[])
        : []) ?? [];
  } catch (error) {
    return <EmptyOutgoingInvitations />;
  }

  if (!invitations || invitations.length === 0) {
    return <EmptyOutgoingInvitations />;
  }

  // Davetleri duruma göre ayır
  const pendingInvitations = invitations.filter(inv => {
    const isExpired = inv.expires_at && new Date(inv.expires_at) < new Date();
    return inv.status === 'pending' && !isExpired;
  });
  const processedInvitations = invitations.filter(inv => {
    const isExpired = inv.expires_at && new Date(inv.expires_at) < new Date();
    return inv.status !== 'pending' || isExpired;
  });

  return (
    <div className="space-y-6">
      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="bg-muted/30 rounded-xl">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Davet</CardTitle>
            <Send className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{invitations.length}</div>
            <p className="text-muted-foreground text-xs">
              Gönderdiğiniz tüm davetler
            </p>
          </CardContent>
        </Card>

        <Card className="bg-muted/30 rounded-xl">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bekleyen</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {pendingInvitations.length}
            </div>
            <p className="text-muted-foreground text-xs">Yanıt bekleniyor</p>
          </CardContent>
        </Card>

        <Card className="bg-muted/30 rounded-xl">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Yanıtlanan</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {processedInvitations.length}
            </div>
            <p className="text-muted-foreground text-xs">Salon yanıtları</p>
          </CardContent>
        </Card>
      </div>

      {/* Pending Invitations */}
      {pendingInvitations.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Bekleyen Davetler</h2>
            <Badge className="border-yellow-200 bg-yellow-100 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200">
              {pendingInvitations.length} Davet
            </Badge>
          </div>
          <div className="space-y-4">
            {pendingInvitations.map(invitation => (
              <OutgoingInvitationCard
                key={invitation.id}
                invitation={invitation}
              />
            ))}
          </div>
        </div>
      )}

      {/* Processed Invitations */}
      {processedInvitations.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Geçmiş Davetler</h2>
            <Badge variant="secondary">
              {processedInvitations.length} Davet
            </Badge>
          </div>
          <div className="space-y-4">
            {processedInvitations.map(invitation => (
              <OutgoingInvitationCard
                key={invitation.id}
                invitation={invitation}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
