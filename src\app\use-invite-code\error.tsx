'use client';

import { RouteError } from '@/components/errors/route-error';

export default function UseInviteCodeError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <RouteError
      title="Davet Kodu Hatası"
      description="Davet kodu işlenirken bir hata oluştu."
      error={error}
      reset={reset}
      context={{ route: '/use-invite-code' }}
    />
  );
}
