import { AuthErrorHandler } from '@/components/auth/auth-error-handler';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Hata | Sportiva',
  description: 'Bir hata oluştu.',
};

interface ErrorPageProps {
  searchParams: {
    type?: string;
    message?: string;
  };
}

export default async function ErrorPage({ searchParams }: ErrorPageProps) {
  const params = await searchParams;
  const errorType = params.type || 'unknown_error';
  const customMessage = params.message;

  return (
    <div className="bg-background min-h-screen">
      <div className="container mx-auto px-4">
        <AuthErrorHandler
          error={errorType}
          showRetry={true}
          showHome={true}
          showLogin={
            errorType === 'session_expired' ||
            errorType === 'invalid_credentials'
          }
        />

        {/* Custom message varsa göster */}
        {customMessage && (
          <div className="mt-4 text-center">
            <p className="text-muted-foreground text-sm">
              Detay: {decodeURIComponent(customMessage)}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
