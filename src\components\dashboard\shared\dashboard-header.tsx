import { getProfile } from '@/lib/actions/user/profile-actions';

// Dashboard Header Props tipi
interface DashboardHeaderProps {
  mode: 'trainer' | 'manager' | 'member' ;
}

// Header mesajları ve alt mesajları
const headerConfig = {
  trainer: {
    getTitle: (userName: string) => `<PERSON>ş Geldiniz, ${userName}`,
    subtitle: 'Çalıştığınız salonları ve üyeleri yönetin',
    fallbackName: 'Antrenör',
  },
 manager: {
    getTitle: (userName: string) => `Hoş Geldiniz, ${userName}`,
    subtitle: 'Spor salonlarınızı yönetin ve takip edin',
    fallbackName: 'Yönetici',
  },
  member: {
    getTitle: (userName: string) => `Hoş Geldiniz, ${userName}`,
    subtitle: 'Üyeliklerinizi ve fitness yolculuğunuzu takip edin',
    fallbackName: 'Kullanıcı',
  },
} as const;

// Header Skeleton Component
export function DashboardHeaderSkeleton({
  mode,
}: {
  mode: DashboardHeaderProps['mode'];
}) {
  const config = headerConfig[mode];

  return (
    <section>
      <h1 className="text-3xl font-bold tracking-tight">
        Hoş Geldiniz,{' '}
        <div className="bg-muted inline-block h-9 w-64 animate-pulse rounded" />
      </h1>
      <p className="text-muted-foreground mt-1">{config.subtitle}</p>
    </section>
  );
}

// Dashboard Header Server Component
export async function DashboardHeader({ mode }: DashboardHeaderProps) {
  const profile = await getProfile();
  const config = headerConfig[mode];

  // Kullanıcı adını belirle
  const userName =
    (profile.success && profile.data?.full_name) ||
    profile.data?.email ||
    config.fallbackName;

  return (
    <section>
      <h1 className="text-3xl font-bold tracking-tight">
        Hoş Geldiniz, <span className="font-bold">{userName}</span>
      </h1>
      <p className="text-muted-foreground mt-1">{config.subtitle}</p>
    </section>
  );
}
