import { toast } from 'sonner';

export const enhancedToast = {
  success: (message: string, description?: string) => {
    toast.success(message, {
      description,
      duration: 4000,
    });
  },

  error: (message: string, description?: string) => {
    toast.error(message, {
      description,
      duration: 5000,
    });
  },

  warning: (message: string, description?: string) => {
    toast.warning(message, {
      description,
      duration: 4000,
    });
  },

  info: (message: string, description?: string) => {
    toast.info(message, {
      description,
      duration: 3000,
    });
  },

  loading: (message: string) => {
    return toast.loading(message, {
      duration: Infinity,
    });
  },
};

// Gym-specific toast messages
export const gymToasts = {
  memberJoinSuccess: () =>
    enhancedToast.success(
      'Katılım İsteği Gönderildi!',
      'Salon yöneticisi isteğinizi değerlendirip size geri dönecek.'
    ),

  trainerJoinSuccess: () =>
    enhancedToast.success(
      'Antrenör Başvurusu Gönderildi!',
      'Salon yöneticisi başvurunuzu inceleyip size bilgi verecek.'
    ),

  memberJoinError: () =>
    enhancedToast.error(
      'İstek Gönderilemedi',
      'Lütfen daha sonra tekrar deneyiniz veya destek ekibiyle iletişime geçin.'
    ),

  memberRoleRequired: () =>
    enhancedToast.warning(
      'Üye Rolü Gerekli',
      'Salona katılmak için önce üye rolünü almanız gerekiyor.'
    ),

  loginRequired: () =>
    enhancedToast.info(
      'Giriş Yapmanız Gerekiyor',
      'Bu işlemi gerçekleştirmek için lütfen giriş yapın.'
    ),
};
