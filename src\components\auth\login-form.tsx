'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { EnhancedAuthError } from './enhanced-auth-error';

interface LoginFormProps {
  onSubmit: (formData: FormData, type: 'email' | 'phone') => Promise<void>;
  error?: string;
  identifierError?: string;
  passwordError?: string;
  context?: string;
}

/**
 * Detect if input is email or phone number
 */
function detectInputType(value: string): 'email' | 'phone' {
  // Email regex pattern
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  // Phone patterns (Turkish format)
  const phonePattern = /^(\+90|0)?[5][0-9]{9}$/;

  // Remove spaces and common separators for phone detection
  const cleanValue = value.replace(/[\s\-\(\)]/g, '');

  if (emailPattern.test(value)) {
    return 'email';
  } else if (phonePattern.test(cleanValue)) {
    return 'phone';
  }

  // Default to email if unclear
  return 'email';
}

export function LoginForm({
  onSubmit,
  error,
  identifierError,
  passwordError,
  context,
}: LoginFormProps) {
  const router = useRouter();
  const [clientError, setClientError] = useState<string>('');

  const handleSubmit = async (formData: FormData) => {
    const identifier = formData.get('identifier') as string;
    const password = formData.get('password') as string;

    // Clear any previous client errors
    setClientError('');

    // Detect input type
    const inputType = detectInputType(identifier);

    // Create new FormData with correct field name
    const newFormData = new FormData();
    newFormData.append(inputType, identifier);
    newFormData.append('password', password);

    await onSubmit(newFormData, inputType);
  };

  return (
    <div className="space-y-4">
      {/* Enhanced Error Message */}
      {(error || clientError) && (
        <EnhancedAuthError
          error={error || clientError}
          context={{
            action: 'login',
            method: context?.includes('email')
              ? 'email'
              : context?.includes('phone')
                ? 'phone'
                : undefined,
            stage: context?.includes('verification')
              ? 'verification'
              : 'initial',
          }}
          onRetry={() => {
            if (clientError) {
              setClientError('');
            } else {
              router.refresh();
            }
          }}
        />
      )}

      <form action={handleSubmit} className="space-y-4">
        <div>
          <label
            htmlFor="identifier"
            className="text-foreground mb-1 block text-sm font-medium"
          >
            E-posta veya Telefon
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-3 flex items-center">
              <svg
                className="h-4 w-4 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                />
              </svg>
            </div>
            <input
              id="identifier"
              name="identifier"
              type="text"
              placeholder="E-posta veya telefon numarası"
              required
              className="border-border focus:border-primary focus:ring-primary/50 w-full rounded-lg border px-10 py-2.5 text-sm focus:ring-1 focus:outline-none"
            />
          </div>
          {identifierError && (
            <p className="mt-1 text-sm text-red-600">{identifierError}</p>
          )}
        </div>

        <div>
          <div className="mb-1 flex items-center justify-between">
            <label
              htmlFor="password"
              className="text-foreground block text-sm font-medium"
            >
              Şifre
            </label>
            <Link
              href="/auth/forgot-password"
              className="text-primary hover:text-primary/80 text-sm hover:underline"
            >
              Şifremi unuttum?
            </Link>
          </div>
          <div className="relative">
            <div className="absolute inset-y-0 left-3 flex items-center">
              <svg
                className="h-4 w-4 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                />
              </svg>
            </div>
            <input
              id="password"
              name="password"
              type="password"
              placeholder="Şifrenizi girin"
              required
              className="border-border focus:border-primary focus:ring-primary/50 w-full rounded-lg border px-10 py-2.5 pr-10 text-sm focus:ring-1 focus:outline-none"
            />
            <button
              type="button"
              className="absolute inset-y-0 right-3 flex items-center"
            >
              <svg
                className="h-4 w-4 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
            </button>
          </div>
          {passwordError && (
            <p className="mt-1 text-sm text-red-600">{passwordError}</p>
          )}
        </div>

        <button
          type="submit"
          className="bg-primary hover:bg-primary/90 focus:ring-primary/50 w-full rounded-lg px-4 py-2.5 text-sm font-medium text-white transition-colors focus:ring-2 focus:ring-offset-2 focus:outline-none"
        >
          Giriş Yap
        </button>
      </form>
    </div>
  );
}
