'use client';

import { RouteError } from '@/components/errors/route-error';

export default function TrainerError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <RouteError
      title="Eğitmen Paneli Hatası"
      description="Eğitmen paneli içeriği yüklenirken bir hata oluştu."
      error={error}
      reset={reset}
      context={{ route: '/dashboard/trainer' }}
    />
  );
}
