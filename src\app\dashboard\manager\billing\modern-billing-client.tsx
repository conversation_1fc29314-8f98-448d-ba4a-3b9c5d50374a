'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  CheckCircle2, 
  HelpCircle, 
  Star, 
  CreditCard, 
  Users, 
  Building2,
  Calendar,
  TrendingUp,
  Shield,
  Zap,
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import type { PlatformPackages } from '@/types/database/tables';
import { changeCompanyPackage } from '@/lib/actions/business/change-company-package';

interface ModernBillingClientProps {
  packages: PlatformPackages[];
  companyActive: boolean;
  currentPackageId: string | null;
  maxGyms: number | null;
  maxMembers: number | null;
  maxTrainers: number | null;
  maxStaff: number | null;
  maxMonthlyAppointments: number | null;
  usageGyms: number;
  usageMembers: number;
  usageTrainers: number;
  usageStaff: number;
  usageMonthlyAppointments: number;
  daysRemaining: number;
  trialEndsAt: string | null;
}

export function ModernBillingClient({
  packages,
  companyActive,
  currentPackageId,
  maxGyms,
  maxMembers,
  maxTrainers,
  maxStaff,
  maxMonthlyAppointments,
  usageGyms,
  usageMembers,
  usageTrainers,
  usageStaff,
  usageMonthlyAppointments,
  daysRemaining,
  trialEndsAt,
}: ModernBillingClientProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [selectedPackage, setSelectedPackage] =
    useState<PlatformPackages | null>(null);

  const currentPackage = packages.find(p => p.id === currentPackageId);

  const handlePackageChange = async (packageId: string) => {
    setIsLoading(packageId);
    try {
      const result = await changeCompanyPackage(packageId);
      if (result.success) {
        toast.success('Paket başarıyla güncellendi!');
        router.refresh();
      } else {
        toast.error(
          result.error || 'Paket güncellenemedi, lütfen tekrar deneyin.'
        );
      }
    } catch (error) {
      toast.error(
        'Beklenmeyen bir hata oluştu. Lütfen daha sonra tekrar deneyin.'
      );
    } finally {
      setIsLoading(null);
      setSelectedPackage(null);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { staggerChildren: 0.1 } },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      className="min-h-screen bg-background"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className=" mx-auto px-4 py-8">
        {/* Header */}
        <motion.div variants={itemVariants} className="mb-8">
          <div className="flex items-center gap-3 mb-6">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-primary/10">
              <CreditCard className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                Abonelik ve Faturalandırma
              </h1>
              <p className="text-muted-foreground mt-1">
                Planınızı yönetin ve işletmeniz için en iyi paketi seçin
              </p>
            </div>
          </div>
        </motion.div>

        {/* Current Plan Overview */}
        {currentPackage && (
          <motion.div variants={itemVariants} className="mb-8">
            <Card className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 via-primary/3 to-transparent">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                  <div className="flex items-center gap-4">
                    <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-primary/10">
                      <Shield className="h-8 w-8 text-primary" />
                    </div>
                    <div>
                      <div className="flex items-center gap-3 mb-2">
                        <h2 className="text-2xl font-bold">{currentPackage.name}</h2>
                        <Badge variant={companyActive ? 'default' : 'secondary'}>
                          {companyActive ? 'Aktif' : 'Pasif'}
                        </Badge>
                        {trialEndsAt && (
                          <Badge variant="outline">
                            <Calendar className="h-3 w-3 mr-1" />
                            Deneme Süresi
                          </Badge>
                        )}
                      </div>
                      <p className="text-muted-foreground">
                        {currentPackage.duration === 'yearly' ? 'Yıllık' : 'Aylık'} Plan
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex flex-col items-end gap-1 mb-2">
                      <div className="flex flex-col items-end">
                        {/* Monthly price emphasized with 2 free months */}
                        <div className="flex items-baseline gap-2">
                          <span className="text-lg text-muted-foreground line-through">
                            {new Intl.NumberFormat('tr-TR', {
                              style: 'currency',
                              currency: 'TRY',
                              minimumFractionDigits: 0,
                              maximumFractionDigits: 0
                            }).format((currentPackage.price / 10) * 2.5)}
                          </span>
                          <span className="text-3xl font-bold text-primary">
                            {new Intl.NumberFormat('tr-TR', {
                              style: 'currency',
                              currency: 'TRY',
                              minimumFractionDigits: 0,
                              maximumFractionDigits: 0
                            }).format(currentPackage.price / 10)}
                          </span>
                        </div>
                        {/* Annual price and free months info */}
                        <div className="flex flex-col items-center gap-1 mt-2">
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground">
                              {new Intl.NumberFormat('tr-TR', {
                                style: 'currency',
                                currency: 'TRY',
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 0
                              }).format(currentPackage.price)}
                              <span className="text-xs ml-1">/yıl</span>
                            </span>
                            <Badge variant="destructive" className="text-xs">
                              %60 İndirim
                            </Badge>
                          </div>
                          <Badge variant="secondary" className="text-xs mt-1">
                            2 ay ücretsiz
                          </Badge>
                        </div>
                      </div>
                    </div>
                    {trialEndsAt && (
                      <p className="text-sm text-warning">
                        Deneme süresi: {new Date(trialEndsAt).toLocaleDateString('tr-TR')} tarihinde bitiyor
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Usage Statistics */}
        <motion.div variants={itemVariants} className="mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-500/10">
                    <Building2 className="h-5 w-5 text-blue-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Salonlar</p>
                    <p className="text-2xl font-bold">
                      {usageGyms} / {maxGyms ?? '∞'}
                    </p>
                  </div>
                </div>
                <Progress
                  value={maxGyms ? (usageGyms / maxGyms) * 100 : 0}
                  className="h-2"
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-500/10">
                    <Users className="h-5 w-5 text-green-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Üyeler</p>
                    <p className="text-2xl font-bold">
                      {usageMembers} / {maxMembers ?? '∞'}
                    </p>
                  </div>
                </div>
                <Progress
                  value={maxMembers ? (usageMembers / maxMembers) * 100 : 0}
                  className="h-2"
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-500/10">
                    <Shield className="h-5 w-5 text-purple-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Antrenörler</p>
                    <p className="text-2xl font-bold">
                      {usageTrainers} / {maxTrainers ?? '∞'}
                    </p>
                  </div>
                </div>
                <Progress
                  value={maxTrainers ? (usageTrainers / maxTrainers) * 100 : 0}
                  className="h-2"
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-indigo-500/10">
                    <CreditCard className="h-5 w-5 text-indigo-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Personel</p>
                    <p className="text-2xl font-bold">
                      {usageStaff} / {maxStaff ?? '∞'}
                    </p>
                  </div>
                </div>
                <Progress
                  value={maxStaff ? (usageStaff / maxStaff) * 100 : 0}
                  className="h-2"
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-teal-500/10">
                    <Calendar className="h-5 w-5 text-teal-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Aylık Randevu</p>
                    <p className="text-2xl font-bold">
                      {usageMonthlyAppointments} / {maxMonthlyAppointments ?? '∞'}
                    </p>
                  </div>
                </div>
                <Progress
                  value={maxMonthlyAppointments ? (usageMonthlyAppointments / maxMonthlyAppointments) * 100 : 0}
                  className="h-2"
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-orange-500/10">
                    <TrendingUp className="h-5 w-5 text-orange-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {trialEndsAt ? 'Deneme Süresi' : 'Kalan Süre'}
                    </p>
                    <p className="text-2xl font-bold">{daysRemaining} gün</p>
                  </div>
                </div>
                <Progress 
                  value={trialEndsAt ? (daysRemaining / 14) * 100 : (daysRemaining / 30) * 100} 
                  className="h-2"
                />
              </CardContent>
            </Card>
          </div>
        </motion.div>

        {/* Available Plans */}
        <motion.div variants={itemVariants} className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-secondary/10">
                <TrendingUp className="h-5 w-5 text-secondary" />
              </div>
              <div>
                <h2 className="text-2xl font-bold tracking-tight">
                  Mevcut Planlar
                </h2>
                <p className="text-muted-foreground text-sm">
                  İşletmeniz için en uygun paketi seçin
                </p>
              </div>
            </div>
            <Button variant="outline" size="sm">
              <HelpCircle className="mr-2 h-4 w-4" />
              Yardım
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {packages.filter(pkg => pkg.tier !== 'free').map(pkg => {
              const isCurrent = currentPackageId === pkg.id;
              const isRecommended = pkg.name === 'Profesyonel';
              return (
                <motion.div key={pkg.id} variants={itemVariants}>
                  <Card
                    className={cn(
                      'relative flex h-full flex-col transition-all duration-300 hover:shadow-lg hover:-translate-y-1',
                      isCurrent && 'ring-2 ring-primary bg-primary/5',
                      isRecommended && !isCurrent && 'ring-2 ring-secondary/60 bg-secondary/5',
                      !isCurrent && !isRecommended && 'hover:ring-1 hover:ring-primary/30'
                    )}
                  >
                    {/* Corner discount badge to match PricingModern */}
                    <div className="absolute right-3 top-3 z-10">
                      <Badge className="rounded-full bg-primary/15 text-primary border border-primary/30 px-3 py-0.5 text-[11px] font-semibold">
                        %65 İndirim
                      </Badge>
                    </div>
                    {/* Badges */}
                    {isRecommended && !isCurrent && (
                      <div className="absolute -top-3 left-1/2 -translate-x-1/2 z-10">
                        <Badge className="bg-secondary text-secondary-foreground shadow-md">
                          <Star className="mr-1 h-3 w-3" />
                          Tavsiye Edilen
                        </Badge>
                      </div>
                    )}
                    
                    {isCurrent && (
                      <div className="absolute -top-3 left-1/2 -translate-x-1/2 z-10">
                        <Badge className="bg-primary text-primary-foreground shadow-md">
                          <Zap className="mr-1 h-3 w-3" />
                          Mevcut Plan
                        </Badge>
                      </div>
                    )}

                    <CardHeader className="text-center pb-4">
                      <CardTitle className="text-xl font-bold">{pkg.name}</CardTitle>
                      <div className="flex flex-col items-center gap-2">
                        <div className="flex flex-col items-center">
                          {/* Monthly price emphasized with 2 free months */}
                          <div className="flex items-baseline gap-2">
                            <span className="text-lg text-muted-foreground line-through">
                              {new Intl.NumberFormat('tr-TR', {
                                style: 'currency',
                                currency: 'TRY',
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 0
                              }).format((pkg.price / 10) * 2.5)}
                            </span>
                            <span className="text-3xl font-bold text-primary">
                              {new Intl.NumberFormat('tr-TR', {
                                style: 'currency',
                                currency: 'TRY',
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 0
                              }).format(pkg.price / 10)}
                            </span>
                          </div>
                          {/* Annual price and free months info */}
                          <div className="flex flex-col items-center gap-1 mt-2">
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-muted-foreground">
                                {new Intl.NumberFormat('tr-TR', {
                                  style: 'currency',
                                  currency: 'TRY',
                                  minimumFractionDigits: 0,
                                  maximumFractionDigits: 0
                                }).format(pkg.price)}
                                <span className="text-xs ml-1">/yıl</span>
                              </span>
                              <Badge variant="destructive" className="text-xs">
                                %60 İndirim
                              </Badge>
                            </div>
                            <Badge variant="secondary" className="text-xs mt-1">
                              2 ay ücretsiz
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="flex-grow px-6">
                      <div className="space-y-3">
                        {Array.isArray(pkg.features) &&
                          pkg.features.map((feature, idx) => (
                            <div key={idx} className="flex items-start gap-3">
                              <div className="flex h-5 w-5 items-center justify-center rounded-full bg-success/10 mt-0.5 flex-shrink-0">
                                <CheckCircle2 className="h-3 w-3 text-success" />
                              </div>
                              <span className="text-sm leading-relaxed">
                                {String(feature)}
                              </span>
                            </div>
                          ))}
                      </div>
                    </CardContent>
                    
                    <CardFooter className="pt-4 px-6 flex flex-col">
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            className="w-full mt-auto"
                            variant={isCurrent ? 'outline' : 'default'}
                            disabled={isCurrent}
                            onClick={() => setSelectedPackage(pkg)}
                          >
                            {isCurrent ? (
                              <>
                                <Shield className="mr-2 h-4 w-4" />
                                Aktif Plan
                              </>
                            ) : (
                              <>
                                <TrendingUp className="mr-2 h-4 w-4" />
                                İlk ay ücretsiz dene
                              </>
                            )}
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle className="flex items-center gap-2">
                              <TrendingUp className="h-5 w-5 text-primary" />
                              Plan Değişikliğini Onayla
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              <strong>{selectedPackage?.name}</strong> planına geçmek istediğinizden emin misiniz? 
                              Bu işlem hemen uygulanacaktır.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>İptal</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() =>
                                selectedPackage &&
                                handlePackageChange(selectedPackage.id)
                              }
                              disabled={isLoading === selectedPackage?.id}
                            >
                              {isLoading === selectedPackage?.id ? (
                                <>
                                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                                  Yükseltiliyor...
                                </>
                              ) : (
                                <>
                                  <CheckCircle2 className="mr-2 h-4 w-4" />
                                  Onayla ve Yükselt
                                </>
                              )}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </CardFooter>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
}
