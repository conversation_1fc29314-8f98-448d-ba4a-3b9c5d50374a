'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';

export interface Notification {
  id: string;
  user_id: string | null;
  gym_id: string | null;
  title: string;
  message: string;
  is_read: boolean | null;
  created_at: string | null;
}

export interface UseNotificationsReturn {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  deleteAllNotifications: () => Promise<void>;
  refreshNotifications: () => Promise<void>;
}

export function useNotifications(): UseNotificationsReturn {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const supabase = createClient();
  const subscriptionRef = useRef<any>(null);

  // Bildirimleri yükle
  const loadNotifications = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        setError('Kimlik doğrulama hatası');
        return;
      }

      const { data, error: fetchError } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(50);

      if (fetchError) {
        console.error('Bildirimler yüklenirken hata:', fetchError);
        setError('Bildirimler yüklenemedi');
        return;
      }

      const notificationList = data || [];
      setNotifications(notificationList);
      
      // Okunmamış bildirim sayısını hesapla
      const unread = notificationList.filter(n => !n.is_read).length;
      setUnreadCount(unread);

    } catch (err) {
      console.error('Bildirimler yüklenirken hata:', err);
      setError('Beklenmeyen bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  }, [supabase]);

  // Bildirimi okundu olarak işaretle
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        toast.error('Kimlik doğrulama hatası');
        return;
      }

      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Bildirim okundu işaretleme hatası:', error);
        toast.error('Bildirim güncellenemedi');
        return;
      }

      // Local state'i güncelle
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, is_read: true }
            : notification
        )
      );

      // Okunmamış sayısını güncelle
      setUnreadCount(prev => Math.max(0, prev - 1));

    } catch (err) {
      console.error('Bildirim okundu işaretleme hatası:', err);
      toast.error('Beklenmeyen bir hata oluştu');
    }
  }, [supabase]);

  // Tüm bildirimleri okundu olarak işaretle
  const markAllAsRead = useCallback(async () => {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        toast.error('Kimlik doğrulama hatası');
        return;
      }

      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', user.id)
        .eq('is_read', false);

      if (error) {
        console.error('Tüm bildirimler okundu işaretleme hatası:', error);
        toast.error('Bildirimler güncellenemedi');
        return;
      }

      // Local state'i güncelle
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, is_read: true }))
      );
      setUnreadCount(0);

      toast.success('Tüm bildirimler okundu olarak işaretlendi');

    } catch (err) {
      console.error('Tüm bildirimler okundu işaretleme hatası:', err);
      toast.error('Beklenmeyen bir hata oluştu');
    }
  }, [supabase]);

  // Bildirimi sil
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        toast.error('Kimlik doğrulama hatası');
        return;
      }

      // Silinecek bildirimin okunup okunmadığını kontrol et
      const notificationToDelete = notifications.find(n => n.id === notificationId);
      const wasUnread = notificationToDelete && !notificationToDelete.is_read;

      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Bildirim silme hatası:', error);
        toast.error('Bildirim silinemedi');
        return;
      }

      // Local state'i güncelle
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      
      // Eğer silinecek bildirim okunmamışsa sayıyı azalt
      if (wasUnread) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }

      toast.success('Bildirim silindi');

    } catch (err) {
      console.error('Bildirim silme hatası:', err);
      toast.error('Beklenmeyen bir hata oluştu');
    }
  }, [supabase, notifications]);

  // Tüm bildirimleri sil
  const deleteAllNotifications = useCallback(async () => {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        toast.error('Kimlik doğrulama hatası');
        return;
      }

      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('user_id', user.id);

      if (error) {
        console.error('Tüm bildirimler silme hatası:', error);
        toast.error('Bildirimler silinemedi');
        return;
      }

      // Local state'i temizle
      setNotifications([]);
      setUnreadCount(0);

      toast.success('Tüm bildirimler silindi');

    } catch (err) {
      console.error('Tüm bildirimler silme hatası:', err);
      toast.error('Beklenmeyen bir hata oluştu');
    }
  }, [supabase]);

  // Bildirimleri yenile
  const refreshNotifications = useCallback(async () => {
    await loadNotifications();
  }, [loadNotifications]);

  // Realtime subscription
  useEffect(() => {
    let notificationsChannel: any = null;

    const setupRealtimeSubscription = async () => {
      try {
        // Önceki subscription'ı temizle
        if (subscriptionRef.current) {
          subscriptionRef.current.unsubscribe();
          subscriptionRef.current = null;
        }

        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        // Bildirimler için subscription
        notificationsChannel = supabase
          .channel(`notifications-${user.id}-${Date.now()}`)
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'notifications',
              filter: `user_id=eq.${user.id}`,
            },
            async (payload: any) => {

              if (payload.eventType === 'INSERT') {
                const newNotification = payload.new as Notification;

                // Yeni bildirim ekle
                setNotifications(prev => [newNotification, ...prev]);
                setUnreadCount(prev => prev + 1);

                // Toast bildirimi göster
                toast.info(newNotification.title, {
                  description: newNotification.message,
                  duration: 5000,
                });

              } else if (payload.eventType === 'UPDATE') {
                const updatedNotification = payload.new as Notification;

                setNotifications(prev =>
                  prev.map(notification =>
                    notification.id === updatedNotification.id
                      ? updatedNotification
                      : notification
                  )
                );

              } else if (payload.eventType === 'DELETE') {
                const deletedNotification = payload.old as Notification;

                setNotifications(prev =>
                  prev.filter(notification => notification.id !== deletedNotification.id)
                );
              }
            }
          )
          .subscribe();

        subscriptionRef.current = notificationsChannel;

      } catch (err) {
        console.error('Bildirim realtime subscription hatası:', err);
      }
    };

    setupRealtimeSubscription();
    loadNotifications();

    // Cleanup
    return () => {
      if (notificationsChannel) {
        notificationsChannel.unsubscribe();
      }
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
      }
    };
  }, [supabase, loadNotifications]);

  return {
    notifications,
    unreadCount,
    isLoading,
    error,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    deleteAllNotifications,
    refreshNotifications,
  };
}
