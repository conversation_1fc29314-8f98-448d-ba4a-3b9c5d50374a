import { Badge } from '@/components/ui/badge';
import { Inbox } from 'lucide-react';
import { InvitationCard } from './invitation-card';
import { isInvitationExpired } from './types';
import type { InvitationListProps } from './types';

export function InvitationList({
  invitations,
  onAction,
  emptyMessage = 'Hen<PERSON>z davet bulunmuyor',
  emptyDescription = 'Davetler burada görünecektir.',
  showActions = true,
  groupByStatus = true,
}: InvitationListProps) {
  
  if (invitations.length === 0) {
    return (
      <div className="py-12 text-center">
        <Inbox className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
        <h3 className="mb-2 text-lg font-medium text-foreground">
          {emptyMessage}
        </h3>
        <p className="text-muted-foreground">
          {emptyDescription}
        </p>
      </div>
    );
  }

  if (!groupByStatus) {
    return (
      <div className="space-y-4">
        {invitations.map(invitation => (
          <InvitationCard
            key={invitation.id}
            invitation={invitation}
            onAction={onAction}
            showActions={showActions}
          />
        ))}
      </div>
    );
  }

  // Group invitations by status
  const pendingInvitations = invitations.filter(inv => {
    const expired = isInvitationExpired(inv);
    return inv.status === 'pending' && !expired;
  });

  const expiredInvitations = invitations.filter(inv => {
    const expired = isInvitationExpired(inv);
    return inv.status === 'pending' && expired;
  });

  const respondedInvitations = invitations.filter(inv => {
    const expired = isInvitationExpired(inv);
    return inv.status !== 'pending' && !expired;
  });

  return (
    <div className="space-y-8">
      {/* Pending Invitations */}
      {pendingInvitations.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Bekleyen Davetler</h3>
            <Badge className="bg-warning text-warning-foreground">
              {pendingInvitations.length} Davet
            </Badge>
          </div>
          <div className="space-y-4">
            {pendingInvitations.map(invitation => (
              <InvitationCard
                key={invitation.id}
                invitation={invitation}
                onAction={onAction}
                showActions={showActions}
              />
            ))}
          </div>
        </div>
      )}

      {/* Expired Invitations */}
      {expiredInvitations.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Süresi Dolmuş</h3>
            <Badge variant="destructive">
              {expiredInvitations.length} Davet
            </Badge>
          </div>
          <div className="space-y-4">
            {expiredInvitations.map(invitation => (
              <InvitationCard
                key={invitation.id}
                invitation={invitation}
                onAction={onAction}
                showActions={false}
              />
            ))}
          </div>
        </div>
      )}

      {/* Responded Invitations */}
      {respondedInvitations.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Yanıtlanan Davetler</h3>
            <Badge variant="secondary">
              {respondedInvitations.length} Davet
            </Badge>
          </div>
          <div className="space-y-4">
            {respondedInvitations.map(invitation => (
              <InvitationCard
                key={invitation.id}
                invitation={invitation}
                onAction={onAction}
                showActions={false}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
