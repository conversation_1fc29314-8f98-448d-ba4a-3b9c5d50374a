import {
  Home,
  Building,
  Mail,
  LineChart,
  Users,
  Dumbbell,
  Package,
  UserCheck,
  BarChart3,
  Calendar,
  Settings,
  Plus,
  Wrench,
  PackageOpen,
  ChartNoAxesColumn,
} from 'lucide-react';

// Navigation types
export interface NavigationItem {
  href: string;
  title: string;
  icon: React.ReactNode;
  isActive?: boolean;
}

// Member Navigation Items
export const memberNavigation: NavigationItem[] = [
  {
    href: '/dashboard/member',
    title: '<PERSON><PERSON>',
    icon: <Home  />,
  },
  {
    href: '/dashboard/member/memberships',
    title: 'Üyeliklerim',
    icon: <Building  />,
  },
  {
    href: '/dashboard/member/invitations',
    title: 'Salon Davetleri',
    icon: <Mail  />,
  },
  {
    href: '/dashboard/member/progress',
    title: '<PERSON><PERSON><PERSON><PERSON>',
    icon: <LineChart  />,
  },
];

// Manager Navigation Items (Yönetici paneli)
export const managerNavigation: NavigationItem[] = [
  {
    href: '/dashboard/manager',
    title: 'Şirket Paneli',
    icon: <Home  />,
  },
  {
    href: '/dashboard/manager/gym-setup',
    title: '<PERSON><PERSON>',
    icon: <Plus  />,
  },
  {
    href: '/dashboard/manager/managers',
    title: 'Salon Yöneticileri',
    icon: <Mail  />,
  },
  {
    href: '/dashboard/manager/settings',
    title: 'Şirket Ayarları',
    icon: <Settings  />,
  },
  {
    href: '/dashboard/manager/billing',
    title:'Paket Bilgilerim',
    icon:<ChartNoAxesColumn/>
  },
];

// Unified Gym-specific Navigation Items (for all authorized roles)
export const getGymNavigation = (
  gymId: string,
  role: SidebarMode
): NavigationItem[] => {
  const allItems: NavigationItem[] = [
    {
      href: `/dashboard/gym/${gymId}`,
      title: 'Genel Bakış',
      icon: <Home  />,
    },
    {
      href: `/dashboard/gym/${gymId}/members`,
      title: 'Üyeler',
      icon: <Users  />,
    },
    {
      href: `/dashboard/gym/${gymId}/appointments`,
      title: 'Randevular',
      icon: <Calendar  />,
    },
    {
      href: `/dashboard/gym/${gymId}/trainers`,
      title: 'Antrenörler',
      icon: <Dumbbell  />,
    },
    {
      href: `/dashboard/gym/${gymId}/packages`,
      title: 'Paketler',
      icon: <Package  />,
    },
    {
      href: `/dashboard/gym/${gymId}/invitations`,
      title: 'Davetler',
      icon: <Mail  />,
    },
    {
      href: `/dashboard/gym/${gymId}/staff`,
      title: 'Personel',
      icon: <UserCheck  />,
    },
    {
      href: `/dashboard/gym/${gymId}/equipment`,
      title: 'Ekipmanlar',
      icon: <Wrench  />,
    },
    {
      href: `/dashboard/gym/${gymId}/inventory`,
      title: 'Depo',
      icon: <PackageOpen  />,
    },
    {
      href: `/dashboard/gym/${gymId}/analytics`,
      title: 'Analitik',
      icon: <BarChart3  />,
    },
    {
      href: `/dashboard/gym/${gymId}/settings`,
      title: 'Şube Ayarları',
      icon: <Settings  />,
    },
  ];

  // Filter navigation items based on the user's role within the gym
  if (role === 'trainer') {
    const restrictedTitles = [
      'Antrenörler',
      'Personel',
      'Analitik',
      'Şube Ayarları',
    ];
    return allItems.filter(item => !restrictedTitles.includes(item.title));
  }

  return allItems;
};

// Gym Manager Navigation Items (Salon yöneticisi paneli)
export const gymManagerNavigation: NavigationItem[] = [
  {
    href: '/dashboard/gym-manager',
    title: 'Salon Yöneticisi',
    icon: <Home  />,
  },
];

// Sidebar Navigation Helper Function
export type SidebarMode =
  | 'member'
  | 'manager'
  | 'gym-manager'
  | 'trainer';

export const getSidebarNavigation = (
  mode: SidebarMode,
  gymId?: string
): NavigationItem[] => {
  switch (mode) {
    case 'member':
      return memberNavigation;
    case 'manager':
      return gymId ? getGymNavigation(gymId, mode) : managerNavigation;
    case 'gym-manager':
      return gymManagerNavigation;
    case 'trainer':
      if (gymId) {
        return getGymNavigation(gymId, mode);
      }
      // Trainer varsayılan navigasyon (kendi paneli ve davetleri)
      return [
        {
          href: '/dashboard/trainer',
          title: 'Salonlarım',
          icon: <Building  />,
        },
        {
          href: '/dashboard/trainer/invitations',
          title: 'Davetler',
          icon: <Mail  />,
        },
      ];
    default:
      return [];
  }
};
