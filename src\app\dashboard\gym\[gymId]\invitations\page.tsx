import { Suspense } from 'react';
import { transformIncomingInvitation, transformOutgoingInvitation } from '@/components/invitations';
import {
  getGymIncomingRequests,
  getGymOutgoingInvites,
} from '@/lib/actions/gym_invitations/invitation-actions';
import { GymIncomingInvitation, GymOutgoingInvitation } from '@/lib/actions/gym_invitations/invitation-types';
import { GymInvitationsActions } from './components/gym-invitations-actions';

interface InvitationsPageProps {
  params: Promise<{ gymId: string }>;
}

// Server component to fetch gym invitations data
async function GymInvitationsData({ gymId }: { gymId: string }) {
  try {
    const [incomingResult, outgoingResult] = await Promise.all([
      getGymIncomingRequests(gymId),
      getGymOutgoingInvites(gymId),
    ]);

    const incomingInvitations = (incomingResult.success ? incomingResult.data : []) as GymIncomingInvitation[];
    const outgoingInvitations = (outgoingResult.success ? outgoingResult.data : []) as GymOutgoingInvitation[];

    // Transform to unified format
    // Note: GymIncomingInvitation and GymOutgoingInvitation don't have gym property
    // We need to create a mock gym object or get it from context
    const gymInfo = {
      id: gymId,
      name: 'Current Gym', // This should come from gym context
      address: null,
      gym_phone: null,
    };

    const unifiedIncoming = incomingInvitations.map(inv => transformIncomingInvitation({
      ...inv,
      gym: gymInfo,
    } as any));

    const unifiedOutgoing = outgoingInvitations.map(inv => transformOutgoingInvitation({
      ...inv,
      gym: gymInfo,
    } as any));

    return (
      <GymInvitationsActions
        incomingInvitations={unifiedIncoming}
        outgoingInvitations={unifiedOutgoing}
      />
    );
  } catch (error) {
    console.error('Error fetching gym invitations:', error);
    return (
      <GymInvitationsActions
        incomingInvitations={[]}
        outgoingInvitations={[]}
      />
    );
  }
}

export default async function InvitationsPage({
  params,
}: InvitationsPageProps) {
  const { gymId } = await params;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-6 border-b pb-6">
        <h1 className="text-3xl font-bold tracking-tight">Salon Davetleri</h1>
        <p className="mt-1 text-muted-foreground">
          Salonunuza gelen talepleri ve gönderdiğiniz davetleri yönetin.
        </p>
      </div>

      {/* Invitations with Streaming */}
      <Suspense
        fallback={
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 animate-pulse rounded-lg bg-muted" />
            ))}
          </div>
        }
      >
        <GymInvitationsData gymId={gymId} />
      </Suspense>
    </div>
  );
}

