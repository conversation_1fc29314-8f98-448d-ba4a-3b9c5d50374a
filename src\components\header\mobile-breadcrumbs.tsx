'use client';

import Link from 'next/link';
import { usePathname, useParams } from 'next/navigation';
import { useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Crown,
  Dumbbell,
  User,
  Building2,
  Plus,
  ChevronDown,
} from 'lucide-react';

import { useDashboard } from '@/context/dashboard-context';
import { GymData } from './shared/header-types';

// Role icon mapping
const roleIconMap = {
  member: User,
  trainer: <PERSON><PERSON><PERSON>,
  manager: Crown,
};

const getRoleIcon = (role: string) => {
  return roleIconMap[role as keyof typeof roleIconMap] || User;
};

export function MobileBreadcrumbs() {
  const {
    roleOptions,
    managedGyms,
    trainerGyms,
    isManager,
    isTrainer,
    isMember,
  } = useDashboard();
  const allManagedGyms = [...(trainerGyms || []), ...(managedGyms || [])];
  const pathname = usePathname();
  const params = useParams();
  const currentGymId = params?.gymId as string;

  const currentRole = useMemo(() => {
    if (pathname.includes('/dashboard/manager')) return 'manager';
    if (pathname.includes('/dashboard/gym-manager')) return 'manager';
    if (pathname.includes('/dashboard/trainer')) return 'trainer';
    if (pathname.includes('/dashboard/gym')) {
      if (isManager) return 'manager';
      if (isTrainer) return 'trainer';
    }
    if (pathname.includes('/dashboard/member')) return 'member';
    // Fallback based on available roles
    if (isManager) return 'manager';
    if (isTrainer) return 'trainer';
    if (isMember) return 'member';
    return roleOptions[0]?.role || '';
  }, [
    pathname,
    isManager,
    isTrainer,
    isMember,
    roleOptions,
  ]);
  const currentRoleOption = roleOptions.find(
    option => option.role === currentRole
  );
  const currentGym =
    currentRole === 'manager'
      ? managedGyms.find((gym: GymData) => gym.id === currentGymId)
      : allManagedGyms.find((gym: GymData) => gym.id === currentGymId);

  const renderRoleSelector = () => {
    // Rol seçiciyi her zaman göster - tek rol olsa bile kullanıcı hangi rolde olduğunu görebilir
    if (
      roleOptions.length === 0 ||
      !(isMember || isTrainer || isManager)
    ) {
      return null;
    }

    return (
      <div className="space-y-2">
        <h4 className="text-muted-foreground text-sm font-medium">Aktif Rol</h4>
        {roleOptions.length > 1 ? (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-between"
                size="sm"
              >
                <div className="flex items-center space-x-2">
                  {currentRoleOption && (
                    <>
                      {(() => {
                        const IconComponent = getRoleIcon(
                          currentRoleOption.role
                        );
                        return <IconComponent className="h-4 w-4" />;
                      })()}
                      <span>{currentRoleOption.label}</span>
                    </>
                  )}
                </div>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-56">
              <DropdownMenuLabel className="text-muted-foreground text-xs font-medium">
                Rol Değiştir
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              {roleOptions.map(option => (
                <DropdownMenuItem key={option.role} asChild>
                  <Link
                    href={option.href}
                    className={`flex w-full cursor-pointer items-center justify-between px-2 py-2 ${
                      option.role === currentRole ? 'bg-accent' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      {(() => {
                        const IconComponent = getRoleIcon(option.role);
                        return <IconComponent className="h-4 w-4" />;
                      })()}
                      <span>{option.label}</span>
                    </div>
                    {option.role === currentRole && (
                      <Badge variant="secondary" className="text-xs">
                        Aktif
                      </Badge>
                    )}
                  </Link>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          // Tek rol varsa basit buton göster
          <Button
            variant="outline"
            className="w-full justify-start"
            size="sm"
            asChild
          >
            <Link href={currentRoleOption?.href || '/dashboard'}>
              <div className="flex items-center space-x-2">
                {currentRoleOption && (
                  <>
                    {(() => {
                      const IconComponent = getRoleIcon(currentRoleOption.role);
                      return <IconComponent className="h-4 w-4" />;
                    })()}
                    <span>{currentRoleOption.label}</span>
                  </>
                )}
              </div>
            </Link>
          </Button>
        )}
      </div>
    );
  };

  const renderGymSelector = () => {
    const showGymSelector =
      (isManager || isTrainer) &&
      allManagedGyms &&
      allManagedGyms.length > 0;
    if (showGymSelector) {
      return (
        <div className="space-y-2">
          <h4 className="text-muted-foreground text-sm font-medium">
            Aktif Salon
          </h4>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-between"
                size="sm"
              >
                <div className="flex items-center space-x-2">
                  <Building2 className="h-4 w-4" />
                  <span className="truncate">
                    {currentGym?.name || 'Salon Seç'}
                  </span>
                </div>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-56">
              <DropdownMenuLabel className="text-muted-foreground text-xs font-medium">
                Salon Seç
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              {allManagedGyms.map(gym => (
                <DropdownMenuItem key={gym.id} asChild>
                  <Link
                    href={`/dashboard/gym/${gym.id}`}
                    className={`flex w-full cursor-pointer items-center justify-between px-2 py-2 ${
                      gym.id === currentGymId ? 'bg-accent' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <Building2 className="h-4 w-4" />
                      <span className="truncate">{gym.name}</span>
                    </div>
                    {gym.id === currentGymId && (
                      <Badge variant="secondary" className="text-xs">
                        Aktif
                      </Badge>
                    )}
                  </Link>
                </DropdownMenuItem>
              ))}
              {currentRole === 'manager' && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link
                      href="/dashboard/manager/gym-setup"
                      className="flex w-full cursor-pointer items-center space-x-2 px-2 py-2"
                    >
                      <Plus className="h-4 w-4" />
                      <span>Yeni Salon Ekle</span>
                    </Link>
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="space-y-4">
      {renderRoleSelector()}
      {renderGymSelector()}
    </div>
  );
}
