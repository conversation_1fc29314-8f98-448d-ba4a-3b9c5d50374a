'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import {
  InventoryFilters,
  InventoryCategory,
  INVENTORY_UNIT_TYPES,
} from '@/types/database/equipment-inventory';

interface InventoryFiltersPanelProps {
  filters: InventoryFilters;
  categories: InventoryCategory[];
  onFiltersChange: (filters: InventoryFilters) => void;
}

export function InventoryFiltersPanel({
  filters,
  categories,
  onFiltersChange,
}: InventoryFiltersPanelProps) {
  const handleFilterChange = (
    key: keyof InventoryFilters,
    value: string | boolean | undefined
  ) => {
    onFiltersChange({
      ...filters,
      [key]: value === 'all' ? undefined : value || undefined,
    });
  };

  const clearFilters = () => {
    onFiltersChange({});
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Filtreler</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={clearFilters}
            disabled={
              !Object.values(filters).some(
                value => value !== undefined && value !== false
              )
            }
          >
            Temizle
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Category Filter */}
          <div className="space-y-2">
            <Label htmlFor="category">Kategori</Label>
            <Select
              value={filters.category_id || ''}
              onValueChange={value => handleFilterChange('category_id', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Tüm kategoriler" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm kategoriler</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <Label htmlFor="status">Durum</Label>
            <Select
              value={filters.status || ''}
              onValueChange={value => handleFilterChange('status', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Tüm durumlar" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm durumlar</SelectItem>
                <SelectItem value="active">Aktif</SelectItem>
                <SelectItem value="discontinued">Durdurulmuş</SelectItem>
                <SelectItem value="out_of_stock">Tükendi</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Unit Type Filter */}
          <div className="space-y-2">
            <Label htmlFor="unit_type">Birim Türü</Label>
            <Select
              value={filters.unit_type || ''}
              onValueChange={value => handleFilterChange('unit_type', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Tüm birimler" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm birimler</SelectItem>
                {INVENTORY_UNIT_TYPES.map(unit => (
                  <SelectItem key={unit.value} value={unit.value}>
                    {unit.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Special Filters */}
          <div className="space-y-3">
            <Label>Özel Filtreler</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="low_stock"
                  checked={filters.low_stock || false}
                  onCheckedChange={checked =>
                    handleFilterChange('low_stock', checked as boolean)
                  }
                />
                <Label htmlFor="low_stock" className="text-sm">
                  Düşük stok
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="expired"
                  checked={filters.expired || false}
                  onCheckedChange={checked =>
                    handleFilterChange('expired', checked as boolean)
                  }
                />
                <Label htmlFor="expired" className="text-sm">
                  Süresi geçmiş
                </Label>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
