'use client';

import { useState, useTransition, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Lock, CheckCircle, Eye, EyeOff, Shield, Key } from 'lucide-react';
import { changePassword } from '@/lib/actions/user/account-security';

export const PasswordChange = () => {
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  // Loading states
  const [isPending, startTransition] = useTransition();

  // Message states
  const [error, setError] = useState<string | undefined>();
  const [success, setSuccess] = useState<string | undefined>();

  // Password visibility states
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleSubmit = async (formData: FormData) => {
    setError(undefined);
    setSuccess(undefined);

    startTransition(async () => {
      try {
        const result = await changePassword(
          { success: false, error: undefined, data: undefined },
          formData
        );

        if (result.success) {
          setSuccess('Şifreniz başarıyla değiştirildi');
          setFormData({
            currentPassword: '',
            newPassword: '',
            confirmPassword: '',
          });
        } else {
          setError(result.error || 'Şifre değiştirilirken hata oluştu');
        }
      } catch (err) {
        setError('Beklenmeyen bir hata oluştu');
        console.error('Password change error:', err);
      }
    });
  };

  const handleInputChange = useCallback((field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  }, []);

  const togglePasswordVisibility = useCallback(
    (field: 'current' | 'new' | 'confirm') => {
      switch (field) {
        case 'current':
          setShowCurrentPassword(prev => !prev);
          break;
        case 'new':
          setShowNewPassword(prev => !prev);
          break;
        case 'confirm':
          setShowConfirmPassword(prev => !prev);
          break;
      }
    },
    []
  );

  return (
    <div className="space-y-6">
      {/* Status Messages */}
      {error && (
        <div className="bg-destructive/10 border-destructive/20 text-destructive flex items-center gap-2 rounded-lg border p-4 text-sm">
          <div className="bg-destructive h-2 w-2 rounded-full" />
          {error}
        </div>
      )}
      {success && (
        <div className="flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-4 text-sm text-green-700 dark:border-green-800 dark:bg-green-950 dark:text-green-400">
          <CheckCircle className="h-4 w-4" />
          {success}
        </div>
      )}

      <form action={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Şifre Değiştir
            </CardTitle>
            <CardDescription>
              Hesabınızın güvenliği için güçlü bir şifre seçin
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Güvenlik İpuçları */}
            <div className="bg-primary/5 border-primary/20 rounded-lg border p-4">
              <h4 className="mb-2 flex items-center gap-2 text-sm font-medium">
                <Key className="h-4 w-4" />
                Güçlü Şifre İpuçları
              </h4>
              <ul className="text-muted-foreground space-y-1 text-xs">
                <li>• En az 8 karakter uzunluğunda olsun</li>
                <li>• Büyük ve küçük harfler içersin</li>
                <li>• Rakam ve özel karakterler kullanın</li>
                <li>• Kişisel bilgilerinizi kullanmayın</li>
              </ul>
            </div>

            {/* Mevcut Şifre */}
            <div className="space-y-2">
              <Label htmlFor="currentPassword" className="text-sm font-medium">
                Mevcut Şifre *
              </Label>
              <div className="relative">
                <Input
                  id="currentPassword"
                  name="currentPassword"
                  type={showCurrentPassword ? 'text' : 'password'}
                  value={formData.currentPassword}
                  onChange={e =>
                    handleInputChange('currentPassword', e.target.value)
                  }
                  placeholder="Mevcut şifrenizi girin"
                  required
                  className="h-11 pr-10"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('current')}
                  className="text-muted-foreground hover:text-foreground absolute top-1/2 right-3 -translate-y-1/2 transition-colors"
                >
                  {showCurrentPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
            </div>

            {/* Yeni Şifre */}
            <div className="space-y-2">
              <Label htmlFor="newPassword" className="text-sm font-medium">
                Yeni Şifre *
              </Label>
              <div className="relative">
                <Input
                  id="newPassword"
                  name="newPassword"
                  type={showNewPassword ? 'text' : 'password'}
                  value={formData.newPassword}
                  onChange={e =>
                    handleInputChange('newPassword', e.target.value)
                  }
                  placeholder="Yeni şifrenizi girin"
                  required
                  minLength={6}
                  className="h-11 pr-10"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('new')}
                  className="text-muted-foreground hover:text-foreground absolute top-1/2 right-3 -translate-y-1/2 transition-colors"
                >
                  {showNewPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              <p className="text-muted-foreground text-xs">
                Şifreniz en az 6 karakter olmalıdır
              </p>
            </div>

            {/* Şifre Onayı */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-sm font-medium">
                Yeni Şifre (Tekrar) *
              </Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={formData.confirmPassword}
                  onChange={e =>
                    handleInputChange('confirmPassword', e.target.value)
                  }
                  placeholder="Yeni şifrenizi tekrar girin"
                  required
                  className="h-11 pr-10"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('confirm')}
                  className="text-muted-foreground hover:text-foreground absolute top-1/2 right-3 -translate-y-1/2 transition-colors"
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              <p className="text-muted-foreground text-xs">
                Şifreler eşleşmelidir
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-3 pt-4 sm:flex-row sm:justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setFormData({
                    currentPassword: '',
                    newPassword: '',
                    confirmPassword: '',
                  });
                  setError(undefined);
                  setSuccess(undefined);
                }}
                disabled={isPending}
                className="sm:w-auto"
              >
                Formu Temizle
              </Button>
              <Button
                type="submit"
                disabled={isPending}
                className="min-w-[160px] sm:w-auto"
              >
                {isPending ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    Değiştiriliyor...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Lock className="h-4 w-4" />
                    Şifreyi Değiştir
                  </div>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
};
