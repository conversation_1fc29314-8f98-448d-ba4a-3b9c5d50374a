import { IncomingInvitation, OutgoingInvitation } from '@/lib/actions/gym_invitations/invitation-types';

// Generic invitation interface for unified handling
export interface UnifiedInvitation {
  id: string;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  type: 'join_request' | 'gym_invite';
  role: 'member' | 'trainer';
  created_at: string;
  expires_at?: string;
  responded_at?: string;
  message?: string;
  
  // Gym information
  gym: {
    id: string;
    name: string;
    address?: string;
    phone?: string;
    email?: string;
  };
  
  // Profile information (for the other party)
  profile?: {
    id: string;
    full_name?: string;
    email?: string;
  };
  
  // Direction of invitation
  direction: 'incoming' | 'outgoing';
}

// Props for invitation components
export interface InvitationCardProps {
  invitation: UnifiedInvitation;
  onAction?: (invitationId: string, action: 'accept' | 'reject' | 'cancel') => Promise<void>;
  showActions?: boolean;
  compact?: boolean;
}

export interface InvitationListProps {
  invitations: UnifiedInvitation[];
  onAction?: (invitationId: string, action: 'accept' | 'reject' | 'cancel') => Promise<void>;
  emptyMessage?: string;
  emptyDescription?: string;
  showActions?: boolean;
  groupByStatus?: boolean;
}

export interface InvitationTabsProps {
  incomingInvitations: UnifiedInvitation[];
  outgoingInvitations: UnifiedInvitation[];
  onAction?: (invitationId: string, action: 'accept' | 'reject' | 'cancel') => Promise<void>;
  userType: 'member' | 'trainer' | 'gym';
}

// Utility functions for transforming data
export function transformIncomingInvitation(invitation: IncomingInvitation): UnifiedInvitation {
  return {
    id: invitation.id,
    status: invitation.status as any,
    type: 'gym_invite' as any, // IncomingInvitation is always gym_invite
    role: invitation.role as any,
    created_at: invitation.created_at,
    expires_at: invitation.expires_at || undefined,
    responded_at: invitation.responded_at || undefined,
    message: invitation.message || undefined,
    gym: {
      id: invitation.gym.id,
      name: invitation.gym.name,
      address: invitation.gym.address || undefined,
      phone: invitation.gym.gym_phone || undefined,
      email: undefined, // gym_phone is available, but not email
    },
    direction: 'incoming',
  };
}

export function transformOutgoingInvitation(invitation: OutgoingInvitation): UnifiedInvitation {
  return {
    id: invitation.id,
    status: invitation.status as any,
    type: 'join_request' as any, // OutgoingInvitation is typically join_request
    role: invitation.role as any,
    created_at: invitation.created_at,
    expires_at: invitation.expires_at || undefined,
    responded_at: invitation.responded_at || undefined,
    message: invitation.message || undefined,
    gym: {
      id: invitation.gym.id,
      name: invitation.gym.name,
      address: invitation.gym.address || undefined,
      phone: invitation.gym.gym_phone || undefined,
      email: undefined, // gym_phone is available, but not email
    },
    profile: undefined, // OutgoingInvitation doesn't have profile in the type definition
    direction: 'outgoing',
  };
}

// Helper functions
export function isInvitationExpired(invitation: UnifiedInvitation): boolean {
  return invitation.expires_at ? new Date(invitation.expires_at) < new Date() : false;
}

export function getInvitationTypeLabel(type: string, role: string): string {
  if (type === 'join_request') {
    return role === 'member' ? 'Üyelik Talebi' : 'Antrenörlük Talebi';
  }
  return role === 'member' ? 'Üye Daveti' : 'Antrenör Daveti';
}

export function getInvitationStatusColor(status: string, expired: boolean): string {
  if (expired) return 'destructive';
  
  switch (status) {
    case 'pending': return 'warning';
    case 'accepted': return 'success';
    case 'rejected': return 'destructive';
    default: return 'secondary';
  }
}
