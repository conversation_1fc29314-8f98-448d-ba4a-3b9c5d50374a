'use server';

import { getProfile } from '@/lib/actions/user/profile-actions';
import {
  HeaderAuthStatus,
  RoleOption,
  GymData,
} from '@/components/header/shared/header-types';
import {  createDynamicRoleOptions } from '@/components/header/shared/header-constants';
import type { GymAccessControlResult } from '@/lib/types/gym-access-control';
import { createAction } from '@/lib/actions/core/core';
import { cache } from 'react';

// Types for internal use
type UserRolesFlags = {
  is_member: boolean | null;
  is_trainer: boolean | null;
  is_manager: boolean | null;
  member_gymids: string[] | null;  // snake_case (veritabanından geliyor)
  trainer_gymids: string[] | null; // snake_case
  managed_gymids: string[] | null; // snake_case
};

// Helper functions
const createUnauthenticatedResponse = (
  gymId?: string
): HeaderAuthStatus & { gymAccess?: GymAccessControlResult } => ({
  authenticated: false,
  profile: null,
  roleOptions: [],
  managedGyms: [],
  trainerGyms: [],
  isManager: false,
  isTrainer: false,
  isMember: false,
  ...(gymId ? { gymAccess: { hasAccess: false, role: 'none', gymId } } : {}),
});

const mapGymSimple = (gym: {
  id: string;
  name: string;
  slug: string | null;
  city: string | null;
  district: string | null;
}): GymData => ({
  id: gym.id,
  name: gym.name,
  slug: gym.slug || '',
  city: gym.city || '',
  district: gym.district || '',
});

// ---- Small helpers to reduce duplication ----
const normalizeIdArray = (
  flags: any,
  key: 'trainer_gymids' | 'managed_gymids'
): string[] => {
  const value = flags?.[key];
  return Array.isArray(value) ? value.filter(Boolean) : [];
};

const selectGymsByIds = async (
  supabase: any,
  ids: string[]
): Promise<GymData[]> => {
  if (!ids || ids.length === 0) return [];
  const { data } = await supabase
    .from('gyms')
    .select('id,name,slug,city,district')
    .in('id', ids);
  return (data || []).map(mapGymSimple);
};



const createRoleOptions = async (roles: string[]): Promise<RoleOption[]> => {
  // Dinamik role options oluştur (manager için özel href)
  return await createDynamicRoleOptions(roles);
};

const getRestrictedPages = (role: GymAccessControlResult['role']): string[] => {
  switch (role) {
    case 'trainer':
      // Antrenörlerin antrenör yönetimi sayfasına erişimi kapalı olmalı
      return ['analytics', 'trainers', 'staff', 'settings'];
    case 'manager':
      return [];
    default:
      // Genel kısıtlar: route segmentleri ile uyumlu tut
      return ['analytics', 'trainers', 'staff', 'settings'];
  }
};

const determineGymAccess = (
  gymId: string,
  managedGyms: GymData[],
  trainerGyms: GymData[]
): GymAccessControlResult => {
  const isManager = managedGyms.some(gym => gym.id === gymId);
  const isTrainer = trainerGyms.some(gym => gym.id === gymId);

  let role: GymAccessControlResult['role'] = 'none';
  if (isManager) {
    role = 'manager';
  } else if (isTrainer) {
    role = 'trainer';
  }

  return {
    hasAccess: role !== 'none',
    role,
    gymId,
    restrictedPages: getRestrictedPages(role),
  };
};

export const getDashboardContext = cache(
  async (
    gymId?: string
  ): Promise<HeaderAuthStatus & { gymAccess?: GymAccessControlResult }> => {
    try {
      const profileResult = await getProfile();

      // 1) user_roles üzerinden tüm rol ve ilişki verilerini yükle
      const rolesAndGyms = await createAction(async (_, supabase, userId) => {
        // Flags ve ID dizilerini çek
        const { data: flags } = await (supabase as any)
          .from('user_roles')
          .select('*')
          .eq('profile_id', userId)
          .maybeSingle();

        const safeFlags: UserRolesFlags = flags || {
          is_member: false,
          is_trainer: false,
          is_manager: false,
          member_gymids: [],
          trainer_gymids: [],
          managed_gymids: [],
        };

        const roles: string[] = [
          safeFlags.is_member && 'member',
          safeFlags.is_trainer && 'trainer',
          safeFlags.is_manager && 'manager',
        ].filter(Boolean) as string[];

        const trainerIds = normalizeIdArray(flags, 'trainer_gymids');
        const managerIds = normalizeIdArray(flags, 'managed_gymids');

        const [managedGyms, trainerGyms] = await Promise.all([
          safeFlags.is_manager
            ? selectGymsByIds(supabase, managerIds)
            : Promise.resolve([]),
          safeFlags.is_trainer
            ? selectGymsByIds(supabase, trainerIds)
            : Promise.resolve([]),
        ]);

        return {
          flags: safeFlags,
          roles,
          managedGyms,
          trainerGyms,
        };
      });

      const roles = rolesAndGyms.success ? rolesAndGyms.data!.roles : [];
      const managedGyms = rolesAndGyms.success
        ? rolesAndGyms.data!.managedGyms
        : [];
      const trainerGyms = rolesAndGyms.success
        ? rolesAndGyms.data!.trainerGyms
        : [];

      const isManager = roles.includes('manager');
      const isTrainer = roles.includes('trainer');

      const roleOptions = await createRoleOptions(roles);

      // Gym access kontrolü
      const gymAccess = gymId
        ? determineGymAccess(gymId, managedGyms, trainerGyms)
        : undefined;

      return {
        authenticated: true,
        profile:
          profileResult.success && profileResult.data
            ? profileResult.data
            : null,
        roleOptions,
        managedGyms,
        trainerGyms,
        isManager,
        isTrainer,
        isMember: roles.includes('member'),
        ...(gymId ? { gymAccess } : {}),
      };
    } catch (error) {
      console.error('Header auth status check failed:', error);
      return createUnauthenticatedResponse(gymId);
    }
  }
);
