'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UserPlus, Info, Copy, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';
import { createGymManagerInvitation } from '@/lib/actions/dashboard/company/gym-manager-invitation-actions';

interface CreateInvitationDialogProps {
  gym: { id: string; name: string };
  companyId: string;
  onSuccess: () => void;
}

export function CreateInvitationDialog({
  gym,
  companyId,
  onSuccess,
}: CreateInvitationDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [createdInvitation, setCreatedInvitation] = useState<{
    inviteCode: string;
    gymName: string;
    expiresAt: string;
  } | null>(null);

  const handleCreateInvitation = async () => {
    setIsLoading(true);
    try {
      const result = await createGymManagerInvitation({
        companyId,
        gymId: gym.id,
      });

      if (result.success && result.data) {
        setCreatedInvitation({
          inviteCode: result.data.invite_code,
          gymName: gym.name,
          expiresAt: result.data.expires_at,
        });
        toast.success('Davet kodu başarıyla oluşturuldu!');
        onSuccess();
      } else {
        toast.error(result.error || 'Davet kodu oluşturulamadı');
      }
    } catch (error) {
      console.error('Create invitation error:', error);
      toast.error('Beklenmeyen bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  const copyInviteCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast.success('Davet kodu kopyalandı!');
  };

  const handleClose = () => {
    setIsOpen(false);
    setCreatedInvitation(null);
  };

  const formatExpiryDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('tr-TR');
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button>
          <UserPlus className="mr-2 h-4 w-4" />
          Davet Kodu Oluştur
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Salon Yöneticisi Davet Kodu
          </DialogTitle>
          <DialogDescription>
            {gym.name} salonu için yönetici davet kodu oluşturun
          </DialogDescription>
        </DialogHeader>

        {!createdInvitation ? (
          <div className="space-y-4">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Davet kodu 24 saat geçerlidir ve tek kullanımlıktır. Kod
                kullanıldığında size bildirim gönderilecektir.
              </AlertDescription>
            </Alert>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={handleClose}>
                İptal
              </Button>
              <Button onClick={handleCreateInvitation} disabled={isLoading}>
                {isLoading ? 'Oluşturuluyor...' : 'Davet Kodu Oluştur'}
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
              <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
              <AlertDescription className="text-green-800 dark:text-green-200">
                Davet kodu başarıyla oluşturuldu!
              </AlertDescription>
            </Alert>

            <div className="space-y-3">
              <div>
                <Label className="text-sm font-medium">Salon</Label>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {createdInvitation.gymName}
                </p>
              </div>

              <div>
                <Label className="text-sm font-medium">Davet Kodu</Label>
                <div className="mt-1 flex items-center space-x-2">
                  <code className="flex-1 rounded bg-gray-100 px-3 py-2 text-center font-mono text-lg dark:bg-gray-800">
                    {createdInvitation.inviteCode}
                  </code>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyInviteCode(createdInvitation.inviteCode)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Geçerlilik Süresi</Label>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {formatExpiryDate(createdInvitation.expiresAt)}
                </p>
              </div>
            </div>

            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Bu kodu ilgili kişiye gönderin. Kod kullanıldığında size e-posta
                ile bildirim gönderilecektir.
              </AlertDescription>
            </Alert>

            <div className="flex justify-end">
              <Button onClick={handleClose}>Tamam</Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
