'use client';

import { useTheme } from 'next-themes';
import { useCallback, useEffect, useState } from 'react';
import { Sun, Moon, Monitor } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface ThemeToggleProps {
  className?: string;
  variant?: 'default' | 'auth' | 'footer' | 'dropdown';
}

type ThemeOption = {
  id: 'light' | 'dark' | 'system';
  label: string;
  icon: React.ReactNode;
};

export function ThemeToggle({
  className,
  variant = 'default',
}: ThemeToggleProps) {
  const { theme, setTheme, systemTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Hidrasyon hatalarını önlemek için client-side mount kontrolü
  useEffect(() => {
    setMounted(true);
  }, []);

  const getActiveThemeIcon = useCallback(() => {
    const currentTheme = theme === 'system' ? systemTheme : theme;
    return currentTheme === 'dark' ? (
      <Moon className="h-5 w-5" />
    ) : (
      <Sun className="h-5 w-5" />
    );
  }, [theme, systemTheme]);

  if (!mounted) {
    if (variant === 'auth') {
      return (
        <div className="absolute top-4 right-4 h-10 w-10 lg:top-6 lg:right-6" />
      );
    }
    return null;
  }

  const themeOptions: ThemeOption[] = [
    {
      id: 'light',
      label: 'Açık Tema',
      icon: <Sun className="mr-2 h-4 w-4" />,
    },
    {
      id: 'dark',
      label: 'Koyu Tema',
      icon: <Moon className="mr-2 h-4 w-4" />,
    },
    {
      id: 'system',
      label: 'Sistem Varsayılanı',
      icon: <Monitor className="mr-2 h-4 w-4" />,
    },
  ];

  const baseClasses = 'transition-all duration-300';

  const variantClasses = {
    default: '',
    auth: 'absolute right-4 top-4 border border-white/20 bg-white/10 text-white backdrop-blur-sm hover:scale-110 hover:bg-white/20 hover:text-white lg:right-6 lg:top-6',
    footer: '',
    dropdown: 'w-full justify-start hover:bg-accent/50',
  };

  if (variant === 'footer') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className={cn(baseClasses, variantClasses[variant], className)}
            aria-label="Tema seçeneklerini göster"
          >
            {getActiveThemeIcon()}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {themeOptions.map(option => (
            <DropdownMenuItem
              key={option.id}
              onClick={() => setTheme(option.id)}
              className="cursor-pointer"
            >
              {option.icon}
              <span>{option.label}</span>
              {theme === option.id && (
                <span className="text-muted-foreground ml-auto text-xs">✓</span>
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  if (variant === 'dropdown') {
    return (
      <div className="w-full">
        {themeOptions.map(option => (
          <Button
            key={option.id}
            variant="ghost"
            onClick={() => setTheme(option.id)}
            className={cn(
              'w-full justify-start',
              theme === option.id ? 'bg-accent' : ''
            )}
          >
            {option.icon}
            <span>{option.label}</span>
          </Button>
        ))}
      </div>
    );
  }

  // Varsayılan davranış
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={cn(baseClasses, variantClasses[variant], className)}
          aria-label="Tema seçeneklerini göster"
        >
          {getActiveThemeIcon()}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {themeOptions.map(option => (
          <DropdownMenuItem
            key={option.id}
            onClick={() => setTheme(option.id)}
            className="cursor-pointer"
          >
            {option.icon}
            <span>{option.label}</span>
            {theme === option.id && (
              <span className="text-muted-foreground ml-auto text-xs">✓</span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
