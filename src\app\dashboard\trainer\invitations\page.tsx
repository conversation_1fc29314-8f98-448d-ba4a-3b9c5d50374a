import { Suspense } from 'react';
import { transformIncomingInvitation, transformOutgoingInvitation } from '@/components/invitations';
import {
  IncomingInvitation,
  OutgoingInvitation,
} from '@/lib/actions/gym_invitations/invitation-types';
import {
  getUserIncomingInvites,
  getUserOutgoingRequests,
} from '@/lib/actions/gym_invitations/invitation-actions';
import { TrainerInvitationsActions } from './components/trainer-invitations-actions';

// Force dynamic rendering

// Invitations Data Component
async function InvitationsData() {
  try {
    // Server action ile trainer davet<PERSON><PERSON> al
    const incomingResult = await getUserIncomingInvites('trainer');
    const outgoingResult = await getUserOutgoingRequests('trainer');

    const incomingInvitations = (incomingResult.success ? incomingResult.data : []) as IncomingInvitation[];
    const outgoingInvitations = (outgoingResult.success ? outgoingResult.data : []) as OutgoingInvitation[];

    // Transform to unified format
    const unifiedIncoming = incomingInvitations.map(transformIncomingInvitation);
    const unifiedOutgoing = outgoingInvitations.map(transformOutgoingInvitation);

    return (
      <TrainerInvitationsActions
        incomingInvitations={unifiedIncoming}
        outgoingInvitations={unifiedOutgoing}
      />
    );
  } catch (error) {
    console.error('Invitations fetch error:', error);
    return (
      <TrainerInvitationsActions
        incomingInvitations={[]}
        outgoingInvitations={[]}
      />
    );
  }
}
// Main Page Component
export default async function TrainerInvitationsPage() {
  return (
    <div className="space-y-6">
      <div className="mb-6 border-b pb-6">
        <h1 className="text-3xl font-bold tracking-tight">Salon Davetleri</h1>
        <p className="text-muted-foreground mt-1">
          Size gönderilen salon davetlerini görüntüleyin ve yanıtlayın
        </p>
      </div>

      {/* Invitations List */}
      <Suspense
        fallback={
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-muted h-32 animate-pulse rounded-lg" />
            ))}
          </div>
        }
      >
        <InvitationsData />
      </Suspense>
    </div>
  );
}
