'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { toast } from 'sonner';
import {
  User,
  Shield,
  Calendar,
  Users,
  Package,
  Crown,
  UserCheck,
  Settings,
  CheckCircle,
  XCircle,
  Info,
  Star,
} from 'lucide-react';

import {
  TrainerPermissionLevel,
  getAllPermissionLevels,
  getPermissionLevel,
  getPermissionLevelDefinition,
} from '@/lib/constants/trainer-permission-levels';
import { TrainerPermissions } from '@/lib/auth/server-auth';
import {
  GymTrainerWithPermissions,
  updateTrainerPermissionLevel,
  updateTrainerPermissions,
} from '@/lib/actions/dashboard/company/trainer-permissions';

interface TrainerPermissionsClientProps {
  initialTrainers: GymTrainerWithPermissions[];
  gymId: string;
}

interface PermissionUpdate {
  trainerId: string;
  permissions: TrainerPermissions;
}

export function TrainerPermissionsClient({
  initialTrainers,
  gymId,
}: TrainerPermissionsClientProps) {
  const [trainers, setTrainers] = useState(initialTrainers);
  const [pendingUpdates, setPendingUpdates] = useState<
    Map<string, PermissionUpdate>
  >(new Map());
  const [isUpdating, setIsUpdating] = useState(false);
  const [viewMode, setViewMode] = useState<'simple' | 'advanced'>('simple');

  const permissionLevels = getAllPermissionLevels();

  const updatePermission = (
    trainerId: string,
    category: keyof TrainerPermissions,
    action: string,
    value: boolean
  ) => {
    const trainer = trainers.find(t => t.id === trainerId);
    if (!trainer) return;

    const newPermissions = { ...trainer.permissions };
    if (category in newPermissions) {
      const categoryPerms = newPermissions[category] as any;
      if (action in categoryPerms) {
        categoryPerms[action] = value;
      }
    }

    // Update local state
    setTrainers(prev =>
      prev.map(t =>
        t.id === trainerId ? { ...t, permissions: newPermissions } : t
      )
    );

    // Track pending update
    setPendingUpdates(prev => {
      const updated = new Map(prev);
      updated.set(trainerId, {
        trainerId,
        permissions: newPermissions,
      });
      return updated;
    });
  };

  const savePermissions = async (trainerId: string) => {
    const update = pendingUpdates.get(trainerId);
    if (!update) return;

    setIsUpdating(true);
    try {
      const result = await updateTrainerPermissions({
        gymId,
        trainerId,
        permissions: update.permissions,
      });

      if (result.success) {
        toast.success('Yetki ayarları güncellendi');
        setPendingUpdates(prev => {
          const updated = new Map(prev);
          updated.delete(trainerId);
          return updated;
        });
      } else {
        toast.error(result.error || 'Güncelleme başarısız');
      }
    } catch (error) {
      toast.error('Bir hata oluştu');
      console.error('Permission update error:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const updatePermissionLevel = async (
    trainerId: string,
    level: TrainerPermissionLevel
  ) => {
    setIsUpdating(true);
    try {
      const result = await updateTrainerPermissionLevel({
        gymId,
        trainerId,
        level,
      });

      if (result.success) {
        const levelDefinition = getPermissionLevelDefinition(level);

        // Update local state
        setTrainers(prev =>
          prev.map(t =>
            t.id === trainerId
              ? {
                  ...t,
                  permissions: levelDefinition.permissions,
                  permission_level: level,
                }
              : t
          )
        );

        toast.success(
          `Yetki seviyesi "${levelDefinition.name}" olarak güncellendi`
        );
      } else {
        toast.error(result.error || 'Seviye güncellemesi başarısız');
      }
    } catch (error) {
      toast.error('Bir hata oluştu');
      console.error('Permission level update error:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const getPermissionIcon = (category: keyof TrainerPermissions) => {
    switch (category) {
      case 'appointments':
        return <Calendar className="h-4 w-4" />;
      case 'members':
        return <Users className="h-4 w-4" />;
      case 'packages':
        return <Package className="h-4 w-4" />;

      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  const getPermissionLabel = (category: keyof TrainerPermissions) => {
    switch (category) {
      case 'appointments':
        return 'Randevular';
      case 'members':
        return 'Üyeler';
      case 'packages':
        return 'Paketler';

      default:
        return category;
    }
  };

  const getActionLabel = (action: string) => {
    switch (action) {
      case 'read':
        return 'Görüntüleme';
      case 'create':
        return 'Oluşturma';
      case 'update':
        return 'Düzenleme';
      case 'delete':
        return 'Silme';
      default:
        return action;
    }
  };

  if (trainers.length === 0) {
    return (
      <div className="space-y-8">
        {/* Enhanced Empty State */}
        <Card className="border-2 border-dashed border-gray-300 dark:border-gray-600">
          <CardContent className="flex min-h-[400px] items-center justify-center">
            <div className="max-w-md space-y-6 text-center">
              <div className="relative">
                <div className="mx-auto flex h-24 w-24 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800">
                  <Users className="h-12 w-12 text-gray-400 dark:text-gray-500" />
                </div>
                <div className="absolute -right-2 -bottom-2 flex h-8 w-8 items-center justify-center rounded-full bg-blue-500">
                  <Shield className="h-4 w-4 text-white" />
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Henüz Antrenör Bulunmuyor
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Bu salonda henüz aktif antrenör bulunmuyor. Antrenör
                  ekledikten sonra yetki ayarlarını buradan yönetebilirsiniz.
                </p>
              </div>

              <div className="rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-950/20">
                <h4 className="mb-2 font-medium text-blue-900 dark:text-blue-100">
                  Antrenör Ekleme Adımları:
                </h4>
                <ol className="space-y-1 text-left text-sm text-blue-800 dark:text-blue-200">
                  <li>1. &quot;Antrenör Listesi&quot; sekmesine gidin</li>
                  <li>2. &quot;Antrenör Davet Et&quot; butonuna tıklayın</li>
                  <li>3. Antrenör kodunu girin ve davet gönderin</li>
                  <li>4. Davet kabul edildikten sonra yetkileri ayarlayın</li>
                </ol>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getLevelIcon = (level: TrainerPermissionLevel) => {
    switch (level) {
      case 'basic':
        return <User className="h-4 w-4" />;
      case 'standard':
        return <UserCheck className="h-4 w-4" />;
      case 'advanced':
        return <Crown className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  const getLevelBadgeVariant = (level: TrainerPermissionLevel) => {
    switch (level) {
      case 'basic':
        return 'secondary' as const;
      case 'standard':
        return 'default' as const;
      case 'advanced':
        return 'destructive' as const;
      default:
        return 'outline' as const;
    }
  };

  return (
    <div className="space-y-8">
      {/* View Mode Toggle */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Antrenör Yetkileri</h2>
          <p className="text-muted-foreground">
            Antrenörlerin salon içindeki yetkilerini yönetin
          </p>
        </div>
        <Tabs
          value={viewMode}
          onValueChange={value => setViewMode(value as 'simple' | 'advanced')}
          className="rounded-lg border bg-white p-1 shadow-sm dark:bg-gray-800"
        >
          <TabsList className="grid w-full grid-cols-2 bg-transparent">
            <TabsTrigger
              value="simple"
              className="flex items-center gap-2 data-[state=active]:bg-blue-500 data-[state=active]:text-white"
            >
              <Shield className="h-4 w-4" />
              <span className="hidden sm:inline">Basit</span>
            </TabsTrigger>
            <TabsTrigger
              value="advanced"
              className="flex items-center gap-2 data-[state=active]:bg-blue-500 data-[state=active]:text-white"
            >
              <Settings className="h-4 w-4" />
              <span className="hidden sm:inline">Detaylı</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {trainers.map(trainer => {
        const hasPendingChanges = pendingUpdates.has(trainer.id);
        const currentLevel = getPermissionLevel(trainer.permissions);
        const levelDefinition = getPermissionLevelDefinition(currentLevel);

        return (
          <Card
            key={trainer.id}
            className="group border-l-4 border-l-blue-500 transition-all duration-200 hover:shadow-lg"
          >
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  {/* Enhanced Avatar */}
                  <div className="relative">
                    <Avatar className="h-14 w-14 ring-2 ring-blue-100 dark:ring-blue-900">
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-indigo-600 text-lg font-semibold text-white">
                        {trainer.trainer.full_name
                          .split(' ')
                          .map(n => n[0])
                          .join('')
                          .slice(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                    {/* Status Indicator */}
                    <div className="absolute -right-1 -bottom-1 flex h-5 w-5 items-center justify-center rounded-full border-2 border-white bg-green-500 dark:border-gray-800">
                      <CheckCircle className="h-3 w-3 text-white" />
                    </div>
                  </div>

                  {/* Trainer Info */}
                  <div className="min-w-0 flex-1">
                    <div className="mb-1 flex items-center gap-2">
                      <CardTitle className="truncate text-xl font-semibold text-gray-900 dark:text-white">
                        {trainer.trainer.full_name}
                      </CardTitle>
                      {currentLevel === 'advanced' && (
                        <Star className="h-4 w-4 fill-current text-yellow-500" />
                      )}
                    </div>

                    <p className="mb-3 truncate text-sm text-gray-600 dark:text-gray-300">
                      {trainer.trainer.email}
                    </p>

                    {/* Enhanced Badges */}
                    <div className="flex flex-wrap items-center gap-2">
                      {trainer.trainer.specialization && (
                        <Badge
                          variant="outline"
                          className="border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800"
                        >
                          <Package className="mr-1 h-3 w-3" />
                          {trainer.trainer.specialization}
                        </Badge>
                      )}

                      <Badge
                        variant={getLevelBadgeVariant(currentLevel)}
                        className="flex items-center gap-1.5 px-3 py-1 font-medium"
                      >
                        {getLevelIcon(currentLevel)}
                        {levelDefinition.name}
                      </Badge>

                      {trainer.trainer.experience_years && (
                        <Badge
                          variant="secondary"
                          className="bg-blue-50 text-blue-700 dark:bg-blue-950 dark:text-blue-300"
                        >
                          {trainer.trainer.experience_years} yıl deneyim
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                {/* Action Button */}
                <div className="flex flex-col items-end gap-2">
                  {hasPendingChanges && (
                    <Button
                      onClick={() => savePermissions(trainer.id)}
                      disabled={isUpdating}
                      size="sm"
                      className="bg-green-600 text-white shadow-sm hover:bg-green-700"
                    >
                      {isUpdating ? (
                        <div className="flex items-center gap-2">
                          <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                          Kaydediliyor...
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4" />
                          Kaydet
                        </div>
                      )}
                    </Button>
                  )}

                  {/* Quick Level Indicator */}
                  <div className="text-right text-xs text-gray-500 dark:text-gray-400">
                    {levelDefinition.features.length} yetki
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {viewMode === 'simple' ? (
                // Enhanced Simple View
                <div className="space-y-6">
                  {/* Level Selector with Visual Cards */}
                  <div>
                    <label className="mb-3 block text-sm font-semibold text-gray-700 dark:text-gray-300">
                      Yetki Seviyesi Seçin
                    </label>

                    <div className="grid grid-cols-1 gap-3 md:grid-cols-3">
                      {permissionLevels.map(level => (
                        <div
                          key={level.id}
                          onClick={() =>
                            !isUpdating &&
                            updatePermissionLevel(trainer.id, level.id)
                          }
                          className={`relative cursor-pointer rounded-xl border-2 p-4 transition-all duration-200 hover:shadow-md ${
                            currentLevel === level.id
                              ? 'border-blue-500 bg-blue-50 shadow-md dark:bg-blue-950/20'
                              : 'border-gray-200 hover:border-gray-300 dark:border-gray-700 dark:hover:border-gray-600'
                          } ${isUpdating ? 'cursor-not-allowed opacity-50' : ''} `}
                        >
                          {/* Selection Indicator */}
                          {currentLevel === level.id && (
                            <div className="absolute -top-2 -right-2 flex h-6 w-6 items-center justify-center rounded-full bg-blue-500">
                              <CheckCircle className="h-4 w-4 text-white" />
                            </div>
                          )}

                          {/* Level Icon & Name */}
                          <div className="mb-2 flex items-center gap-3">
                            <div
                              className={`rounded-lg p-2 ${level.color} text-white`}
                            >
                              {getLevelIcon(level.id)}
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {level.name}
                              </h3>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {level.features.length} yetki
                              </p>
                            </div>
                          </div>

                          {/* Description */}
                          <p className="mb-3 text-sm text-gray-600 dark:text-gray-300">
                            {level.description}
                          </p>

                          {/* Key Features */}
                          <div className="space-y-1">
                            {level.features
                              .slice(0, 2)
                              .map((feature, index) => (
                                <div
                                  key={index}
                                  className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400"
                                >
                                  <div className="h-1 w-1 rounded-full bg-current" />
                                  {feature}
                                </div>
                              ))}
                            {level.features.length > 2 && (
                              <div className="text-xs text-gray-400 dark:text-gray-500">
                                +{level.features.length - 2} daha fazla...
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Current Level Details */}
                  <div className="rounded-xl border border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 p-5 dark:border-gray-700 dark:from-gray-800 dark:to-gray-900">
                    <div className="mb-4 flex items-center gap-3">
                      <div
                        className={`rounded-lg p-2 ${levelDefinition.color} text-white`}
                      >
                        {getLevelIcon(currentLevel)}
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-white">
                          Mevcut Seviye: {levelDefinition.name}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          Bu seviyedeki tüm yetkiler
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                      {levelDefinition.features.map((feature, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-3 rounded-lg border border-gray-100 bg-white p-2 dark:border-gray-700 dark:bg-gray-800"
                        >
                          <div className="h-2 w-2 rounded-full bg-green-500" />
                          <span className="text-sm text-gray-700 dark:text-gray-300">
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                // Enhanced Advanced View
                <div className="space-y-6">
                  <div className="rounded-lg border border-amber-200 bg-amber-50 p-4 dark:border-amber-800 dark:bg-amber-950/20">
                    <div className="flex items-center gap-2 text-amber-800 dark:text-amber-200">
                      <Info className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        Detaylı Yetki Kontrolü
                      </span>
                    </div>
                    <p className="mt-1 text-sm text-amber-700 dark:text-amber-300">
                      Her kategori için ayrı ayrı yetki ayarları yapabilirsiniz.
                      Değişiklikler otomatik olarak kaydedilir.
                    </p>
                  </div>

                  {Object.entries(trainer.permissions).map(
                    ([category, permissions]) => (
                      <div
                        key={category}
                        className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800"
                      >
                        {/* Category Header */}
                        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4 dark:border-gray-700 dark:bg-gray-900">
                          <div className="flex items-center gap-3">
                            <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900">
                              {getPermissionIcon(
                                category as keyof TrainerPermissions
                              )}
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900 dark:text-white">
                                {getPermissionLabel(
                                  category as keyof TrainerPermissions
                                )}
                              </h4>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {
                                  Object.keys(
                                    permissions as Record<string, boolean>
                                  ).length
                                }{' '}
                                yetki seçeneği
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Permission Controls */}
                        <div className="p-6">
                          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                            {Object.entries(
                              permissions as Record<string, boolean>
                            ).map(([action, enabled]) => (
                              <div
                                key={action}
                                className={`relative rounded-lg border-2 p-4 transition-all duration-200 ${
                                  enabled
                                    ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20'
                                    : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'
                                } `}
                              >
                                {/* Status Indicator */}
                                <div className="absolute top-2 right-2">
                                  {enabled ? (
                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                  ) : (
                                    <XCircle className="h-4 w-4 text-gray-400" />
                                  )}
                                </div>

                                {/* Action Info */}
                                <div className="mb-3">
                                  <h5 className="font-medium text-gray-900 dark:text-white">
                                    {getActionLabel(action)}
                                  </h5>
                                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    {action === 'read' && 'Görüntüleme yetkisi'}
                                    {action === 'create' && 'Oluşturma yetkisi'}
                                    {action === 'update' && 'Düzenleme yetkisi'}
                                    {action === 'delete' && 'Silme yetkisi'}
                                  </p>
                                </div>

                                {/* Switch */}
                                <div className="flex items-center justify-between">
                                  <span
                                    className={`text-sm font-medium ${enabled ? 'text-green-700 dark:text-green-300' : 'text-gray-500 dark:text-gray-400'}`}
                                  >
                                    {enabled ? 'Aktif' : 'Pasif'}
                                  </span>
                                  <Switch
                                    checked={enabled}
                                    onCheckedChange={value =>
                                      updatePermission(
                                        trainer.id,
                                        category as keyof TrainerPermissions,
                                        action,
                                        value
                                      )
                                    }
                                    className="data-[state=checked]:bg-green-600"
                                  />
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
