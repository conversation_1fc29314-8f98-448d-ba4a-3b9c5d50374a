'use server';

import { createClient, createAnonClient } from '@/lib/supabase/server';
import { getSupabaseAdmin } from '@/lib/supabase/admin';
import { revalidatePath } from 'next/cache';
import { ApiResponse } from '@/types/global/api';

// Import utilities and constants
import {
  getUserId,
  performRevalidation,
  handleActionError,
  type ActionOptions,
} from './core-utils';

import { ACTION_DEFAULTS, CORE_ERROR_MESSAGES } from './core-constants';

/**
 * Main action creator function
 * Following Clean Code principles - orchestrates action execution with clear separation of concerns
 * Auth kontrolü otomatik olarak yapılır - requireAuth true ise userId garantili string olur
 */
export async function createAction<T = unknown, P = unknown>(
  handler: (
    params: P,
    supabase: Awaited<ReturnType<typeof createClient>>,
    userId: string | undefined
  ) => Promise<T>,
  options: ActionOptions = {}
): Promise<ApiResponse<T>> {
  // Apply default values
  const { requireAuth = ACTION_DEFAULTS.REQUIRE_AUTH, ...revalidationConfig } =
    options;

  try {
    // Create Supabase client
    const supabase = await createClient();

    // Get user ID with auth check
    const userId = await getUserId();

    // Auth kontrolü - requireAuth true ise userId zorunlu
    if (requireAuth && !userId) {
      throw new Error(CORE_ERROR_MESSAGES.LOGIN_REQUIRED);
    }

    // Execute handler - userId requireAuth kontrolünden geçtiyse garantili string | undefined
    const result = await handler(null as P, supabase, userId);

    // Perform cache revalidation
    await performRevalidation(revalidationConfig);

    return { success: true, data: result };
  } catch (error: unknown) {
    return await handleActionError(error, {
      requireAuth,
      ...revalidationConfig,
    });
  }
}

/**
 * Public (cookie-free) action creator function
 * - Does NOT touch cookies; uses anon Supabase client
 * - Never reads user session; userId is undefined
 */
export async function createActionPublic<T = unknown, P = unknown>(
  handler: (
    params: P,
    supabase: Awaited<ReturnType<typeof createAnonClient>>,
    userId: undefined
  ) => Promise<T>,
  options: ActionOptions = {}
): Promise<ApiResponse<T>> {
  const revalidationConfig = options;

  try {
    // Create anon Supabase client (no cookies)
    const supabase = await createAnonClient();

    // Execute handler with no userId context
    const result = await handler(null as P, supabase, undefined);

    // Perform cache revalidation if configured
    await performRevalidation(revalidationConfig);

    return { success: true, data: result };
  } catch (error: unknown) {
    return await handleActionError(error, {
      requireAuth: false,
      ...revalidationConfig,
    });
  }
}

/**
 * Admin action creator function
 * Following Clean Code principles - dedicated function for admin operations
 * Auth kontrolü otomatik olarak yapılır ve adminClient garantili sağlanır
 */
export async function createAdminAction<T = unknown, P = unknown>(
  handler: (
    params: P,
    supabase: Awaited<ReturnType<typeof createClient>>,
    userId: string,
    adminClient: ReturnType<typeof getSupabaseAdmin>
  ) => Promise<T>,
  options: ActionOptions = {}
): Promise<ApiResponse<T>> {
  // Admin actions always require auth
  const revalidationConfig = options;

  try {
    // Create Supabase client
    const supabase = await createClient();

    // Get user ID with auth check
    const userId = await getUserId();

    // Auth kontrolü - admin işlemler her zaman auth gerektirir
    if (!userId) {
      throw new Error(CORE_ERROR_MESSAGES.LOGIN_REQUIRED);
    }

    // Load admin client - garantili sağlanır
    const adminClient = getSupabaseAdmin();

    // Execute handler - userId garantili string, adminClient garantili
    const result = await handler(null as P, supabase, userId, adminClient);

    // Perform cache revalidation
    await performRevalidation(revalidationConfig);

    return { success: true, data: result };
  } catch (error: unknown) {
    return await handleActionError(error, {
      requireAuth: true,
      ...revalidationConfig,
    });
  }
}

// ============================================================================

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Revalidate multiple paths
 * Following Clean Code principles - simple utility function
 */
export async function revalidatePaths(paths: string[]) {
  paths.forEach(path => revalidatePath(path));
}
