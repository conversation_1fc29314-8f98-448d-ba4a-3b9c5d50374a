import { GymMembersHeader } from './components/gym-members-header';
import { getGymMembers } from '@/lib/actions/all-actions';
import { GymMembersClient } from './components/gym-members-client';

export default async function GymMembersPage({
  params,
}: {
  params: Promise<{ gymId: string }>;
}) {
  const { gymId } = await params;
  const membersResult = await getGymMembers(gymId);

  if (!membersResult.success) {
    throw new Error(membersResult.error || 'Üyeler yüklenemedi');
  }

  const members = membersResult.data?.members || [];
  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Üyeler</h1>
            <p className="text-muted-foreground">
              Salon üyelerini görüntüleyin ve yönetin
            </p>
          </div>
          <GymMembersHeader gymId={gymId} />
        </div>
      </div>
      <div className="space-y-6">
        <GymMembersClient members={members} gymId={gymId} />
      </div>
    </div>
  );
}
