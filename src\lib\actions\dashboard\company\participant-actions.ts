'use server';

import { z } from 'zod';
import { ApiResponse } from '@/types/global/api';
import { AppointmentParticipants } from '@/types/database/tables';
import { ParticipantStatus } from '@/lib/supabase/types';
import { createNotification } from '@/lib/actions/notifications/notification-actions';
import { createAction, validateFormData } from '../../core';

const updateParticipantStatusSchema = z.object({
  appointment_participant_id: z.uuid('Geçersiz katılımcı ID'),
  status: z.enum(['confirmed', 'cancelled', 'no_show', 'completed']),
});

export async function updateAppointmentParticipantStatus(
  formData: FormData
): Promise<ApiResponse<AppointmentParticipants>> {
  const validation = await validateFormData(
    formData,
    updateParticipantStatusSchema
  );
  if (validation.error) {
    throw new Error(validation.error);
  }
  const { appointment_participant_id, status } = validation.data!;

  return createAction<AppointmentParticipants>(
    async (_, supabase) => {
      const { data, error } = await supabase
        .from('appointment_participants')
        .update({ status: status as ParticipantStatus })
        .eq('id', appointment_participant_id)
        .select(
          `
          *,
          appointment:appointments!appointment_participants_appointment_id_fkey(
            id,
            appointment_date,
            gym_id,
            trainer_profile:profiles!appointments_trainer_profile_id_fkey(
              id,
              full_name
            ),
            gym:gyms!appointments_gym_id_fkey(
              name
            )
          ),
          membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
            membership:gym_memberships!gym_membership_packages_membership_id_fkey(
              profile_id
            ),
            gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(
              name
            )
          )
        `
        )
        .single();
      if (error || !data) {
        throw new Error('Katılımcı durumu güncellenemedi');
      }

      // Bildirim: sadece 'cancelled' için gönder
      try {
        if (status === 'cancelled') {
          const recipientUserId = (data as any)?.membership_package?.membership
            ?.profile_id as string | undefined;
          const aptDate = (data as any)?.appointment?.appointment_date as
            | string
            | undefined;
          const gymId = (data as any)?.appointment?.gym_id as
            | string
            | undefined;
          if (recipientUserId) {
            const gymName = (data as any)?.appointment?.gym?.name as
              | string
              | undefined;
            const trainerName = (data as any)?.appointment?.trainer_profile
              ?.full_name as string | undefined;
            const packageName = (data as any)?.membership_package?.gym_package
              ?.name as string | undefined;

            const parts: string[] = [];
            if (aptDate) parts.push(new Date(aptDate).toLocaleString('tr-TR'));
            if (gymName) parts.push(`Salon: ${gymName}`);
            if (trainerName) parts.push(`Antrenör: ${trainerName}`);
            if (packageName) parts.push(`Paket: ${packageName}`);

            const detail = parts.length ? ` (${parts.join(' • ')})` : '';

            await createNotification({
              recipientUserId,
              gymId,
              title: 'Randevu İptali',
              message: `Randevu katılımınız iptal edildi${detail}`,
            });
          }
        }
      } catch (e) {
        console.error('Katılımcı iptal bildirimi gönderilemedi', e);
      }

      return data as any;
    },
    { requireAuth: true }
  );
}

/**
 * Bir katılımcıyı aynı paketteki başka bir randevuya taşır
 */
export async function moveParticipantToAppointment(
  appointmentParticipantId: string,
  targetAppointmentId: string,
  gymId?: string
): Promise<ApiResponse<AppointmentParticipants>> {
  return createAction<AppointmentParticipants>(
    async (_, supabase) => {
      // Kaynağı (katılımcı) çek
      const { data: participant, error: partErr } = await supabase
        .from('appointment_participants')
        .select(
          `
          id,
          appointment_id,
          status,
          gym_membership_package_id,
          membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
            id,
            gym_package_id
          )
        `
        )
        .eq('id', appointmentParticipantId)
        .single();
      if (partErr || !participant) {
        throw new Error('Katılımcı bulunamadı');
      }

      // Hedef randevuyu çek
      const { data: target, error: targetErr } = await supabase
        .from('appointments')
        .select('id, gym_package_id, max_participants, status')
        .eq('id', targetAppointmentId)
        .single();
      if (targetErr || !target) {
        throw new Error('Hedef randevu bulunamadı');
      }
      if (target.status !== 'scheduled') {
        throw new Error('Yalnızca planlanmış randevulara taşıyabilirsiniz');
      }

      // Paket uyuşması zorunlu
      const sourcePkgId = (participant as any)?.membership_package
        ?.gym_package_id as string | null;
      if (!sourcePkgId || sourcePkgId !== (target as any).gym_package_id) {
        throw new Error('Paket uyuşmuyor');
      }

      // Kapasite kontrolü
      const { data: targetParts, error: partsErr } = await supabase
        .from('appointment_participants')
        .select('id, gym_membership_package_id, status')
        .eq('appointment_id', targetAppointmentId);
      if (partsErr) {
        throw new Error('Hedef randevu katılımcıları alınamadı');
      }
      const confirmedCount = (targetParts || []).filter(
        p => (p as any).status === 'confirmed'
      ).length;
      if (
        typeof target.max_participants === 'number' &&
        confirmedCount >= target.max_participants
      ) {
        throw new Error('Hedef randevu dolu');
      }

      // Aynı üye zaten hedef randevuda mı?
      const alreadyInTarget = (targetParts || []).some(
        p =>
          (p as any).gym_membership_package_id ===
          participant.gym_membership_package_id
      );
      if (alreadyInTarget) {
        throw new Error('Üye zaten hedef randevuda');
      }

      // Taşı: appointment_id güncelle
      const { data: updated, error: updateErr } = await supabase
        .from('appointment_participants')
        .update({ appointment_id: targetAppointmentId })
        .eq('id', appointmentParticipantId)
        .select()
        .single();
      if (updateErr || !updated) {
        throw new Error('Katılımcı taşınamadı');
      }
      return updated as any;
    },
    {
      requireAuth: true,
      revalidatePaths: [
        '/dashboard/manager/appointments',
        '/dashboard/trainer/appointments',
        ...(gymId ? [`/dashboard/gym/${gymId}/appointments`] : []),
      ],
    }
  );
}

/**
 * Bir katılımcıyı seçili antrenörde belirtilen tarih/saat slotuna taşır.
 * Aynı pakette mevcut uygun randevu varsa ona taşır; yoksa yeni randevu oluşturur.
 */
export async function moveParticipantToDateTime(
  appointmentParticipantId: string,
  trainerProfileId: string,
  targetAppointmentDateISO: string,
  gymId: string
): Promise<ApiResponse<{ appointmentId: string }>> {
  return createAction<{ appointmentId: string }>(
    async (_, supabase) => {
      // Katılımcı ve paket bilgisini al
      const { data: participant, error: partErr } = await supabase
        .from('appointment_participants')
        .select(
          `
          id,
          appointment_id,
          gym_membership_package_id,
          membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
            id,
            gym_package_id,
            gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(
              id,
              name,
              package_type,
              max_participants,
              session_duration_minutes
            )
          )
        `
        )
        .eq('id', appointmentParticipantId)
        .single();
      if (partErr || !participant) {
        throw new Error('Katılımcı bulunamadı');
      }

      const pkg = (participant as any).membership_package?.gym_package;
      const pkgId = (participant as any).membership_package?.gym_package_id as
        | string
        | null;
      if (!pkg || !pkgId) {
        throw new Error('Paket bilgisi bulunamadı');
      }

      // 1) Aynı pakette, aynı saate, aynı antrenörde mevcut randevu var mı?
      const { data: existing, error: existErr } = await supabase
        .from('appointments')
        .select('id, gym_package_id, max_participants, status')
        .eq('gym_id', gymId)
        .eq('trainer_profile_id', trainerProfileId)
        .eq('status', 'scheduled')
        .eq(
          'appointment_date',
          new Date(targetAppointmentDateISO).toISOString()
        )
        .eq('gym_package_id', pkgId)
        .maybeSingle();
      if (existErr) {
        throw new Error('Randevular kontrol edilirken hata');
      }

      if (existing) {
        // Kapasite kontrol et
        const { data: targetParts } = await supabase
          .from('appointment_participants')
          .select('id, gym_membership_package_id, status')
          .eq('appointment_id', existing.id);
        const confirmedCount = (targetParts || []).filter(
          p => (p as any).status === 'confirmed'
        ).length;
        if (
          typeof existing.max_participants === 'number' &&
          confirmedCount >= existing.max_participants
        ) {
          throw new Error('Hedef randevu dolu');
        }
        // Güncelle ve bitir
        const { data: updated, error: updateErr } = await supabase
          .from('appointment_participants')
          .update({ appointment_id: existing.id })
          .eq('id', appointmentParticipantId)
          .select()
          .single();
        if (updateErr || !updated) {
          throw new Error('Katılımcı taşınamadı');
        }
        return { appointmentId: existing.id } as any;
      }

      // 2) Aynı saatte bu antrenörde başka randevu var mı? (Çakışma kontrolü)
      const { data: conflicts, error: conflictErr } = await supabase
        .from('appointments')
        .select('id')
        .eq('gym_id', gymId)
        .eq('trainer_profile_id', trainerProfileId)
        .eq('status', 'scheduled')
        .eq(
          'appointment_date',
          new Date(targetAppointmentDateISO).toISOString()
        );
      if (conflictErr) {
        throw new Error('Müsaitlik kontrolü başarısız');
      }
      if ((conflicts || []).length > 0) {
        throw new Error('Bu saat bu antrenör için uygun değil');
      }

      // 3) Yeni randevu oluştur ve katılımcıyı taşı
      const { data: created, error: createErr } = await supabase
        .from('appointments')
        .insert({
          gym_id: gymId,
          appointment_date: new Date(targetAppointmentDateISO).toISOString(),
          appointment_type: pkg.package_type,
          max_participants: pkg.max_participants ?? 1,
          trainer_profile_id: trainerProfileId,
          gym_package_id: pkg.id,
          status: 'scheduled',
        })
        .select('id')
        .single();
      if (createErr || !created) {
        throw new Error('Yeni randevu oluşturulamadı');
      }

      const { data: updated, error: updateErr } = await supabase
        .from('appointment_participants')
        .update({ appointment_id: created.id })
        .eq('id', appointmentParticipantId)
        .select()
        .single();
      if (updateErr || !updated) {
        throw new Error('Katılımcı yeni randevuya taşınamadı');
      }

      return { appointmentId: created.id } as any;
    },
    {
      requireAuth: true,
      revalidatePaths: [
        '/dashboard/manager/appointments',
        '/dashboard/trainer/appointments',
        `/dashboard/gym/${gymId}/appointments`,
      ],
    }
  );
}
