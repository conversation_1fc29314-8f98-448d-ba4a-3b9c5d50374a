'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  CreditCard,
  Check,
  AlertCircle,
  ArrowLeft,
  CheckCircle2,
  PartyPopper,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import type { PlatformPackages } from '@/types/database/tables';
import { createManager } from '@/lib/actions/user/manager-actions';
import Link from 'next/link';

interface OnboardingPaymentStepProps {
  packages: PlatformPackages[];
  onBack: () => void;
  onSuccess: () => void;
}

type SubscriptionPlan = 'monthly' | 'yearly';

function formatFeatures(features: unknown): string[] {
  // 1) Array<string>
  if (Array.isArray(features)) {
    return features.filter((v): v is string => typeof v === 'string');
  }
  // 2) JSON string fallback
  if (typeof features === 'string') {
    try {
      const parsed = JSON.parse(features);
      return formatFeatures(parsed);
    } catch {
      return [];
    }
  }
  // 3) Object form: { perks: string[], ... }
  if (typeof features === 'object' && features !== null) {
    const obj = features as Record<string, unknown>;
    if (Array.isArray(obj.perks)) {
      return (obj.perks as unknown[]).filter(
        (v): v is string => typeof v === 'string'
      );
    }
    // Direct string values in object
    const directStrings = Object.values(obj).filter(
      (v): v is string => typeof v === 'string'
    );
    if (directStrings.length) return directStrings;
    // Nested arrays of strings
    const nested = Object.values(obj)
      .flatMap(v => (Array.isArray(v) ? v : []))
      .filter((v): v is string => typeof v === 'string');
    return nested;
  }
  return [];
}

export function OnboardingPaymentStep({
  packages,
  onBack,
  onSuccess,
}: OnboardingPaymentStepProps) {
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const selectedPackage = packages.find(p => p.id === selectedId) || null;

  const handlePay = async () => {
    if (!selectedPackage) return;
    setIsLoading(true);
    setError(null);
    try {
      // Simulate payment latency
      await new Promise(resolve => setTimeout(resolve, 1200));

      const result = await createManager(selectedPackage.id);
      if (result.success) {
        toast.success(result.message || 'Abonelik oluşturuldu');
        setIsSuccess(true);
        // Parent'a haber vermek istenirse yine de çağır
        if (onSuccess) {
          try {
            onSuccess();
          } catch {}
        }
      } else {
        setError(result.error || 'Ödeme tamamlanamadı');
      }
    } catch (e) {
      setError(e instanceof Error ? e.message : 'Beklenmeyen bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      {isSuccess && (
        <Card className="border-green-200 bg-green-50/60 dark:border-green-900/40 dark:bg-green-950/30">
          <CardHeader>
            <div className="flex items-center gap-3">
              <CheckCircle2 className="h-6 w-6 text-green-600" />
              <CardTitle>Ödemeniz başarıyla tamamlandı!</CardTitle>
            </div>
            <CardDescription className="mt-2 flex items-center gap-2">
              <PartyPopper className="text-primary h-4 w-4" />
              Artık salon kurulumuna geçebilirsiniz.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-3 sm:flex-row">
              <Link
                href="/dashboard/manager/gym-setup"
                className="w-full sm:w-auto"
              >
                <Button className="w-full" size="lg">
                  Salon Kurulumuna Başla
                </Button>
              </Link>
              <Link href="/dashboard/manager" className="w-full sm:w-auto">
                <Button variant="outline" className="w-full" size="lg">
                  Şirket Paneline Git
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Geri
        </Button>
      </div>

      {!isSuccess && (
        <div className="grid gap-6 md:grid-cols-2">
          {packages.map(pkg => {
            const features = formatFeatures(pkg.features);
            const isSelected = selectedId === pkg.id;
            const planLabel: SubscriptionPlan =
              pkg.duration === 'yearly' ? 'yearly' : 'monthly';
            return (
              <Card
                key={pkg.id}
                className={cn(
                  'cursor-pointer transition-shadow',
                  isSelected ? 'ring-primary shadow-lg ring-2' : 'hover:shadow'
                )}
                onClick={() => setSelectedId(pkg.id)}
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>{pkg.name}</CardTitle>
                  </div>
                  <CardDescription>
                    {planLabel === 'yearly' ? 'Yıllık Plan' : 'Aylık Plan'} •{' '}
                    {pkg.price}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    {features.map((f, idx) => (
                      <li key={idx} className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-green-600" /> {f}
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button
                    variant={isSelected ? 'default' : 'outline'}
                    className="w-full"
                  >
                    {isSelected ? 'Seçildi' : 'Bu Paketi Seç'}
                  </Button>
                </CardFooter>
              </Card>
            );
          })}
        </div>
      )}

      <Separator />

      {!isSuccess && (
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            <h3 className="text-lg font-semibold">Ödeme</h3>
          </div>
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          <Button
            disabled={!selectedPackage || isLoading}
            onClick={handlePay}
            className="w-full"
          >
            {isLoading ? 'Ödeme İşleniyor...' : 'Ödeme Yap ve Başla'}
          </Button>
        </div>
      )}
    </div>
  );
}
