import Image from 'next/image';
import {
  AnimatedSection,
  StaggeredAnimation,
} from '@/components/ui/animated-section';
import { Card, CardContent } from '@/components/ui/card';
import { StarDisplay } from '@/components/ui/star-rating';

interface TestimonialItem {
  name: string;
  role: string;
  company: string;
  quote: string;
  rating: number;
  avatar?: string;
}

export function PremiumTestimonials() {
  const testimonials: TestimonialItem[] = [
    {
      name: '<PERSON><PERSON><PERSON> <PERSON><PERSON>',
      role: '<PERSON><PERSON>üdür',
      company: 'FitLife Şubeleri',
      quote:
        '<PERSON>ube bazlı raporlama ile operasyonel kararları çok daha hızlı alıyoruz. Randevu çakışmaları neredeyse bitti.',
      rating: 5,
      avatar: '/placeholder-user.jpg',
    },
    {
      name: '<PERSON><PERSON>',
      role: 'Operasyon Yöneticisi',
      company: 'ProGym',
      quote:
        'Twilio entegrasyonu sayesinde hatırlatma ve bildirim akışlarım<PERSON>z tamamen otomatik hale geldi.',
      rating: 5,
      avatar: '/placeholder-user.jpg',
    },
    {
      name: '<PERSON><PERSON>.',
      role: '<PERSON><PERSON><PERSON>',
      company: 'Core Studio',
      quote:
        'Raporlar ve panolar çok net. Takımın performansını anlık takip etmek büyük kolaylık sağlıyor.',
      rating: 5,
      avatar: '/placeholder-user.jpg',
    },
  ];

  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <AnimatedSection animation="fade-in">
          <div className="mx-auto mb-10 max-w-2xl text-center">
            <h2 className="text-3xl font-bold text-balance md:text-4xl">
              Referanslar
            </h2>
            <p className="text-muted-foreground mt-3">
              Müşterilerimizin gerçek yorumları.
            </p>
          </div>
        </AnimatedSection>

        <StaggeredAnimation
          animation="fade-up"
          className="mx-auto grid max-w-6xl grid-cols-1 gap-6 md:grid-cols-3"
        >
          {testimonials.map(t => (
            <Card key={t.name} className="h-full">
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="relative h-10 w-10 overflow-hidden rounded-full border">
                    <Image
                      src={t.avatar || '/placeholder-user.jpg'}
                      alt={`${t.name} avatar`}
                      fill
                      sizes="40px"
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <div className="leading-none font-medium">{t.name}</div>
                    <div className="text-muted-foreground text-xs">
                      {t.role} · {t.company}
                    </div>
                  </div>
                </div>

                <p className="text-foreground/90 mt-4 text-sm">“{t.quote}”</p>

                <div className="mt-4">
                  <StarDisplay
                    rating={t.rating}
                    totalReviews={undefined}
                    size="sm"
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </StaggeredAnimation>
      </div>
    </section>
  );
}
