'use server';

import { createAction } from '../core';
import { ManagerStatusData } from './types';
import { ManagerSubWithPackageDetails } from './profile-actions';
import { ApiResponse } from '@/types/global/api';

/**
 * Manager aboneliğini iptal eder
 * @param userId - Kullanıcı ID'si
 * @returns boolean - İşlem başarılı ise true
 */
export async function cancelManagerSubscription(): Promise<
  ApiResponse<{ success: boolean }>
> {
  return createAction<{ success: boolean }>(async (_, supabase, authUserId) => {
    const { error } = await supabase
      .from('companies')
      .update({
        status: 'passive',
        updated_at: new Date().toISOString(),
      })
      .eq('manager_profile_id', authUserId);

    if (error) {
      throw new Error(`Manager abonelik iptal hatası: ${error.message}`);
    }

    return { success: true };
  });
}

/**
 * Manager status bilgilerini getirir
 */
export async function getManagerStatus(): Promise<
  ApiResponse<ManagerStatusData>
> {
  return createAction<ManagerStatusData>(async (_, supabase, userId) => {
    // Company bilgilerini direkt companies tablosundan al
    const { data: company } = await supabase
      .from('companies')
      .select('id, status')
      .eq('manager_profile_id', userId)
      .maybeSingle();

    if (!company) {
      return { isManager: false };
    }

    // Company bilgilerini platform package ile birlikte al
    const { data: companyData, error: companyError } = await supabase
      .from('companies')
      .select(
        `
          platform_package_id,
          subscription_start_date,
          subscription_end_date,
          status,
          platform_packages!inner(
            name,
            tier,
            duration
          )
        `
      )
      .eq('id', company.id)
      .single();

    if (companyError || !companyData) {
      return { isManager: false };
    }

    // Kalan gün hesapla
    let daysRemaining = 0;
    if (companyData.subscription_end_date) {
      const endDate = new Date(companyData.subscription_end_date);
      const today = new Date();
      const diffTime = endDate.getTime() - today.getTime();
      daysRemaining = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    const packageData = companyData.platform_packages as any;

    return {
      isManager: company.status === 'active',
      tier: packageData?.tier,
      packageType: packageData?.duration,
      subscriptionStartDate: companyData.subscription_start_date,
      subscriptionEndDate: companyData.subscription_end_date,
      status: companyData.status,
      daysRemaining: Math.max(0, daysRemaining),
    };
  });
}

/**
 * Kullanıcının manager details bilgilerini getirir
 */
export async function getManagerDetail(): Promise<
  ApiResponse<ManagerSubWithPackageDetails>
> {
  return createAction<ManagerSubWithPackageDetails>(
    async (_, supabase, userId) => {
      // Company bilgilerini platform package ile birlikte al
      const { data: companyData, error: companyError } = await supabase
        .from('companies')
        .select(
          `
          *,
          platform_packages!inner(
            id,
            name,
            tier,
            duration,
            price,
            max_gyms,
            max_members,
            max_trainers,
            max_staff,
            max_monthly_appointments,
            features,
            is_active
          )
        `
        )
        .eq('manager_profile_id', userId)
        .single();

      if (companyError) {
        // PGRST116 = no rows returned (normal durum - henüz company bilgisi yok)
        if (companyError.code === 'PGRST116') {
          return {
            company: {
              id: '',
              manager_profile_id: '',
              name: '',
              logo_url: null,
              trial_ends_at: null,
              phone: null,
              email: null,
              status: null,
              created_at: null,
              updated_at: null,
              subscription_start_date: null,
              subscription_end_date: null,
              platform_package_id: null,
              social_links: null,
            },
            package: {
              id: '',
              name: '',
              tier: '',
              duration: '',
              price: 0,
              max_gyms: null,
              max_members: null,
              max_trainers: null,
              max_staff: null,
              max_monthly_appointments: null,
              features: null,
              is_active: null,
            },
          };
        }
        throw new Error('Şirket bilgileri alınamadı: ' + companyError.message);
      }

      const packageData = companyData.platform_packages as any;

      return {
        company: companyData,
        package: {
          id: packageData?.id || '',
          name: packageData?.name || '',
          tier: packageData?.tier || '',
          duration: packageData?.duration || '',
          price: packageData?.price || 0,
          max_gyms: packageData?.max_gyms || null,
          max_members: packageData?.max_members || null,
          max_trainers: packageData?.max_trainers || null,
          max_staff: packageData?.max_staff || null,
          max_monthly_appointments: packageData?.max_monthly_appointments || null,
          features: packageData?.features || null,
          is_active: packageData?.is_active || null,
        },
      };
    }
  );
}

/**
 * Manager kullanım istatistiklerini getirir
 * - Aktif salon sayısı
 * - Toplam aktif üye sayısı (tüm salonlar)
 * - Abonelik bitişine kalan gün
 */
export async function getManagerUsageStats(): Promise<
  ApiResponse<{ 
    gymsUsed: number; 
    membersUsed: number; 
    trainersUsed: number;
    staffUsed: number;
    monthlyAppointmentsUsed: number;
    daysRemaining: number;
  }>
> {
  return createAction<{
    gymsUsed: number;
    membersUsed: number;
    trainersUsed: number;
    staffUsed: number;
    monthlyAppointmentsUsed: number;
    daysRemaining: number;
  }>(async (_, supabase, userId) => {
    if (!userId) {
      throw new Error('Giriş yapmanız gerekmektedir.');
    }

    // Manager'ın şirketini, abonelik bitiş tarihini ve deneme süresini al
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('id, subscription_end_date, trial_ends_at')
      .eq('manager_profile_id', userId)
      .single();

    if (companyError || !company) {
      // Şirket yoksa 0 değerleri döndür
      return { 
        gymsUsed: 0, 
        membersUsed: 0, 
        trainersUsed: 0,
        staffUsed: 0,
        monthlyAppointmentsUsed: 0,
        daysRemaining: 0 
      };
    }

    // Şirkete bağlı aktif salonları çek
    const { data: gyms, error: gymsError } = await supabase
      .from('gyms')
      .select('id')
      .eq('owner_profile_id', company.id)
      .eq('status', 'active');

    if (gymsError) {
      throw new Error(`Salonlar alınamadı: ${gymsError.message}`);
    }

    const gymIds = (gyms || []).map(g => g.id);
    const gymsUsed = gymIds.length;

    // Tüm salonlar için aktif üyelik sayısını say
    let membersUsed = 0;
    if (gymIds.length > 0) {
      const { count: membersCount, error: membersError } = await supabase
        .from('gym_memberships')
        .select('*', { count: 'exact', head: true })
        .in('gym_id', gymIds)
        .eq('status', 'active');

      if (membersError) {
        throw new Error(`Üye sayısı alınamadı: ${membersError.message}`);
      }
      membersUsed = membersCount || 0;
    }

    // Kalan gün hesapla
    let daysRemaining = 0;
    const now = new Date();
    
    // Önce deneme süresini kontrol et
    if (company.trial_ends_at) {
      const trialEnd = new Date(company.trial_ends_at);
      if (now > trialEnd) {
        // Deneme süresi dolmuş, abonelik süresini kontrol et
        if (company.subscription_end_date) {
          const end = new Date(company.subscription_end_date);
          const diff = end.getTime() - now.getTime();
          daysRemaining = Math.max(0, Math.ceil(diff / (1000 * 60 * 60 * 24)));
        }
      } else {
        // Deneme süresi devam ediyor
        const diff = trialEnd.getTime() - now.getTime();
        daysRemaining = Math.max(0, Math.ceil(diff / (1000 * 60 * 60 * 24)));
      }
    } else if (company.subscription_end_date) {
      // Abonelik süresini kontrol et
      const end = new Date(company.subscription_end_date);
      const diff = end.getTime() - now.getTime();
      daysRemaining = Math.max(0, Math.ceil(diff / (1000 * 60 * 60 * 24)));
    }

    // Tüm salonlar için aktif antrenör sayısını say
    let trainersUsed = 0;
    if (gymIds.length > 0) {
      const { count: trainersCount, error: trainersError } = await supabase
        .from('gym_trainers')
        .select('*', { count: 'exact', head: true })
        .in('gym_id', gymIds)
        .eq('status', 'active');

      if (trainersError) {
        throw new Error(`Antrenör sayısı alınamadı: ${trainersError.message}`);
      }
      trainersUsed = trainersCount || 0;
    }

    // Tüm salonlar için aktif personel sayısını say
    let staffUsed = 0;
    if (gymIds.length > 0) {
      const { count: staffCount, error: staffError } = await supabase
        .from('gym_staffs')
        .select('*', { count: 'exact', head: true })
        .in('gym_id', gymIds);

      if (staffError) {
        throw new Error(`Personel sayısı alınamadı: ${staffError.message}`);
      }
      staffUsed = staffCount || 0;
    }

    // Bu ay yapılan randevu sayısını say
    let monthlyAppointmentsUsed = 0;
    if (gymIds.length > 0) {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      const { count: appointmentsCount, error: appointmentsError } = await supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true })
        .in('gym_id', gymIds)
        .gte('appointment_date', startOfMonth.toISOString())
        .lte('appointment_date', endOfMonth.toISOString());

      if (appointmentsError) {
        throw new Error(`Randevu sayısı alınamadı: ${appointmentsError.message}`);
      }
      monthlyAppointmentsUsed = appointmentsCount || 0;
    }

    return { 
      gymsUsed, 
      membersUsed, 
      trainersUsed,
      staffUsed,
      monthlyAppointmentsUsed,
      daysRemaining 
    };
  });
}

/**
 * Manager kaydı oluşturur (platform package satın alma)
 * @param platformPackageId - Platform paket ID'si
 * @returns İşlem sonucu
 */
export async function createManager(
  platformPackageId: string
): Promise<ApiResponse<{ success: boolean; message: string }>> {
  return createAction<{ success: boolean; message: string }>(
    async (_, supabase, userId) => {
      if (!platformPackageId) {
        throw new Error('Platform paket ID gereklidir.');
      }

      // Önce platform paket bilgilerini al
      const { data: packageData, error: packageError } = await supabase
        .from('platform_packages')
        .select('*')
        .eq('id', platformPackageId)
        .eq('is_active', true)
        .single();

      if (packageError) {
        throw new Error(`Platform paketi bulunamadı: ${packageError.message}`);
      }

      if (!packageData) {
        throw new Error('Platform paketi bulunamadı.');
      }

      // Kullanıcının aktif manager aboneliği var mı? companies tablosundan kontrol et
      const { data: existingCompany, error: companyErr } = await supabase
        .from('companies')
        .select('status')
        .eq('manager_profile_id', userId)
        .eq('status', 'active')
        .maybeSingle();

      if (companyErr) {
        throw new Error(
          `Mevcut abonelik kontrolü yapılamadı: ${companyErr.message}`
        );
      }

      if (existingCompany) {
        throw new Error('Zaten aktif bir manager aboneliğiniz bulunmaktadır.');
      }

      // Subscription tarihlerini hesapla - ilk ay ücretsiz
      const startDate = new Date();
      const trialEndDate = new Date();
      trialEndDate.setDate(trialEndDate.getDate() + 30); // 30 gün deneme süresi
      
      // Bitiş tarihi deneme süresinden sonra başlayacak
      const endDate = new Date(trialEndDate);
      
      // Duration'a göre bitiş tarihini hesapla
      if (packageData.duration === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1);
      } else if (packageData.duration === 'yearly') {
        endDate.setFullYear(endDate.getFullYear() + 1);
      } else {
        throw new Error('Geçersiz paket süresi.');
      }

      // Kullanıcının company'sini bul ve subscription bilgilerini güncelle
      const { data: userCompany, error: companyFindError } = await supabase
        .from('companies')
        .select('id, status')
        .eq('manager_profile_id', userId)
        .single();

      if (companyFindError) {
        throw new Error(
          `Şirket bulunamadı. Önce şirket oluşturmanız gerekiyor: ${companyFindError.message}`
        );
      }

      // Eğer company zaten active ise hata ver
      if (userCompany.status === 'active') {
        throw new Error('Zaten aktif bir manager aboneliğiniz bulunmaktadır.');
      }

      // Company'nin subscription bilgilerini güncelle
      const { error: updateError } = await supabase
        .from('companies')
        .update({
          platform_package_id: platformPackageId,
          subscription_start_date: startDate.toISOString(),
          subscription_end_date: endDate.toISOString(),
          trial_ends_at: trialEndDate.toISOString(),
          status: 'active',
          updated_at: new Date().toISOString(),
        })
        .eq('id', userCompany.id);

      if (updateError) {
        throw new Error(
          `Abonelik bilgileri güncellenemedi: ${updateError.message}`
        );
      }

      return {
        success: true,
        message: `${packageData.name} paketine başarıyla abone oldunuz! Artık salon yöneticisi olarak sistemi kullanabilirsiniz.`,
      };
    }
  );
}
