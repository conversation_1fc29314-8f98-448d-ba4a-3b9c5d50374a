'use server';

import { createAdminAction } from '@/lib/actions/core/core';
import { ApiResponse } from '@/types/global/api';
import {
  createGymMembershipRecord,
  enforceGymMemberLimitForAdmin,
} from '@/lib/actions/dashboard/member/membership-actions';
import {
  validateUserCreationData,
  generateUserPassword,
} from '@/lib/actions/user/user-creation-utils';

interface ImportMemberRowParams {
  gymId: string;
  userData: {
    email: string | null;
    phone_number: string | null;
    profileData: { full_name: string; avatar_url?: string | null };
  };
  options?: {
    upsertOnDuplicate?: boolean;
  };
}

interface ImportRowResult {
  updated?: boolean;
  message?: string;
  status?: 'created' | 'updated' | 'skipped' | 'conflict';
  conflict_reason?: string;
}

// Satır bazlı import action'ı: auth + profiles + gym_memberships
export async function importMemberRowAction(
  params: ImportMemberRowParams
): Promise<ApiResponse<ImportRowResult>> {
  const { gymId, userData } = params;

  return createAdminAction<ImportRowResult>(
    async (_, supabase, _userId, adminClient) => {
      // 1) Sade telefon formatlama (sadece rakam)
      const normalizedPhone = userData.phone_number
        ? userData.phone_number.replace(/\D/g, '') // sadece 0-9
        : null;
      // Supabase Auth createUser için E.164 zorunlu olduğundan, sadece bu çağrı için basit TR dönüştürme
      const phoneForAuth = (() => {
        if (!normalizedPhone) return undefined;
        const d = normalizedPhone;
        if (d.length === 11 && d.startsWith('0')) return `+90${d.slice(1)}`; // 0XXXXXXXXXX -> +90XXXXXXXXXX
        if (d.length === 12 && d.startsWith('90')) return `+${d}`; // 90XXXXXXXXXXX -> +90XXXXXXXXXXX
        if (d.length === 10 && d.startsWith('5')) return `+90${d}`; // 5XXXXXXXXX -> +905XXXXXXXXX
        return undefined; // Diğer durumlarda phone'u göndermeyelim
      })();

      // 2) En azından telefon veya email gerekli (eski kontrol duruyor)
      const validation = validateUserCreationData({
        email: userData.email,
        phone_number: normalizedPhone,
      });
      if (!validation.isValid) {
        throw new Error(
          'E-posta veya telefon numarasından en az biri gereklidir'
        );
      }

      // 3) Duplicate kontrolü (email ve telefon ayrı ayrı)
      let existingProfileId: string | null = null;
      let emailExists = false;
      let phoneExists = false;

      let existingByEmailId: string | null = null;
      if (validation.hasEmail && userData.email) {
        const { data: existingByEmail } = await adminClient
          .from('profiles')
          .select('id')
          .eq('email', userData.email)
          .maybeSingle();
        existingByEmailId = existingByEmail?.id ?? null;
        emailExists = Boolean(existingByEmailId);
      }

      let existingByPhoneId: string | null = null;
      if (validation.hasPhone && normalizedPhone) {
        const { data: existingByPhone } = await adminClient
          .from('profiles')
          .select('id')
          .eq('phone_number', normalizedPhone)
          .maybeSingle();
        existingByPhoneId = existingByPhone?.id ?? null;
        phoneExists = Boolean(existingByPhoneId);
      }

      // Karar:
      // - Eğer phone zaten varsa, o profile'a üyelik (profil güncelleme yok)
      // - Eğer email varsa ama phone yoksa ve valid bir phone sağlandıysa: email'i yok say, phone ile yeni hesap oluştur
      // - Aksi halde email varsa: mevcut profile üyelik
      // - Hiçbiri yoksa: yeni kullanıcı oluştur
      const canCreateWithPhoneOnly =
        emailExists && !phoneExists && !!normalizedPhone && !!phoneForAuth;

      if (phoneExists) {
        existingProfileId = existingByPhoneId;
      } else if (emailExists && !canCreateWithPhoneOnly) {
        existingProfileId = existingByEmailId;
      } else {
        existingProfileId = null; // yeni kullanıcı yolu
      }

      if (!existingProfileId) {
        // 3) Yeni auth kullanıcısı + profile + membership oluştur
        const password = await generateUserPassword(
          userData.profileData.full_name
        );
        const emailForAuth = canCreateWithPhoneOnly
          ? undefined
          : validation.hasEmail
            ? userData.email!
            : undefined;
        const phoneForAuthParam =
          validation.hasPhone && phoneForAuth ? phoneForAuth : undefined;

        const { data: authCreated, error: authError } =
          await adminClient.auth.admin.createUser({
            email: emailForAuth,
            phone: phoneForAuthParam,
            password,
            email_confirm: emailForAuth ? true : undefined,
            phone_confirm: phoneForAuthParam ? true : undefined,
            user_metadata: { full_name: userData.profileData.full_name },
          });
        if (authError || !authCreated.user) {
          throw new Error(
            'Auth kullanıcı oluşturulamadı: ' + (authError?.message || '')
          );
        }
        const newUserId = authCreated.user.id;

        const { error: profileError } = await adminClient
          .from('profiles')
          .insert({
            id: newUserId,
            email: canCreateWithPhoneOnly ? null : userData.email,
            full_name: userData.profileData.full_name,
            avatar_url: userData.profileData.avatar_url ?? null,
            phone_number: normalizedPhone,
            is_guest_account: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });
        if (profileError) {
          // rollback auth user
          await adminClient.auth.admin.deleteUser(newUserId);
          throw new Error('Profil oluşturulamadı: ' + profileError.message);
        }

        // Limit kontrolü ve üyelik oluştur (RLS içinden çalışabilsin diye supabase client üzerinden)
        await enforceGymMemberLimitForAdmin(adminClient, gymId);
        await createGymMembershipRecord(supabase, newUserId, gymId);

        return {
          updated: false,
          status: 'created',
          message: 'Yeni kullanıcı oluşturuldu ve üyelik eklendi',
        };
      }

      // 4) Duplicate bulundu: PROFİL GÜNCELLEME YOK
      // Sadece üyelik yoksa ekle, varsa skip
      const { data: existingMembership } = await adminClient
        .from('gym_memberships')
        .select('id')
        .eq('gym_id', gymId)
        .eq('profile_id', existingProfileId)
        .maybeSingle();
      if (!existingMembership) {
        await enforceGymMemberLimitForAdmin(adminClient, gymId);
        await createGymMembershipRecord(supabase, existingProfileId!, gymId);
        return {
          updated: false,
          status: 'created',
          message: 'Mevcut kullanıcı için üyelik eklendi',
        };
      }
      return {
        updated: false,
        status: 'skipped',
        message: 'Mevcut kullanıcı bulundu, üyelik zaten mevcut',
      };
    },
    {
      revalidatePaths: [`/dashboard/gym/${gymId}/members`],
    }
  );
}
