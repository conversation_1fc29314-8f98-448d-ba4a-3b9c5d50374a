import { SettingsSidebar } from '@/components/profile-settings/SettingsSidebar';
import { SettingsHeader } from '@/components/profile-settings/SettingsHeader';
import { getUserRoles } from '@/lib/auth/server-auth';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

interface SettingsLayoutProps {
  children: React.ReactNode;
}

export default async function SettingsLayout({
  children,
}: SettingsLayoutProps) {
  // Server-side'da user roles'ları çek
  const userRoles = await getUserRoles();

  return (
    <div className="bg-background min-h-screen">
      {/* Sticky Header */}
      <div className="bg-background sticky top-0 z-40 border-b">
        <SettingsHeader />
      </div>

      {/* Ana içerik alanı */}
      <div className="mx-auto flex min-h-[calc(100vh-4rem)]">
        {/* Sidebar Navigation */}
        <SettingsSidebar userRoles={userRoles} />

        {/* Main Content */}
        <div className="flex-1 overflow-hidden">
          <main className="h-full overflow-y-auto p-4 lg:p-6">{children}</main>
        </div>
      </div>
    </div>
  );
}
