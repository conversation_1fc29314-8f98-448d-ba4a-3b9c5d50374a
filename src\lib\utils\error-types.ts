import { COMMON_PATTERNS } from '@/lib/actions/shared/constants';

// Standard error types
export enum ErrorType {
  VALIDATION = 'VALIDATION_ERROR',
  AUTHENTICATION = 'AUTHENTICATION_ERROR',
  AUTHORIZATION = 'AUTHORIZATION_ERROR',
  NOT_FOUND = 'NOT_FOUND_ERROR',
  CONFLICT = 'CONFLICT_ERROR',
  DATABASE = 'DATABASE_ERROR',
  EXTERNAL_API = 'EXTERNAL_API_ERROR',
  INTERNAL = 'INTERNAL_ERROR',
  RATE_LIMIT = 'RATE_LIMIT_ERROR',
  LIMIT_REACHED = 'LIMIT_REACHED',
}

// Standard error messages in Turkish
export const ErrorMessages = {
  [ErrorType.VALIDATION]: 'Girilen bilgiler geçersiz.',
  [ErrorType.AUTHENTICATION]: 'Bu işlem için giriş yapmanız gerekiyor.',
  [ErrorType.AUTHORIZATION]: 'Bu işlemi gerçekleştirmek için yetkiniz yok.',
  [ErrorType.NOT_FOUND]: 'Aranan kayıt bulunamadı.',
  [ErrorType.CONFLICT]:
    'Bu işlem çakışan veriler nedeniyle gerçekleştirilemedi.',
  [ErrorType.DATABASE]: 'Veritabanı işlemi sırasında bir hata oluştu.',
  [ErrorType.EXTERNAL_API]: 'Harici servis ile iletişimde bir hata oluştu.',
  [ErrorType.INTERNAL]: 'Sunucu hatası oluştu. Lütfen tekrar deneyin.',
  [ErrorType.RATE_LIMIT]: 'Çok fazla istek gönderdiniz. Lütfen bekleyin.',
  [ErrorType.LIMIT_REACHED]:
    'Plan limitine ulaşıldı. Lütfen planınızı yükseltin.',
} as const;

// Custom error class
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly context?: Record<string, any>;

  constructor(
    type: ErrorType,
    message?: string,
    statusCode: number = 400,
    context?: Record<string, any>
  ) {
    super(message || ErrorMessages[type]);
    this.type = type;
    this.statusCode = statusCode;
    this.isOperational = true;
    this.context = context;

    // Maintain proper stack trace
    Error.captureStackTrace(this, AppError);
  }
}

// Error factory functions
export const createValidationError = (
  message?: string,
  context?: Record<string, any>
) => new AppError(ErrorType.VALIDATION, message, 400, context);

export const createAuthenticationError = (
  message?: string,
  context?: Record<string, any>
) => new AppError(ErrorType.AUTHENTICATION, message, 401, context);

export const createAuthorizationError = (
  message?: string,
  context?: Record<string, any>
) => new AppError(ErrorType.AUTHORIZATION, message, 403, context);

export const createNotFoundError = (
  message?: string,
  context?: Record<string, any>
) => new AppError(ErrorType.NOT_FOUND, message, 404, context);

export const createConflictError = (
  message?: string,
  context?: Record<string, any>
) => new AppError(ErrorType.CONFLICT, message, 409, context);

export const createDatabaseError = (
  message?: string,
  context?: Record<string, any>
) => new AppError(ErrorType.DATABASE, message, 500, context);

export const createInternalError = (
  message?: string,
  context?: Record<string, any>
) => new AppError(ErrorType.INTERNAL, message, 500, context);

export const createLimitReachedError = (
  message?: string,
  context?: Record<string, any>
) => new AppError(ErrorType.LIMIT_REACHED, message, 403, context);

/**
 * Validation helper with standardized error handling
 */
export function validateRequired<T>(
  value: T | null | undefined,
  fieldName: string
): T {
  if (value === null || value === undefined || value === '') {
    throw createValidationError(`${fieldName} gereklidir.`);
  }
  return value;
}

/**
 * Email validation helper
 */
export function validateEmail(email: string): string {
  if (!COMMON_PATTERNS.EMAIL_REGEX.test(email)) {
    throw createValidationError('Geçerli bir e-posta adresi giriniz.');
  }
  return email;
}

/**
 * ID validation helper
 */
export function validateId(id: string, fieldName: string = 'ID'): string {
  if (!id || typeof id !== 'string' || id.trim().length === 0) {
    throw createValidationError(`Geçerli bir ${fieldName} gereklidir.`);
  }
  return id.trim();
}

/**
 * Numeric validation helper
 */
export function validateNumber(
  value: any,
  fieldName: string,
  options: { min?: number; max?: number; integer?: boolean } = {}
): number {
  const num = Number(value);

  if (isNaN(num)) {
    throw createValidationError(`${fieldName} geçerli bir sayı olmalıdır.`);
  }

  if (options.integer && !Number.isInteger(num)) {
    throw createValidationError(`${fieldName} tam sayı olmalıdır.`);
  }

  if (options.min !== undefined && num < options.min) {
    throw createValidationError(`${fieldName} en az ${options.min} olmalıdır.`);
  }

  if (options.max !== undefined && num > options.max) {
    throw createValidationError(
      `${fieldName} en fazla ${options.max} olmalıdır.`
    );
  }

  return num;
}

/**
 * Array validation helper
 */
export function validateArray<T>(
  value: any,
  fieldName: string,
  options: { minLength?: number; maxLength?: number } = {}
): T[] {
  if (!Array.isArray(value)) {
    throw createValidationError(`${fieldName} bir dizi olmalıdır.`);
  }

  if (options.minLength !== undefined && value.length < options.minLength) {
    throw createValidationError(
      `${fieldName} en az ${options.minLength} öğe içermelidir.`
    );
  }

  if (options.maxLength !== undefined && value.length > options.maxLength) {
    throw createValidationError(
      `${fieldName} en fazla ${options.maxLength} öğe içermelidir.`
    );
  }

  return value;
}

/**
 * String length validation helper
 */
export function validateStringLength(
  value: string,
  fieldName: string,
  options: { min?: number; max?: number } = {}
): string {
  if (typeof value !== 'string') {
    throw createValidationError(`${fieldName} metin olmalıdır.`);
  }

  if (options.min !== undefined && value.length < options.min) {
    throw createValidationError(
      `${fieldName} en az ${options.min} karakter olmalıdır.`
    );
  }

  if (options.max !== undefined && value.length > options.max) {
    throw createValidationError(
      `${fieldName} en fazla ${options.max} karakter olmalıdır.`
    );
  }

  return value;
}

/**
 * Password validation helper
 */
export function validatePassword(password: string): string {
  if (!password || typeof password !== 'string') {
    throw createValidationError('Şifre gereklidir.');
  }

  if (password.length < 6) {
    throw createValidationError('Şifre en az 6 karakter olmalıdır.');
  }

  return password;
}

/**
 * FormData validation helper for auth forms
 */
export function validateAuthFormData(formData: FormData): {
  email: string;
  password: string;
} {
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;

  if (!email || !password) {
    throw createValidationError('E-posta ve şifre gereklidir.');
  }

  validateEmail(email);
  validatePassword(password);

  return { email, password };
}
