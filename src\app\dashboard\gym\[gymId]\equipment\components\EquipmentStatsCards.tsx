'use client';

import { <PERSON><PERSON>, CheckCircle, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { EquipmentAnalytics } from '@/types/database/equipment-inventory';

interface EquipmentStatsCardsProps {
  analytics: EquipmentAnalytics;
}

export function EquipmentStatsCards({ analytics }: EquipmentStatsCardsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Equipment */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Toplam Ekipman</CardTitle>
          <Wrench className="text-muted-foreground h-4 w-4" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{analytics.total_equipment}</div>
          <div className="mt-2 flex gap-2">
            <Badge variant="secondary" className="text-xs">
              <CheckCircle className="mr-1 h-3 w-3" />
              {analytics.active_equipment} Aktif
            </Badge>
            {analytics.maintenance_equipment > 0 && (
              <Badge variant="outline" className="text-xs text-yellow-600">
                <AlertTriangle className="mr-1 h-3 w-3" />
                {analytics.maintenance_equipment} Bakımda
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
