'use client';

import { useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, TrendingUp, DollarSign, Calendar } from 'lucide-react';
import { Staff } from '@/types/staff';
import { formatCurrency } from '@/lib/utils';

interface StaffStatsData {
  totalStaff: number;
  totalSalary: number;
  averageSalary: number;
  recentHires: number;
}

interface StaffStatsProps {
  staffs: Staff[];
}

export function StaffStats({ staffs }: StaffStatsProps) {
  const stats = useMemo<StaffStatsData>(() => {
    // Calculate stats
    const totalStaff = staffs.length;
    const totalSalary = staffs.reduce(
      (sum, member) => sum + (member.salary_amount || 0),
      0
    );
    const averageSalary = totalStaff > 0 ? totalSalary / totalStaff : 0;

    // Recent hires (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentHires = staffs.filter(
      member => new Date(member.hire_date) >= thirtyDaysAgo
    ).length;

    return {
      totalStaff,
      totalSalary,
      averageSalary,
      recentHires,
    };
  }, [staffs]);

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Toplam Personel</CardTitle>
          <Users className="text-muted-foreground h-4 w-4" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalStaff}</div>
          <p className="text-muted-foreground text-xs">Aktif personel sayısı</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Toplam Maaş</CardTitle>
          <DollarSign className="text-muted-foreground h-4 w-4" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {formatCurrency(stats.totalSalary)}
          </div>
          <p className="text-muted-foreground text-xs">Aylık toplam maliyet</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Ortalama Maaş</CardTitle>
          <TrendingUp className="text-muted-foreground h-4 w-4" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {formatCurrency(stats.averageSalary)}
          </div>
          <p className="text-muted-foreground text-xs">
            Personel başına ortalama
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Yeni İşe Alınanlar
          </CardTitle>
          <Calendar className="text-muted-foreground h-4 w-4" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.recentHires}</div>
          <p className="text-muted-foreground text-xs">Son 30 günde</p>
        </CardContent>
      </Card>
    </div>
  );
}
