'use client';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { IconDefinition, SizeProp } from '@fortawesome/fontawesome-svg-core';
import { cn } from '@/lib/utils';

interface FontAwesomeIconProps {
  icon: IconDefinition;
  className?: string;
  size?: SizeProp;
  spin?: boolean;
  pulse?: boolean;
  bounce?: boolean;
  fade?: boolean;
  beat?: boolean;
  shake?: boolean;
  flip?: 'horizontal' | 'vertical' | 'both';
  rotation?: 90 | 180 | 270;
  fixedWidth?: boolean;
  inverse?: boolean;
  border?: boolean;
  pull?: 'left' | 'right';
  style?: React.CSSProperties;
}

/**
 * FontAwesome Icon Component
 *
 * Bu component FontAwesome ikonlarını kullanmak için optimize edilmiş bir wrapper'dır.
 * Tailwind CSS sınıfları ile uyumlu çalışır ve ek animasyon özellikleri sunar.
 *
 * @example
 * ```tsx
 * import { faHome } from '@fortawesome/free-solid-svg-icons';
 * import { FontAwesomeIconComponent } from '@/components/ui/fontawesome-icon';
 *
 * <FontAwesomeIconComponent
 *   icon={faHome}
 *   className="text-blue-500 w-6 h-6"
 *   spin
 * />
 * ```
 */
export function FontAwesomeIconComponent({
  icon,
  className,
  size,
  spin = false,
  pulse = false,
  bounce = false,
  fade = false,
  beat = false,
  shake = false,
  flip,
  rotation,
  fixedWidth = false,
  inverse = false,
  border = false,
  pull,
  style,
  ...props
}: FontAwesomeIconProps) {
  return (
    <FontAwesomeIcon
      icon={icon}
      className={cn(className)}
      size={size}
      spin={spin}
      pulse={pulse}
      bounce={bounce}
      fade={fade}
      beat={beat}
      shake={shake}
      flip={flip}
      rotation={rotation}
      fixedWidth={fixedWidth}
      inverse={inverse}
      border={border}
      pull={pull}
      style={style}
      {...props}
    />
  );
}

// Yaygın kullanılan ikon boyutları için yardımcı sınıflar
export const iconSizes = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4',
  md: 'w-5 h-5',
  lg: 'w-6 h-6',
  xl: 'w-8 h-8',
  '2xl': 'w-10 h-10',
  '3xl': 'w-12 h-12',
  '4xl': 'w-16 h-16',
  '5xl': 'w-20 h-20',
} as const;

// Yaygın kullanılan ikon renkleri
export const iconColors = {
  primary: 'text-primary',
  secondary: 'text-secondary',
  success: 'text-success',
  warning: 'text-warning',
  danger: 'text-error',
  error: 'text-error',
  info: 'text-info',
  muted: 'text-muted-foreground',
  white: 'text-white',
  black: 'text-black',
} as const;

// Hızlı kullanım için preset kombinasyonlar
export const iconPresets = {
  button: 'w-4 h-4',
  nav: 'w-5 h-5',
  header: 'w-6 h-6',
  hero: 'w-8 h-8',
  feature: 'w-12 h-12',
  loading: 'w-4 h-4 animate-spin',
  success: 'w-5 h-5 text-success',
  error: 'w-5 h-5 text-error',
  warning: 'w-5 h-5 text-warning',
  info: 'w-5 h-5 text-info',
} as const;

export default FontAwesomeIconComponent;
