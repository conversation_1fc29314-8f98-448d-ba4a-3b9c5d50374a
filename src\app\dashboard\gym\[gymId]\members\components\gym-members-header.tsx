'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { UserPlus, Users, FileSpreadsheet } from 'lucide-react';
import Link from 'next/link';
import { InviteMemberDialog } from './invite-member-dialog';

interface GymMembersHeaderProps {
  gymId: string;
}

export function GymMembersHeader({ gymId }: GymMembersHeaderProps) {
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);

  const handleMemberInvited = () => {
    // Optionally refresh the page or update the members list
    // For now, we'll just show a success message via toast (already handled in dialog)
  };

  return (
    <>
      <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-2">
        <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
          <Button asChild className="w-full sm:w-auto">
            <Link href={`/dashboard/gym/${gymId}/members/addmember`}>
              <UserPlus className="mr-2 h-4 w-4" />
              Üye Ekle
            </Link>
          </Button>

          <Button asChild variant="secondary" className="w-full sm:w-auto">
            <Link href={`/dashboard/gym/${gymId}/members/import`}>
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              Toplu İçe Aktar
            </Link>
          </Button>

          <Button
            variant="outline"
            className="w-full sm:w-auto"
            onClick={() => setIsInviteDialogOpen(true)}
          >
            <Users className="mr-2 h-4 w-4" />
            Üye Davet Et
          </Button>
        </div>
      </div>

      <InviteMemberDialog
        isOpen={isInviteDialogOpen}
        onClose={() => setIsInviteDialogOpen(false)}
        gymId={gymId}
        onMemberInvited={handleMemberInvited}
      />
    </>
  );
}
