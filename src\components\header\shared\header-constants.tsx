import { RoleOption } from './header-types';
import { Crown, Dumbbell, User } from 'lucide-react';
import { getDashboardPath } from '@/lib/actions/auth';

// 🎯 Navigation Constants
export const NAV_LINKS = {
  login: '/auth/login',
  register: '/auth/register',
  dashboard: '/dashboard',
  profile: '/profile/settings',
} as const;

// 🏷️ Role Options Constants
export const ROLE_OPTIONS: Record<string, Omit<RoleOption, 'available'>> = {
  member: {
    role: 'member',
    label: 'Üye',
    href: '/dashboard/member',
    icon: <User className="h-4 w-4" />,
  },
  trainer: {
    role: 'trainer',
    label: 'Antrenör',
    href: '/dashboard/trainer',
    icon: <Dumbbell className="h-4 w-4" />,
  },
  manager: {
    role: 'manager',
    label: 'Yönetici',
    href: '/dashboard/manager',
    icon: <Crown className="h-4 w-4" />,
  },
};

// 📊 Dashboard Specific Constants
export const DASHBOARD_CONFIG = {
  pathPrefix: '/dashboard',
  roles: {
    member: 'member',
    trainer: 'trainer',
    manager: 'manager',
  },
  gymPathPattern: /\/gym\/([^\/]+)/,
} as const;

// 🔧 Utility Functions
export const createRoleOptions = (userRoles: string[]): RoleOption[] => {
  return userRoles
    .filter(role => role in ROLE_OPTIONS)
    .map(role => ROLE_OPTIONS[role]);
};

// Dinamik yönetici href'i oluştur
export const createDynamicRoleOptions = async (userRoles: string[]): Promise<RoleOption[]> => {
  const options = userRoles
    .filter(role => role in ROLE_OPTIONS)
    .map(role => ({ ...ROLE_OPTIONS[role] }));

  // Manager rolü varsa, dinamik href belirle
  const managerOption = options.find(option => option.role === 'manager');
  if (managerOption) {
    try {
      const dashboardPath = await getDashboardPath();
      // Eğer gym-manager path'i dönerse, manager rolünü güncelle
      if (dashboardPath === '/dashboard/gym-manager') {
        managerOption.href = '/dashboard/gym-manager';
        managerOption.label = 'Salon Yöneticisi';
      }
    } catch (error) {
      console.error('Dashboard path alınırken hata:', error);
      // Hata durumunda varsayılan değeri koru
    }
  }

  return options;
};

export const extractCurrentRole = (pathname: string): string => {
  const segments = pathname.split('/');
  const dashboardIndex = segments.indexOf('dashboard');
  return dashboardIndex !== -1 && segments[dashboardIndex + 1]
    ? segments[dashboardIndex + 1]
    : '';
};

export const extractCurrentGymId = (pathname: string): string | null => {
  const segments = pathname.split('/');
  const gymIndex = segments.indexOf('gym');
  return gymIndex !== -1 && segments[gymIndex + 1]
    ? segments[gymIndex + 1]
    : null;
};
