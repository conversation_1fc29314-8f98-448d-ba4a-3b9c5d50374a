'use client';

import { cn } from '@/lib/utils';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  User,
  Activity,
  Award,
  Crown,
  Palette,
  Shield,
  Settings,
  Menu,
  X,
  ArrowLeft,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { useState } from 'react';

interface SettingsSidebarProps {
  userRoles: string[];
}

interface NavigationItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  requiredRole?: string;
}

export function SettingsSidebar({ userRoles }: SettingsSidebarProps) {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const isMember = userRoles.includes('member');
  const isTrainer = userRoles.includes('trainer');
  const isManager = userRoles.includes('manager');

  const navigationItems: NavigationItem[] = [
    {
      href: '/profile/settings',
      label: 'Profil',
      icon: User,
      description: 'Temel profil bilgileri',
    },
    {
      href: '/profile/settings/physical',
      label: 'Fiziksel',
      icon: Activity,
      description: 'Fiziksel özellikler ve hedefler',
      requiredRole: 'member',
    },
    {
      href: '/profile/settings/trainer',
      label: 'Antrenör',
      icon: Award,
      description: 'Uzmanlık ve sertifikalar',
      requiredRole: 'trainer',
    },
    {
      href: '/profile/settings/manager',
      label: 'Yönetici',
      icon: Crown,
      description: 'Yönetici ayarları ve salon yönetimi',
      requiredRole: 'manager',
    },
    {
      href: '/profile/settings/preferences',
      label: 'Tercihler',
      icon: Palette,
      description: 'Tema, bildirim ve gizlilik',
    },
    {
      href: '/profile/settings/security',
      label: 'Güvenlik',
      icon: Shield,
      description: 'Şifre ve hesap güvenliği',
    },
    {
      href: '/profile/settings/roles',
      label: 'Rol Ayarları',
      icon: Settings,
      description: 'Rol yönetimi ve izinler',
    },
  ];

  // Kullanıcının rolüne göre navigation item'ları filtrele
  const filteredItems = navigationItems.filter(item => {
    if (!item.requiredRole) return true;

    switch (item.requiredRole) {
      case 'member':
        return isMember;
      case 'trainer':
        return isTrainer;
      case 'manager':
        return isManager;
      default:
        return true;
    }
  });

  const NavigationContent = ({ showPanelButton = false }) => (
    <div className="space-y-4">
      {/* Panel'e Dön butonu - sadece mobile'da göster */}
      {showPanelButton && (
        <div className="border-b pb-4">
          <Link
            href="/dashboard"
            onClick={() => setIsMobileMenuOpen(false)}
            className="hover:bg-accent text-muted-foreground hover:text-foreground flex items-center gap-3 rounded-lg px-3 py-3 text-sm transition-all"
          >
            <ArrowLeft className="h-4 w-4 flex-shrink-0" />
            <div className="font-medium">Panel&apos;e Dön</div>
          </Link>
        </div>
      )}

      {/* Navigation Items */}
      <nav className="space-y-2 overflow-y-auto">
        {filteredItems.map(item => {
          const Icon = item.icon;
          const isActive = pathname === item.href;

          return (
            <Link
              key={item.href}
              href={item.href}
              onClick={() => setIsMobileMenuOpen(false)}
              className={cn(
                'hover:bg-accent flex items-start gap-3 rounded-lg px-3 py-3 text-sm transition-all',
                isActive
                  ? 'bg-accent text-accent-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground'
              )}
            >
              <Icon className="mt-0.5 h-4 w-4 flex-shrink-0" />
              <div className="leading-none font-medium">{item.label}</div>
            </Link>
          );
        })}
      </nav>
    </div>
  );

  return (
    <>
      {/* Mobile Menu Button - Positioned in header area */}
      <div className="lg:hidden">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="bg-background fixed top-4 left-4 z-50 border shadow-sm"
        >
          {isMobileMenuOpen ? (
            <X className="h-4 w-4" />
          ) : (
            <Menu className="h-4 w-4" />
          )}
          <span className="sr-only">Toggle navigation menu</span>
        </Button>
      </div>

      {/* Mobile Sidebar Overlay */}
      {isMobileMenuOpen && (
        <div
          className="bg-background/80 fixed inset-0 z-50 backdrop-blur-sm lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          <div
            className="bg-background fixed top-0 left-0 flex h-full w-72 flex-col border-r shadow-lg sm:w-80"
            onClick={e => e.stopPropagation()}
          >
            <div className="flex-1 overflow-y-auto p-4 sm:p-6">
              <NavigationContent showPanelButton={true} />
            </div>
          </div>
        </div>
      )}

      {/* Desktop Sidebar */}
      <div className="bg-background hidden w-56 flex-col border-r lg:flex">
        <div className="flex-1 overflow-auto p-6">
          <NavigationContent showPanelButton={false} />
        </div>
      </div>
    </>
  );
}
