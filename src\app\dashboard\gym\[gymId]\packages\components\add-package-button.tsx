'use client';

import { GymPackageForm } from '@/app/dashboard/manager/components/gym-package-form';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface AddPackageButtonProps {
  gymId: string;
  variant?:
    | 'default'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link'
    | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  children?: React.ReactNode;
}

export function AddPackageButton({
  gymId,
  variant = 'default',
  size = 'default',
  className,
  children,
}: AddPackageButtonProps) {
  return (
    <GymPackageForm
      gymId={gymId}
      trigger={
        <Button variant={variant} size={size} className={className}>
          <Plus className="mr-2 h-4 w-4" />
          {children || 'Yeni Paket Ekle'}
        </Button>
      }
    />
  );
}
