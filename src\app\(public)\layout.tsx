import type { ReactNode } from 'react';
import { AppHeader } from '@/components/header/app-header';
import Footer from '@/components/public/home/<USER>';

/**
 * Public Layout
 * - Ana sayfa, pricing gibi public sayfalar için
 * - PublicHeader ve MainFooter içerir
 */
export default function PublicLayout({ children }: { children: ReactNode }) {
  return (
    <>
      <AppHeader />
      <main
        id="main-content"
        className="relative flex min-h-[calc(100vh-10rem)] w-full flex-col"
      >
        {/* BG layers */}
        <div className="pointer-events-none absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-[radial-gradient(55%_55%_at_20%_0%,hsl(var(--primary)/0.10),transparent_60%)]" />
          <div className="absolute inset-0 bg-[linear-gradient(to_right,rgba(255,255,255,0.05)_1px,transparent_1px),linear-gradient(to_bottom,rgba(255,255,255,0.05)_1px,transparent_1px)] [background-size:44px_44px] mix-blend-overlay" />
        </div>
        {children}
      </main>
      <Footer />
    </>
  );
}

