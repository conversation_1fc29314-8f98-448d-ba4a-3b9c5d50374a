/**
 * Equipment Management Page
 *
 * Main page for gym equipment management with listing, filtering, and analytics
 */

import { Suspense } from 'react';
import { Metadata } from 'next';
import {
  getGymEquipment,
  getEquipmentCategories,
} from '@/lib/actions/dashboard/company/equipment-actions';
import { EquipmentDashboard } from './components/EquipmentDashboard';
import { EquipmentListSkeleton } from './components/EquipmentListSkeleton';

export const metadata: Metadata = {
  title: 'Ekipmanlar | Sportiva',
  description: 'Spor salonu ekipmanlar ve bakım takibi.',
};

interface EquipmentPageProps {
  params: Promise<{ gymId: string }>;
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function EquipmentPage({
  params,
  searchParams,
}: EquipmentPageProps) {
  const { gymId } = await params;
  const searchParamsResolved = await searchParams;

  // Parse filters from search params
  const filters = {
    category_id:
      typeof searchParamsResolved?.category === 'string'
        ? searchParamsResolved.category
        : undefined,
    condition:
      typeof searchParamsResolved?.condition === 'string'
        ? (searchParamsResolved.condition as any)
        : undefined,
    status:
      typeof searchParamsResolved?.status === 'string'
        ? (searchParamsResolved.status as any)
        : undefined,
    location:
      typeof searchParamsResolved?.location === 'string'
        ? searchParamsResolved.location
        : undefined,
    search:
      typeof searchParamsResolved?.search === 'string'
        ? searchParamsResolved.search
        : undefined,
  };

  return (
    <div className="space-y-6">
      <Suspense fallback={<EquipmentListSkeleton />}>
        <EquipmentPageContent gymId={gymId} filters={filters} />
      </Suspense>
    </div>
  );
}

async function EquipmentPageContent({
  gymId,
  filters,
}: {
  gymId: string;
  filters: any;
}) {
  // Fetch data in parallel
  const [equipmentResponse, categoriesResponse] = await Promise.all([
    getGymEquipment(gymId, filters),
    getEquipmentCategories(),
  ]);

  if (!equipmentResponse.success) {
    throw new Error(equipmentResponse.error || 'Ekipman listesi alınamadı');
  }

  if (!categoriesResponse.success) {
    throw new Error(categoriesResponse.error || 'Kategoriler alınamadı');
  }

  return (
    <EquipmentDashboard
      gymId={gymId}
      equipmentData={equipmentResponse.data!}
      categories={categoriesResponse.data!}
      initialFilters={filters}
    />
  );
}
