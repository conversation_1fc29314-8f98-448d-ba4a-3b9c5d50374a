'use client';

import { Button } from '@/components/ui/button';
import { Check, X } from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { useState, useTransition } from 'react';
import {
  acceptInvitation,
  rejectInvitation,
} from '@/lib/actions/gym_invitations/invitation-actions';

interface AcceptRejectButtonsProps {
  invitationId: string;
  status: string;
}

export default function AcceptRejectButtons({
  invitationId,
  status,
}: AcceptRejectButtonsProps) {
  const router = useRouter();
  const [isPendingTransition, startTransition] = useTransition();
  const [loading, setLoading] = useState<null | 'accept' | 'reject'>(null);

  if (status !== 'pending') return null;

  const onAccept = async () => {
    setLoading('accept');
    try {
      const res = await acceptInvitation(invitationId);
      if (res.success) {
        toast.success(res.message ?? 'Davet kabul edildi');
        startTransition(() => router.refresh());
      } else {
        toast.error(res.error ?? 'Davet kabul edilirken bir hata oluştu');
      }
    } catch (e) {
      toast.error('Davet kabul edilirken bir hata oluştu');
    } finally {
      setLoading(null);
    }
  };

  const onReject = async () => {
    setLoading('reject');
    try {
      const res = await rejectInvitation(invitationId);
      if (res.success) {
        toast.success(res.message ?? 'Davet reddedildi');
        startTransition(() => router.refresh());
      } else {
        toast.error(res.error ?? 'Davet reddedilirken bir hata oluştu');
      }
    } catch (e) {
      toast.error('Davet reddedilirken bir hata oluştu');
    } finally {
      setLoading(null);
    }
  };

  const disabled = !!loading || isPendingTransition;

  return (
    <div className="flex justify-end gap-3">
      <Button
        variant="outline"
        onClick={onReject}
        disabled={disabled}
        className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
      >
        <X className="mr-2 h-4 w-4" />
        Reddet
      </Button>
      <Button onClick={onAccept} disabled={disabled}>
        <Check className="mr-2 h-4 w-4" />
        Kabul Et
      </Button>
    </div>
  );
}
