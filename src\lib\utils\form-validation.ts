/**
 * Client-side form validation utilities
 * Provides real-time input validation and formatting
 */

/**
 * Validates and formats phone number input
 * Allows only digits and limits to 11 characters for Turkish phone numbers
 */
export const formatPhoneNumber = (value: string): string => {
  // Remove all non-digit characters
  const digitsOnly = value.replace(/\D/g, '');

  // Limit to 11 digits (Turkish phone number format)
  return digitsOnly.slice(0, 11);
};

/**
 * Validates phone number format
 */
export const isValidPhoneNumber = (phone: string): boolean => {
  const digitsOnly = phone.replace(/\D/g, '');
  return digitsOnly.length >= 10 && digitsOnly.length <= 11;
};

/**
 * Formats phone number for display with proper spacing
 */
export const displayPhoneNumber = (phone: string): string => {
  const digitsOnly = phone.replace(/\D/g, '');

  if (digitsOnly.length <= 3) return digitsOnly;
  if (digitsOnly.length <= 6)
    return `${digitsOnly.slice(0, 3)} ${digitsOnly.slice(3)}`;
  if (digitsOnly.length <= 8)
    return `${digitsOnly.slice(0, 3)} ${digitsOnly.slice(3, 6)} ${digitsOnly.slice(6)}`;
  if (digitsOnly.length <= 10)
    return `${digitsOnly.slice(0, 3)} ${digitsOnly.slice(3, 6)} ${digitsOnly.slice(6, 8)} ${digitsOnly.slice(8)}`;

  return `${digitsOnly.slice(0, 3)} ${digitsOnly.slice(3, 6)} ${digitsOnly.slice(6, 8)} ${digitsOnly.slice(8, 10)} ${digitsOnly.slice(10)}`;
};

/**
 * Validates and formats name input
 * Allows only letters, spaces, and Turkish characters
 */
export const formatName = (value: string): string => {
  // Allow letters (including Turkish), spaces, apostrophes, and hyphens
  return value.replace(/[^a-zA-ZçğıöşüÇĞIİÖŞÜ\s'-]/g, '').slice(0, 50);
};

/**
 * Validates name format
 */
export const isValidName = (name: string): boolean => {
  const trimmed = name.trim();
  return (
    trimmed.length >= 2 &&
    trimmed.length <= 50 &&
    /^[a-zA-ZçğıöşüÇĞIİÖŞÜ\s'-]+$/.test(trimmed)
  );
};

/**
 * Validates and formats numeric input
 */
export const formatNumericInput = (
  value: string,
  min: number = 0,
  max: number = 999
): string => {
  // Remove non-digit characters
  const digitsOnly = value.replace(/\D/g, '');

  if (digitsOnly === '') return '';

  const numValue = parseInt(digitsOnly, 10);

  // Clamp between min and max
  if (numValue < min) return min.toString();
  if (numValue > max) return max.toString();

  return numValue.toString();
};

/**
 * Validates email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

/**
 * Formats company name input
 * Allows letters, numbers, spaces, and common business characters
 */
export const formatCompanyName = (value: string): string => {
  // Allow letters, numbers, spaces, and common business characters
  return value.replace(/[^a-zA-Z0-9çğıöşüÇĞIİÖŞÜ\s&.-]/g, '').slice(0, 100);
};

/**
 * Validates company name
 */
export const isValidCompanyName = (name: string): boolean => {
  const trimmed = name.trim();
  return trimmed.length >= 2 && trimmed.length <= 100;
};

/**
 * Formats textarea input with character limit
 */
export const formatTextarea = (
  value: string,
  maxLength: number = 500
): string => {
  return value.slice(0, maxLength);
};

/**
 * Real-time validation states
 */
export type ValidationState = 'idle' | 'valid' | 'invalid' | 'warning';

/**
 * Gets validation state for a field
 */
export const getValidationState = (
  value: string,
  validator: (value: string) => boolean,
  required: boolean = false
): ValidationState => {
  if (!value.trim()) {
    return required ? 'invalid' : 'idle';
  }

  return validator(value) ? 'valid' : 'invalid';
};

/**
 * Validates URL format
 */
export const isValidUrl = (url: string): boolean => {
  if (!url.trim()) return true; // Empty URLs are valid (optional)

  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    return false;
  }
};

/**
 * Validates social media URL format
 */
export const isValidSocialUrl = (url: string, platform: string): boolean => {
  if (!url.trim()) return true; // Empty URLs are valid (optional)

  if (!isValidUrl(url)) return false;

  const lowerUrl = url.toLowerCase();

  switch (platform) {
    case 'website':
      return true; // Any valid URL is acceptable for website
    case 'instagram':
      return lowerUrl.includes('instagram.com/');
    case 'facebook':
      return lowerUrl.includes('facebook.com/') || lowerUrl.includes('fb.com/');
    case 'x':
      return lowerUrl.includes('x.com/');
    case 'youtube':
      return (
        lowerUrl.includes('youtube.com/') || lowerUrl.includes('youtu.be/')
      );
    case 'tiktok':
      return lowerUrl.includes('tiktok.com/');
    default:
      return isValidUrl(url);
  }
};

/**
 * Formats URL by adding https:// if missing
 */
export const formatUrl = (url: string): string => {
  if (!url.trim()) return '';

  const trimmed = url.trim();

  // If it already has a protocol, return as is
  if (trimmed.startsWith('http://') || trimmed.startsWith('https://')) {
    return trimmed;
  }

  // Add https:// prefix
  return `https://${trimmed}`;
};

/**
 * Input validation rules for different field types
 */
export const INPUT_RULES = {
  PHONE: {
    maxLength: 11,
    pattern: /^\d{10,11}$/,
    formatter: formatPhoneNumber,
    validator: isValidPhoneNumber,
  },
  NAME: {
    maxLength: 50,
    pattern: /^[a-zA-ZçğıöşüÇĞIİÖŞÜ\s'-]+$/,
    formatter: formatName,
    validator: isValidName,
  },
  EMAIL: {
    maxLength: 100,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    validator: isValidEmail,
  },
  COMPANY_NAME: {
    maxLength: 100,
    pattern: /^[a-zA-Z0-9çğıöşüÇĞIİÖŞÜ\s&.-]+$/,
    formatter: formatCompanyName,
    validator: isValidCompanyName,
  },
  AGE: {
    min: 13,
    max: 100,
    formatter: (value: string) => formatNumericInput(value, 13, 100),
  },
  HEIGHT: {
    min: 100,
    max: 250,
    formatter: (value: string) => formatNumericInput(value, 100, 250),
  },
  WEIGHT: {
    min: 30,
    max: 300,
    formatter: (value: string) => formatNumericInput(value, 30, 300),
  },
  EXPERIENCE: {
    min: 0,
    max: 50,
    formatter: (value: string) => formatNumericInput(value, 0, 50),
  },
  BIO: {
    maxLength: 500,
    formatter: (value: string) => formatTextarea(value, 500),
  },
  URL: {
    maxLength: 255,
    formatter: formatUrl,
    validator: isValidUrl,
  },
} as const;

/**
 * Social media platform configurations
 */
export const SOCIAL_PLATFORMS = {
  website: {
    label: 'Website',
    placeholder: 'https://sirketiniz.com',
    icon: 'Website',
    validator: (url: string) => isValidSocialUrl(url, 'website'),
  },
  instagram: {
    label: 'Instagram',
    placeholder: 'https://instagram.com/kullaniciadi',
    icon: 'Instagram',
    validator: (url: string) => isValidSocialUrl(url, 'instagram'),
  },
  facebook: {
    label: 'Facebook',
    placeholder: 'https://facebook.com/sayfaadi',
    icon: 'Facebook',
    validator: (url: string) => isValidSocialUrl(url, 'facebook'),
  },
  x: {
    label: 'Twitter/X',
    placeholder: 'https://x.com/kullaniciadi',
    icon: 'X',
    validator: (url: string) => isValidSocialUrl(url, 'x'),
  },
  youtube: {
    label: 'YouTube',
    placeholder: 'https://youtube.com/channel/kanalid',
    icon: 'Youtube',
    validator: (url: string) => isValidSocialUrl(url, 'youtube'),
  },
  tiktok: {
    label: 'TikTok',
    placeholder: 'https://tiktok.com/@kullaniciadi',
    icon: 'Tiktok',
    validator: (url: string) => isValidSocialUrl(url, 'tiktok'),
  },
} as const;

export type SocialPlatform = keyof typeof SOCIAL_PLATFORMS;
