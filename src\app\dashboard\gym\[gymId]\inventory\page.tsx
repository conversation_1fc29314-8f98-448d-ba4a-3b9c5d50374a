import { Suspense } from 'react';
import { Metadata } from 'next';
import {
  getGymInventory,
  getInventoryCategories,
} from '@/lib/actions/dashboard/company/inventory-actions';
import { InventoryDashboard } from './components/InventoryDashboard';
import { InventoryListSkeleton } from './components/InventoryListSkeleton';

export const metadata: Metadata = {
  title: 'Depo | Sportiva',
  description: 'Spor salonu depo ve stok takibi.',
};

interface InventoryPageProps {
  params: Promise<{ gymId: string }>;
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function InventoryPage({
  params,
  searchParams,
}: InventoryPageProps) {
  const { gymId } = await params;
  const searchParamsResolved = await searchParams;

  // Parse filters from search params
  const filters = {
    category_id:
      typeof searchParamsResolved?.category === 'string'
        ? searchParamsResolved.category
        : undefined,
    status:
      typeof searchParamsResolved?.status === 'string'
        ? (searchParamsResolved.status as any)
        : undefined,
    unit_type:
      typeof searchParamsResolved?.unit_type === 'string'
        ? (searchParamsResolved.unit_type as any)
        : undefined,
    low_stock: searchParamsResolved?.low_stock === 'true',
    expired: searchParamsResolved?.expired === 'true',
    search:
      typeof searchParamsResolved?.search === 'string'
        ? searchParamsResolved.search
        : undefined,
  };

  return (
    <div className="space-y-6">
      <Suspense fallback={<InventoryListSkeleton />}>
        <InventoryPageContent gymId={gymId} filters={filters} />
      </Suspense>
    </div>
  );
}

async function InventoryPageContent({
  gymId,
  filters,
}: {
  gymId: string;
  filters: any;
}) {
  // Fetch data in parallel
  const [inventoryResponse, categoriesResponse] = await Promise.all([
    getGymInventory(gymId, filters),
    getInventoryCategories(),
  ]);

  if (!inventoryResponse.success) {
    throw new Error(inventoryResponse.error || 'Depo listesi alınamadı');
  }

  if (!categoriesResponse.success) {
    throw new Error(categoriesResponse.error || 'Kategoriler alınamadı');
  }

  return (
    <InventoryDashboard
      gymId={gymId}
      inventoryData={inventoryResponse.data!}
      categories={categoriesResponse.data!}
      initialFilters={filters}
    />
  );
}
