export default function Loading() {
  return (
    <div className="space-y-10">
      {/* Header Skeleton */}
      <section>
        <h1 className="text-3xl font-bold tracking-tight">
          Hoş Geldiniz,{' '}
          <span className="bg-muted inline-block h-8 w-48 animate-pulse rounded" />
        </h1>
        <p className="text-muted-foreground mt-1">
          Üyeliklerinizi ve fitness yolculuğunuzu takip edin
        </p>
      </section>

      {/* Stats Section Skeleton */}
      <section>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Toplam Üyelik */}
          <div className="bg-card rounded-lg border">
            <div className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
              <div className="bg-muted h-4 w-20 animate-pulse rounded" />
              <div className="bg-muted h-4 w-4 animate-pulse rounded" />
            </div>
            <div className="px-8 pb-8">
              <div className="bg-muted h-8 w-8 animate-pulse rounded" />
            </div>
          </div>

          {/* Aktif Ü<PERSON>lik */}
          <div className="bg-card rounded-lg border">
            <div className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
              <div className="bg-muted h-4 w-20 animate-pulse rounded" />
              <div className="bg-muted h-4 w-4 animate-pulse rounded" />
            </div>
            <div className="px-8 pb-8">
              <div className="bg-muted h-8 w-8 animate-pulse rounded" />
            </div>
          </div>

          {/* Pasif Üyelik */}
          <div className="bg-card rounded-lg border">
            <div className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
              <div className="bg-muted h-4 w-20 animate-pulse rounded" />
              <div className="bg-muted h-4 w-4 animate-pulse rounded" />
            </div>
            <div className="px-8 pb-8">
              <div className="bg-muted h-8 w-8 animate-pulse rounded" />
            </div>
          </div>

          {/* Toplam Harcama */}
          <div className="bg-card rounded-lg border">
            <div className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
              <div className="bg-muted h-4 w-24 animate-pulse rounded" />
              <div className="bg-muted h-4 w-4 animate-pulse rounded" />
            </div>
            <div className="px-8 pb-8">
              <div className="bg-muted h-8 w-8 animate-pulse rounded" />
            </div>
          </div>
        </div>
      </section>

      {/* Memberships Section Skeleton */}
      <section>
        <div className="bg-card rounded-lg border">
          {/* Card Header */}
          <div className="p-6 pb-4">
            <div className="flex items-center gap-2">
              <div className="bg-muted h-5 w-5 animate-pulse rounded" />
              <div className="bg-muted h-6 w-20 animate-pulse rounded" />
            </div>
            <div className="bg-muted mt-2 h-4 w-56 animate-pulse rounded" />
          </div>

          {/* Empty State Skeleton - Henüz üyeliğiniz yok */}
          <div className="px-6 pb-6">
            <div className="py-8 text-center">
              <div className="bg-muted mx-auto h-12 w-12 animate-pulse rounded" />
              <div className="bg-muted mx-auto mt-4 h-6 w-40 animate-pulse rounded" />
              <div className="bg-muted mx-auto mt-2 h-4 w-64 animate-pulse rounded" />
              <div className="bg-muted mx-auto mt-4 h-10 w-24 animate-pulse rounded" />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
