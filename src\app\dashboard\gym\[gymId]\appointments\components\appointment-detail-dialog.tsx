'use client';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import {
  updateAppointmentStatus as trainerUpdateStatus,
  updateAppointmentParticipantStatus as trainerUpdateParticipantStatus,
} from '@/lib/actions/appointments/appointment-actions';
import { updateAppointmentStatus as managerUpdateStatus } from '@/lib/actions/dashboard/company/appointment-actions';
import { updateAppointmentParticipantStatus as managerUpdateParticipantStatus } from '@/lib/actions/dashboard/company/participant-actions';
import type { TrainerPermissions } from '@/lib/actions/dashboard/company/trainer-permissions';

interface AppointmentItem {
  id: string;
  appointment_date: string;
  status?: string | null;
  appointment_type?: string | null;
  max_participants?: number;
  trainer_profile?: { full_name?: string | null };
  participants?: Array<{
    id: string;
    status?: string | null;
    membership_package?: {
      membership?: { profile?: { full_name?: string | null } | null } | null;
      gym_package?: { name?: string | null } | null;
    } | null;
  }>;
  notes?: string | null;
}

interface AppointmentDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  appointment: AppointmentItem | null;
  participants?: Array<{
    id: string;
    status?: string | null;
    membership_package?: any;
    membership?: any;
    profile?: { full_name?: string | null };
  }>;
  mode?: 'manager' | 'trainer';
  permissions?: TrainerPermissions;
}

const statusConfig = {
  scheduled: {
    label: '📅 Planlandı',
    className:
      'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300',
  },
  completed: {
    label: '✅ Tamamlandı',
    className:
      'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300',
  },
  cancelled: {
    label: '❌ İptal edildi',
    className:
      'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300',
  },
};

const statusLabel = (status: string) => {
  return statusConfig[status as keyof typeof statusConfig]?.label || status;
};

const typeLabel = (type?: string | null) => {
  switch (type) {
    case 'personal':
      return 'Kişisel';
    case 'group':
      return 'Grup';
    default:
      return type || 'Bilinmiyor';
  }
};

export function AppointmentDetailDialog({
  open,
  onOpenChange,
  appointment,
  mode = 'manager',
  permissions,
}: AppointmentDetailDialogProps) {
  const router = useRouter();

  const canUpdate =
    mode === 'manager' || permissions?.appointments.update === true;

  const handleUpdateStatus = async (appointmentId: string, status: string) => {
    try {
      if (mode === 'manager') {
        const fd = new FormData();
        fd.append('appointment_id', appointmentId);
        fd.append('status', status);
        const res = await managerUpdateStatus(fd as any);
        if (!res.success) throw new Error(res.error || 'Durum güncellenemedi');
      } else {
        const res = await trainerUpdateStatus(appointmentId, status);
        if (!res.success) throw new Error(res.error || 'Durum güncellenemedi');
      }
      toast.success('Durum güncellendi');
      router.refresh();
      onOpenChange(false);
    } catch (e: any) {
      toast.error(e?.message || 'Durum güncellenemedi');
    }
  };

  const handleUpdateParticipantStatus = async (
    participantId: string,
    status: 'confirmed' | 'cancelled' | 'no_show' | 'completed'
  ) => {
    try {
      if (mode === 'manager') {
        const fd = new FormData();
        fd.append('appointment_participant_id', participantId);
        fd.append('status', status);
        const res = await managerUpdateParticipantStatus(fd as any);
        if (!res.success)
          throw new Error(res.error || 'Katılımcı durumu güncellenemedi');
      } else {
        const res = await trainerUpdateParticipantStatus(participantId, status);
        if (!res.success)
          throw new Error(res.error || 'Katılımcı durumu güncellenemedi');
      }
      toast.success('Katılımcı durumu güncellendi');
      router.refresh();
    } catch (e: any) {
      toast.error(e?.message || 'Katılımcı durumu güncellenemedi');
    }
  };

  if (!appointment) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Randevu Detayı</DialogTitle>
          <DialogDescription>
            {format(
              new Date(appointment.appointment_date),
              'dd MMMM yyyy, EEEE HH:mm',
              { locale: tr }
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-2 text-sm">
          <div className="flex items-center justify-between">
            <div className="text-muted-foreground">Durum</div>
            <div className="flex items-center gap-2">
              <Badge
                variant="outline"
                className={`${statusConfig[(appointment.status as keyof typeof statusConfig) || 'scheduled']?.className || 'bg-gray-50 text-gray-700'} font-medium`}
              >
                {statusLabel(appointment.status || 'scheduled')}
              </Badge>
              {canUpdate && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="sm" variant="outline">
                      Durumu Güncelle
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() =>
                        handleUpdateStatus(appointment.id, 'completed')
                      }
                    >
                      Tamamlandı
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() =>
                        handleUpdateStatus(appointment.id, 'cancelled')
                      }
                      className="text-red-600"
                    >
                      İptal Et
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="text-muted-foreground">Tür</div>
            <Badge variant="outline">
              {typeLabel(appointment.appointment_type)}
            </Badge>
          </div>

          {appointment.trainer_profile?.full_name && (
            <div className="flex items-center justify-between">
              <div className="text-muted-foreground">Antrenör</div>
              <div>{appointment.trainer_profile.full_name}</div>
            </div>
          )}

          <div>
            <div className="text-muted-foreground mb-1">Katılımcılar</div>

            {/* Paket bilgisi - eğer tüm katılımcılar aynı paketi kullanıyorsa üstte göster */}
            {(() => {
              const participants = appointment.participants || [];
              const packageNames = participants
                .map(p => p.membership_package?.gym_package?.name)
                .filter(Boolean);
              const uniquePackages = [...new Set(packageNames)];

              if (uniquePackages.length === 1 && uniquePackages[0]) {
                return (
                  <div className="text-muted-foreground mb-3 flex items-center gap-2 rounded-lg border p-2 text-sm">
                    <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                    <span className="font-medium">
                      Paket: {uniquePackages[0]}
                    </span>
                  </div>
                );
              }
              return null;
            })()}

            <div className="space-y-2">
              {(appointment.participants || []).map(p => (
                <div
                  key={p.id}
                  className="flex items-center justify-between text-sm"
                >
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{p.status || 'confirmed'}</Badge>
                    <div className="font-medium">
                      {p.membership_package?.membership?.profile?.full_name ||
                        'Üye'}
                    </div>
                    {/* Eğer farklı paketler varsa katılımcı yanında göster */}
                    {(() => {
                      const participants = appointment.participants || [];
                      const packageNames = participants
                        .map(p => p.membership_package?.gym_package?.name)
                        .filter(Boolean);
                      const uniquePackages = [...new Set(packageNames)];

                      if (
                        uniquePackages.length > 1 &&
                        p.membership_package?.gym_package?.name
                      ) {
                        return (
                          <span className="text-muted-foreground text-xs">
                            ({p.membership_package.gym_package.name})
                          </span>
                        );
                      }
                      return null;
                    })()}
                  </div>
                  {canUpdate && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button size="sm" variant="ghost">
                          Durum
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() =>
                            handleUpdateParticipantStatus(p.id, 'completed')
                          }
                        >
                          Tamamlandı
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            handleUpdateParticipantStatus(p.id, 'no_show')
                          }
                        >
                          Gelmedi
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            handleUpdateParticipantStatus(p.id, 'cancelled')
                          }
                          className="text-red-600"
                        >
                          İptal Et
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              ))}
              <div className="text-muted-foreground text-xs">
                Toplam: {appointment.participants?.length ?? 0}/
                {appointment.max_participants ?? 1}
              </div>
            </div>
          </div>

          {appointment.notes && (
            <div>
              <div className="text-muted-foreground mb-1">Not</div>
              <div className="text-sm">{appointment.notes}</div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
