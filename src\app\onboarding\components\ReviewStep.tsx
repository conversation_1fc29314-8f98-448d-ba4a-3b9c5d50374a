'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle2, ArrowLeft, ArrowRight } from 'lucide-react';
import type {
  PersonalData,
  MemberData,
  TrainerData,
  ManagerData,
} from '@/types/onboarding';

interface ReviewStepProps {
  hasFullName: boolean;
  personalData: PersonalData;
  memberData: MemberData;
  trainerData: TrainerData;
  managerData: ManagerData;
  selectedRoles: string[];
  isSubmitting: boolean;
  onBack: () => void;
  onSubmit: () => void;
}

export function ReviewStep({
  hasFullName,
  personalData,
  memberData,
  trainerData,
  managerData,
  selectedRoles,
  isSubmitting,
  onBack,
  onSubmit,
}: ReviewStepProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle2 className="text-primary h-5 w-5" />
            Bilgileri Kontrol Edin
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-sm">
          {!hasFullName && (
            <div>
              <h3 className="mb-1 font-semibold">Kişisel Bilgiler</h3>
              <p className="text-muted-foreground">
                {personalData.first_name} {personalData.last_name}
              </p>
            </div>
          )}

          {selectedRoles.includes('member') && (
            <div>
              <h3 className="mb-1 font-semibold">Üye Bilgileri</h3>
              <p className="text-muted-foreground">
                Yaş: {memberData.age || '-'} • Cinsiyet:{' '}
                {memberData.gender || '-'} • Boy:
                {memberData.height_cm || '-'}cm • Kilo:{' '}
                {memberData.weight_kg || '-'}kg • Hedef:
                {memberData.fitness_goal || '-'}
              </p>
            </div>
          )}

          {selectedRoles.includes('trainer') && (
            <div>
              <h3 className="mb-1 font-semibold">Antrenör Bilgileri</h3>
              <p className="text-muted-foreground">
                Uzmanlık: {trainerData.specialization || '-'} • Sertifika:{' '}
                {trainerData.certification_level || '-'} • Deneyim:{' '}
                {trainerData.experience_years || '-'} yıl
              </p>
              {trainerData.bio && (
                <p className="text-muted-foreground mt-1">{trainerData.bio}</p>
              )}
            </div>
          )}

          {selectedRoles.includes('company_manager') && (
            <div>
              <h3 className="mb-1 font-semibold">Şirket Bilgileri</h3>
              <p className="text-muted-foreground">
                {managerData.companyName || '-'} •{' '}
                {managerData.companyPhone || '-'} •{' '}
                {managerData.companyEmail || '-'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex flex-col gap-4 sm:flex-row sm:justify-between">
        <Button variant="outline" size="lg" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Geri Dön
        </Button>

        <Button size="lg" onClick={onSubmit} disabled={isSubmitting}>
          {isSubmitting ? 'Kaydediliyor...' : 'Onayla ve Tamamla'}
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
