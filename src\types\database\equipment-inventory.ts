/**
 * Equipment Management & Inventory System Types
 *
 * Type definitions for equipment and inventory management features
 */

// =============================================
// EQUIPMENT MANAGEMENT TYPES
// =============================================

export interface EquipmentCategory {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface GymEquipment {
  id: string;
  gym_id: string;
  category_id?: string;
  name: string;
  brand?: string;
  model?: string;
  serial_number?: string;
  purchase_date?: string;
  purchase_price?: number;
  warranty_expiry?: string;
  condition: 'excellent' | 'good' | 'fair' | 'poor' | 'out_of_order';
  status: 'active' | 'maintenance' | 'retired';
  location?: string;
  notes?: string;
  image_url?: string;
  qr_code?: string;
  created_at: string;
  updated_at: string;
  // Relations
  category?: EquipmentCategory;
}

export interface EquipmentUsageLog {
  id: string;
  equipment_id: string;
  member_id?: string;
  usage_date: string;
  usage_duration_minutes?: number;
  notes?: string;
  created_at: string;
  // Relations
  equipment?: GymEquipment;
}

// =============================================
// INVENTORY MANAGEMENT TYPES
// =============================================

export interface InventoryCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  created_at: string;
  updated_at: string;
}

export interface GymInventory {
  id: string;
  gym_id: string;
  category_id?: string;
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  unit_type: 'piece' | 'kg' | 'liter' | 'box' | 'bottle';
  current_stock: number;
  minimum_stock: number;
  maximum_stock?: number;
  unit_cost?: number;
  selling_price?: number;
  supplier_name?: string;
  supplier_contact?: string;
  expiry_date?: string;
  location?: string;
  status: 'active' | 'discontinued' | 'out_of_stock';
  image_url?: string;
  created_at: string;
  updated_at: string;
  // Relations
  category?: InventoryCategory;
  transactions?: InventoryTransaction[];
  // Computed fields
  is_low_stock?: boolean;
  is_expired?: boolean;
}

export interface InventoryTransaction {
  id: string;
  inventory_id: string;
  transaction_type:
    | 'purchase'
    | 'sale'
    | 'adjustment'
    | 'waste'
    | 'transfer'
    | 'return'
    | 'damage';
  quantity: number;
  unit_cost?: number;
  total_cost?: number;
  reference_number?: string;
  notes?: string;
  performed_by?: string;
  transaction_date: string;
  created_at: string;
  // Relations
  inventory?: GymInventory;
}

// =============================================
// FORM TYPES
// =============================================

export interface EquipmentFormData {
  name: string;
  category_id?: string;
  brand?: string;
  model?: string;
  serial_number?: string;
  purchase_date?: string;
  purchase_price?: number;
  warranty_expiry?: string;
  condition: GymEquipment['condition'];
  status: GymEquipment['status'];
  location?: string;
  notes?: string;
  image_url?: string;
}

export interface InventoryFormData {
  name: string;
  category_id?: string;
  description?: string;
  sku?: string;
  barcode?: string;
  unit_type: GymInventory['unit_type'];
  current_stock?: number;
  minimum_stock: number;
  maximum_stock?: number;
  unit_cost?: number;
  selling_price?: number;
  supplier_name?: string;
  supplier_contact?: string;
  expiry_date?: string;
  location?: string;
  image_url?: string;
}

export interface InventoryTransactionFormData {
  inventory_id: string;
  transaction_type: InventoryTransaction['transaction_type'];
  quantity: number;
  unit_cost?: number;
  reference_number?: string;
  notes?: string;
  transaction_date: string;
}

// =============================================
// ANALYTICS & DASHBOARD TYPES
// =============================================

export interface EquipmentAnalytics {
  total_equipment: number;
  active_equipment: number;
  maintenance_equipment: number;
  retired_equipment: number;
  condition_breakdown: {
    excellent: number;
    good: number;
    fair: number;
    poor: number;
    out_of_order: number;
  };
}

export interface InventoryAnalytics {
  total_items: number;
  low_stock_items: number;
  out_of_stock_items: number;
  expired_items: number;
  total_inventory_value: number;
  monthly_transactions: {
    purchases: number;
    sales: number;
    adjustments: number;
  };
  top_selling_items: (GymInventory & { sales_count: number })[];
  low_stock_alerts: GymInventory[];
  expiry_alerts: GymInventory[];
}

// =============================================
// FILTER & SEARCH TYPES
// =============================================

export interface EquipmentFilters {
  category_id?: string;
  condition?: GymEquipment['condition'];
  status?: GymEquipment['status'];
  location?: string;
  search?: string;
}

export interface InventoryFilters {
  category_id?: string;
  status?: GymInventory['status'];
  unit_type?: GymInventory['unit_type'];
  low_stock?: boolean;
  expired?: boolean;
  search?: string;
}

// =============================================
// API RESPONSE TYPES
// =============================================

export interface EquipmentListResponse {
  equipment: GymEquipment[];
  total: number;
  analytics: EquipmentAnalytics;
}

export interface InventoryListResponse {
  inventory: GymInventory[];
  total: number;
  analytics: InventoryAnalytics;
}

// =============================================
// CONSTANTS
// =============================================

export const EQUIPMENT_CONDITIONS = [
  { value: 'excellent', label: 'Mükemmel', color: 'green' },
  { value: 'good', label: 'İyi', color: 'blue' },
  { value: 'fair', label: 'Orta', color: 'yellow' },
  { value: 'poor', label: 'Kötü', color: 'orange' },
  { value: 'out_of_order', label: 'Arızalı', color: 'red' },
] as const;

export const EQUIPMENT_STATUSES = [
  { value: 'active', label: 'Aktif', color: 'green' },
  { value: 'maintenance', label: 'Bakımda', color: 'yellow' },
  { value: 'retired', label: 'Emekli', color: 'gray' },
] as const;

export const INVENTORY_UNIT_TYPES = [
  { value: 'piece', label: 'Adet' },
  { value: 'kg', label: 'Kilogram' },
  { value: 'liter', label: 'Litre' },
  { value: 'box', label: 'Kutu' },
  { value: 'bottle', label: 'Şişe' },
] as const;

export const INVENTORY_TRANSACTION_TYPES = [
  { value: 'purchase', label: 'Satın Alma' },
  { value: 'sale', label: 'Satış' },
  { value: 'adjustment', label: 'Düzeltme' },
  { value: 'return', label: 'İade' },
  { value: 'damage', label: 'Hasar' },
] as const;

export const TRANSACTION_TYPES = [
  { value: 'purchase', label: 'Satın Alma', color: 'green' },
  { value: 'sale', label: 'Satış', color: 'blue' },
  { value: 'adjustment', label: 'Düzeltme', color: 'yellow' },
  { value: 'waste', label: 'Fire', color: 'red' },
  { value: 'transfer', label: 'Transfer', color: 'purple' },
] as const;
