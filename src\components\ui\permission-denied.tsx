import { AlertTriangle } from 'lucide-react';

interface PermissionDeniedProps {
  message?: string;
  title?: string;
  className?: string;
}

export function PermissionDenied({
  message = 'Bu işlem için yetkiniz bulunmuyor',
  title = '<PERSON><PERSON><PERSON><PERSON>',
  className = 'min-h-[400px]',
}: PermissionDeniedProps) {
  return (
    <div className={`flex ${className} items-center justify-center`}>
      <div className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
          <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {title}
        </h3>
        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
          {message}
        </p>
      </div>
    </div>
  );
}
