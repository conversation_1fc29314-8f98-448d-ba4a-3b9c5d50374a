'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { CheckCir<PERSON>, Star, CalendarCheck, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AppointmentSuccessScreenProps {
  gymId: string;
  appointmentCount: number;
  onContinue?: () => void;
}

export function AppointmentSuccessScreen({
  gymId,
  appointmentCount,
  onContinue,
}: AppointmentSuccessScreenProps) {
  const router = useRouter();
  const [countdown, setCountdown] = useState(3);

  const handleContinue = useCallback(() => {
    if (onContinue) {
      onContinue();
    } else {
      router.push(`/dashboard/gym/${gymId}/appointments`);
    }
  }, [onContinue, router, gymId]);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          handleContinue();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [handleContinue]);

  const handleManualContinue = () => {
    setCountdown(0);
    handleContinue();
  };

  return (
    <div className="container mx-auto py-8">
      <div className="mx-auto max-w-4xl">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="max-w-md text-center">
            <div className="relative mb-8">
              <div className="mx-auto flex h-24 w-24 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20">
                <CheckCircle className="h-12 w-12 text-green-600 dark:text-green-400" />
              </div>
              <div className="bg-primary absolute -top-2 -right-2 flex h-8 w-8 items-center justify-center rounded-full">
                <Star className="text-primary-foreground h-4 w-4" />
              </div>
            </div>

            <h2 className="text-primary mb-3 text-3xl font-bold">
              Randevular Başarıyla Oluşturuldu!
            </h2>

            <p className="text-muted-foreground mb-6 leading-relaxed">
              {appointmentCount} randevu başarıyla sisteme kaydedildi.
              Randevular listesinde görüntüleyebilir ve yönetebilirsiniz.
            </p>

            <div className="text-muted-foreground mb-8 flex items-center justify-center gap-2 text-sm">
              <Clock className="h-4 w-4" />
              <span>
                {countdown > 0
                  ? `${countdown} saniye sonra yönlendirileceksiniz...`
                  : 'Yönlendiriliyor...'}
              </span>
            </div>

            <div className="flex flex-col gap-3 sm:flex-row sm:justify-center">
              <Button
                size="lg"
                className="shadow-lg"
                onClick={handleManualContinue}
              >
                <CalendarCheck className="mr-2 h-5 w-5" />
                Randevulara Git
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
