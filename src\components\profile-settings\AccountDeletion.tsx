'use client';

import { useState, useTransition } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Trash2,
  AlertTriangle,
  CheckCircle,
  Shield,
  UserX,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { deleteAccount } from '@/lib/actions/user/account-security';

export const AccountDeletion = () => {
  const [confirmation, setConfirmation] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | undefined>();
  const [success, setSuccess] = useState<string | undefined>();
  const router = useRouter();

  const handleDelete = async () => {
    setError(undefined);
    setSuccess(undefined);

    const formData = new FormData();
    formData.append('confirmation', confirmation);

    startTransition(async () => {
      try {
        const result = await deleteAccount(
          { success: false, error: undefined, data: undefined },
          formData
        );

        if (result.success) {
          setSuccess('Hesabınız başarıyla silindi. Yönlendiriliyorsunuz...');
          setIsDialogOpen(false);

          // 2 saniye sonra ana sayfaya yönlendir
          setTimeout(() => {
            router.push('/');
          }, 2000);
        } else {
          setError(result.error || 'Hesap silinirken hata oluştu');
        }
      } catch (err) {
        setError('Beklenmeyen bir hata oluştu');
        console.error('Account deletion error:', err);
      }
    });
  };

  const isConfirmationValid =
    confirmation === 'HESABIMI SIL' || confirmation === 'hesabımı sil';

  return (
    <div className="space-y-6">
      {/* Status Messages */}
      {error && (
        <div className="bg-destructive/10 border-destructive/20 text-destructive flex items-center gap-2 rounded-lg border p-4 text-sm">
          <div className="bg-destructive h-2 w-2 rounded-full" />
          {error}
        </div>
      )}
      {success && (
        <div className="flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-4 text-sm text-green-700 dark:border-green-800 dark:bg-green-950 dark:text-green-400">
          <CheckCircle className="h-4 w-4" />
          {success}
        </div>
      )}

      <Card className="border-destructive/30 bg-destructive/5">
        <CardHeader>
          <CardTitle className="text-destructive flex items-center gap-2">
            <UserX className="h-5 w-5" />
            Hesabı Kalıcı Olarak Sil
          </CardTitle>
          <CardDescription className="text-destructive/80">
            ⚠️ Bu işlem geri alınamaz. Hesabınız ve tüm verileriniz kalıcı
            olarak silinecektir.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Uyarı Kutusu */}
          <div className="bg-destructive/15 border-destructive/30 rounded-lg border p-6">
            <div className="flex items-start gap-4">
              <div className="bg-destructive/20 rounded-full p-2">
                <AlertTriangle className="text-destructive h-6 w-6 flex-shrink-0" />
              </div>
              <div className="space-y-3">
                <h4 className="text-destructive text-lg font-semibold">
                  Kritik Uyarı!
                </h4>
                <p className="text-destructive/90 text-sm leading-relaxed">
                  Hesabınızı sildiğinizde aşağıdaki işlemler{' '}
                  <strong>geri alınamaz şekilde</strong> gerçekleştirilir:
                </p>
                <div className="mt-4 space-y-4">
                  <div className="bg-destructive/10 rounded-lg p-4">
                    <p className="text-destructive mb-3 flex items-center gap-2 text-sm font-semibold">
                      <Trash2 className="h-4 w-4" />
                      Tamamen Silinecek Veriler:
                    </p>
                    <div className="text-destructive/80 grid gap-2 text-xs">
                      <div className="flex items-center gap-2">
                        <div className="bg-destructive h-1.5 w-1.5 rounded-full" />
                        <span>Profil bilgileriniz ve fotoğrafınız</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="bg-destructive h-1.5 w-1.5 rounded-full" />
                        <span>Tüm salon üyelikleriniz ve durumları</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="bg-destructive h-1.5 w-1.5 rounded-full" />
                        <span>Satın aldığınız paketler ve abonelikler</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="bg-destructive h-1.5 w-1.5 rounded-full" />
                        <span>Antrenman geçmişiniz ve ilerleme kayıtları</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="bg-destructive h-1.5 w-1.5 rounded-full" />
                        <span>Mesajlar, bildirimler ve davetler</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="bg-destructive h-1.5 w-1.5 rounded-full" />
                        <span>
                          Trainer/Manager hesap bilgileriniz (eğer varsa)
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="bg-destructive h-1.5 w-1.5 rounded-full" />
                        <span>
                          Tüm kişisel verileriniz ve aktivite geçmişiniz
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-lg border border-orange-200 bg-orange-50 p-4 dark:border-orange-800 dark:bg-orange-950/20">
                    <p className="mb-3 flex items-center gap-2 text-sm font-semibold text-orange-700 dark:text-orange-400">
                      <Shield className="h-4 w-4" />
                      Anonimleştirilecek Veriler:
                    </p>
                    <div className="space-y-2 text-xs text-orange-600 dark:text-orange-500">
                      <div className="flex items-center gap-2">
                        <div className="h-1.5 w-1.5 rounded-full bg-orange-500" />
                        <span>Salon yorumlarınız (anonim olarak kalacak)</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="h-1.5 w-1.5 rounded-full bg-orange-500" />
                        <span>Randevu kayıtları (sistem bütünlüğü için)</span>
                      </div>
                      <p className="mt-2 text-xs text-orange-600/80 italic dark:text-orange-500/80">
                        * Bu veriler sizinle ilişkilendirilmeyecek şekilde
                        anonimleştirilir
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Onay Girişi */}
          <div className="border-destructive/20 space-y-4 border-t pt-4">
            <div className="space-y-2">
              <Label htmlFor="confirmation" className="text-sm font-medium">
                Devam etmek için aşağıya{' '}
                <span className="text-destructive bg-destructive/10 rounded px-2 py-1 font-mono text-xs font-bold">
                  HESABIMI SIL
                </span>{' '}
                yazın:
              </Label>
              <Input
                id="confirmation"
                value={confirmation}
                onChange={e => setConfirmation(e.target.value)}
                placeholder="HESABIMI SIL"
                className="border-destructive/30 focus:border-destructive h-11 text-center font-mono"
              />
              {confirmation && !isConfirmationValid && (
                <p className="text-destructive/70 text-xs">
                  Lütfen tam olarak &quot;HESABIMI SIL&quot; yazın
                </p>
              )}
              {isConfirmationValid && (
                <p className="text-xs text-green-600 dark:text-green-400">
                  ✓ Onay metni doğru
                </p>
              )}
            </div>

            <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <AlertDialogTrigger asChild>
                <Button
                  variant="destructive"
                  disabled={!isConfirmationValid || isPending}
                  className="h-12 w-full text-base font-semibold"
                  size="lg"
                >
                  {isPending ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                      Hesap Siliniyor...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <UserX className="h-5 w-5" />
                      Hesabımı Kalıcı Olarak Sil
                    </div>
                  )}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent className="max-w-md">
                <AlertDialogHeader>
                  <AlertDialogTitle className="text-destructive flex items-center gap-2 text-xl">
                    <div className="bg-destructive/20 rounded-full p-2">
                      <AlertTriangle className="h-6 w-6" />
                    </div>
                    Son Uyarı!
                  </AlertDialogTitle>
                  <AlertDialogDescription asChild>
                    <div className="space-y-3 text-sm">
                      <div className="bg-destructive/10 border-destructive/20 rounded-lg border p-4">
                        <div className="text-destructive mb-2 font-semibold">
                          Bu işlem tamamen geri alınamaz!
                        </div>
                        <div className="text-muted-foreground">
                          Hesabınız ve tüm verileriniz kalıcı olarak
                          silinecektir. Bu işlemden sonra hesabınızı kurtarmanın
                          hiçbir yolu yoktur.
                        </div>
                      </div>
                      <div className="text-destructive text-center font-medium">
                        Gerçekten devam etmek istiyor musunuz?
                      </div>
                    </div>
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className="gap-3">
                  <AlertDialogCancel className="flex-1">
                    Hayır, İptal Et
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    disabled={isPending}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90 flex-1"
                  >
                    {isPending ? (
                      <div className="flex items-center gap-2">
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                        Siliniyor...
                      </div>
                    ) : (
                      'Evet, Sil'
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            <div className="border-muted border-t pt-4 text-center">
              <p className="text-muted-foreground text-xs">
                Bu işlem GDPR ve KVKK uyumlu olarak gerçekleştirilir.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
