'use client';

import { toast } from 'sonner';
import { InvitationTabs } from '@/components/invitations';
import { acceptInvitation, rejectInvitation, cancelInvitation } from '@/lib/actions/gym_invitations/invitation-actions';
import type { UnifiedInvitation } from '@/components/invitations';

interface MemberInvitationsActionsProps {
  incomingInvitations: UnifiedInvitation[];
  outgoingInvitations: UnifiedInvitation[];
}

export function MemberInvitationsActions({
  incomingInvitations,
  outgoingInvitations,
}: MemberInvitationsActionsProps) {
  
  const handleAction = async (invitationId: string, action: 'accept' | 'reject' | 'cancel') => {
    try {
      let result;
      
      switch (action) {
        case 'accept':
          result = await acceptInvitation(invitationId);
          if (result.success) {
            toast.success('Davet kabul edildi!');
          } else {
            toast.error(result.error || 'Davet kabul edilirken bir hata oluştu');
          }
          break;
          
        case 'reject':
          result = await rejectInvitation(invitationId);
          if (result.success) {
            toast.success('<PERSON><PERSON> reddedildi');
          } else {
            toast.error(result.error || 'Davet reddedilirken bir hata olu<PERSON>tu');
          }
          break;
          
        case 'cancel':
          result = await cancelInvitation(invitationId);
          if (result.success) {
            toast.success('Talep iptal edildi');
          } else {
            toast.error(result.error || 'Talep iptal edilirken bir hata oluştu');
          }
          break;
          
        default:
          toast.error('Geçersiz işlem');
          return;
      }
      
      // Refresh the page to show updated data
      if (result?.success) {
        window.location.reload();
      }
    } catch (error) {
      console.error('Invitation action error:', error);
      toast.error('Bir hata oluştu');
    }
  };

  return (
    <InvitationTabs
      incomingInvitations={incomingInvitations}
      outgoingInvitations={outgoingInvitations}
      onAction={handleAction}
      userType="member"
    />
  );
}
