'use server';

import { createAction, createAdminAction } from '../core/core';
import { ApiResponse } from '@/types';

import { z } from 'zod';

// <PERSON><PERSON>re değiştirme schema'sı
const changePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, 'Mevcut şifre gereklidir'),
    newPassword: z.string().min(6, 'Yeni şifre en az 6 karakter olmalıdır'),
    confirmPassword: z.string().min(1, '<PERSON><PERSON>re onayı gereklidir'),
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: '<PERSON><PERSON><PERSON>er eşleşmiyor',
    path: ['confirmPassword'],
  });

/**
 * Kullanıcının şifresini değiştirir
 */
export async function changePassword(
  _prevState: ApiResponse<null>,
  formData: FormData
): Promise<ApiResponse<null>> {
  return createAction<null>(async (_, supabase) => {
    // Form verilerini al
    const currentPassword = formData.get('currentPassword') as string;
    const newPassword = formData.get('newPassword') as string;
    const confirmPassword = formData.get('confirmPassword') as string;

    // Validation
    const validation = changePasswordSchema.safeParse({
      currentPassword,
      newPassword,
      confirmPassword,
    });

    if (!validation.success) {
      const errors = validation.error.issues
        .map((err: any) => err.message)
        .join(', ');
      throw new Error(errors);
    }

    // Mevcut şifreyi doğrula
    const { error: signInError } = await supabase.auth.signInWithPassword({
      email: (await supabase.auth.getUser()).data.user?.email || '',
      password: currentPassword,
    });

    if (signInError) {
      throw new Error('Mevcut şifre yanlış.');
    }

    // Yeni şifreyi güncelle
    const { error: updateError } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (updateError) {
      throw new Error(
        'Şifre güncellenirken hata oluştu: ' + updateError.message
      );
    }

    return null;
  });
}

export async function deleteAccount(
  _prevState: ApiResponse<null>,
  formData: FormData
): Promise<ApiResponse<null>> {
  return createAdminAction<null>(async (_, _supabase, userId, adminClient) => {
    // Onay kontrolü
    const confirmation = formData.get('confirmation') as string;
    if (confirmation !== 'HESABIMI SIL') {
      throw new Error('Onay metni yanlış. "HESABIMI SIL" yazmanız gerekiyor.');
    }

    try {
      // 1. Kullanıcının şirket yöneticisi olduğu şirketleri kontrol et
      const { data: managedCompanies } = await adminClient
        .from('companies')
        .select('id, name')
        .eq('manager_profile_id', userId);

      if (managedCompanies && managedCompanies.length > 0) {
        // Şirket yöneticisi olan kullanıcı silinemez, önce yöneticilik devredilmeli
        const companyNames = managedCompanies.map(c => c.name).join(', ');
        throw new Error(
          `Bu hesap şu şirketlerin yöneticisidir: ${companyNames}. Hesabı silmeden önce yöneticilik yetkisini başka birine devretmelisiniz.`
        );
      }

      // 2. Profiles tablosundan kullanıcıyı sil
      // Foreign key constraint'ler sayesinde tüm ilişkili veriler otomatik olarak yönetilecek:
      // - CASCADE tablolar: member_details, trainer_details, gym_trainers, gym_memberships, notifications, user_settings (silinecek)
      // - SET NULL tablolar: appointments, gym_membership_packages, gym_invitations, gym_reviews (null yapılacak)
      const { error: profileError } = await adminClient
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (profileError) {
        throw new Error(
          'Profil silinirken hata oluştu: ' + profileError.message
        );
      }

      // 3. Auth kullanıcısını sil
      const { error: authError } =
        await adminClient.auth.admin.deleteUser(userId);

      if (authError) {
        throw new Error(
          'Kullanıcı hesabı silinirken hata oluştu: ' + authError.message
        );
      }

      return null;
    } catch (error) {
      console.error('Account deletion error:', error);
      throw error;
    }
  });
}
