import { GymCardData } from '@/types/business/gym';
import { MapPin, Star, Users, ExternalLink } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

interface GymCardProps {
  gym: GymCardData;
  index?: number; // fold içindeki ilk kartlar için conditional priority
}

export function GymCard({ gym, index = 0 }: GymCardProps) {
  if (!gym) {
    return null;
  }

  const cityName = gym.city; // searchGyms dönüşümünde isimle dönüştürülüyor
  const location: string | null =
    gym.district && cityName ? `${gym.district}, ${cityName}` : cityName;

  return (
    <Link
      href={`/gym/${gym.slug}`}
      aria-label={`${gym.name} detaylarını gör`}
      className="focus-visible:ring-ring group block h-full transition-all duration-300 ease-out outline-none hover:-translate-y-1 focus-visible:ring-2 focus-visible:ring-offset-2"
    >
      <article
        className="border-border/50 bg-card group-hover:border-primary/40 group-focus-visible:border-primary/40 flex h-full flex-col overflow-hidden rounded-2xl border shadow-sm transition-all duration-300 ease-out group-hover:shadow-lg group-focus-visible:shadow-lg"
        aria-labelledby={`${gym.slug}-title`}
        aria-describedby={`${gym.slug}-location`}
      >
        {/* Image Section */}
        <div
          className="relative w-full overflow-hidden"
          style={{ aspectRatio: '16 / 9' }}
        >
          {gym.cover_image_url ? (
            <Image
              src={gym.cover_image_url}
              alt={`${gym.name} spor salonu görüntüsü`}
              fill
              className="object-cover transition-transform duration-700 ease-out group-hover:scale-110"
              sizes="(max-width: 1024px) 100vw, 50vw"
              // Basit blur placeholder: tek renk (daha sonra gerçek blurDataURL ile değiştirilebilir)
              placeholder="blur"
              blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMzInIGhlaWdodD0nMTgnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHJlY3Qgd2lkdGg9JzMyJyBoZWlnaHQ9JzE4JyBmaWxsPSIjZWVlIi8+PC9zdmc+"
              // İlk satırdaki 2 karta öncelik ver
              priority={index < 2}
            />
          ) : (
            <div className="from-muted to-muted/60 dark:from-muted/80 dark:to-muted/40 flex h-full w-full items-center justify-center bg-gradient-to-br">
              <Users className="text-muted-foreground/40 dark:text-muted-foreground/30 h-16 w-16" />
            </div>
          )}

          {/* Gradient overlay for better text readability */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent dark:from-black/50" />

          {/* Company Logo - Sol üst */}
          {gym.company?.logo_url && (
            <div className="absolute top-3 left-3 h-12 w-12 overflow-hidden rounded-full border-2 border-white/80 shadow-lg backdrop-blur-md">
              <Image
                src={gym.company.logo_url}
                alt={`${gym.company.name} - Logo`}
                className="h-full w-full object-cover"
                width={48}
                height={48}
              />
            </div>
          )}

          {/* Rating Badge - Sağ üst */}
          {gym.average_rating && (
            <div className="border-border/30 bg-card/95 absolute top-3 right-3 flex items-center gap-1.5 rounded-full border px-3 py-1.5 shadow-lg backdrop-blur-md">
              <Star className="h-3.5 w-3.5 fill-yellow-500 text-yellow-500 dark:fill-yellow-400 dark:text-yellow-400" />
              <span className="text-card-foreground text-sm font-bold">
                {gym.average_rating?.toFixed(1)}
              </span>
            </div>
          )}

          {/* External link icon */}
          <div
            className="absolute right-3 bottom-3 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
            aria-hidden
          >
            <div className="bg-primary/90 flex items-center justify-center rounded-full p-2 shadow-lg backdrop-blur-sm">
              <ExternalLink className="text-primary-foreground h-4 w-4" />
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="flex flex-1 flex-col p-5">
          {/* Header */}
          <header className="mb-4">
            <h3
              id={`${gym.slug}-title`}
              className="text-foreground group-hover:text-primary mb-2 line-clamp-2 text-xl leading-tight font-semibold transition-colors duration-200"
            >
              {gym.name}
            </h3>
            <div className="text-muted-foreground flex items-center gap-2">
              <MapPin className="text-primary/70 h-4 w-4 flex-shrink-0" />
              <span
                id={`${gym.slug}-location`}
                className="truncate text-sm font-medium"
              >
                {location}
              </span>
            </div>
          </header>
        </div>
      </article>
    </Link>
  );
}
