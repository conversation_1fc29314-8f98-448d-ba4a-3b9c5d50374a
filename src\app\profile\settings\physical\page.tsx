import { PhysicalInformation } from '@/components/profile-settings/PhysicalInformation';
import { getMemberDetails } from '@/lib/actions/user/member-actions';
import { getUserRoles } from '@/lib/auth/server-auth';
import { MemberDetails } from '@/types/database/tables';
import { redirect } from 'next/navigation';

export default async function PhysicalSettingsPage() {
  // Kullanıcının member rolü olup olmadığını kontrol et
  const userRoles = await getUserRoles();
  const isMember = userRoles.includes('member');

  // Member değilse profil sayfasına yönlendir
  if (!isMember) {
    redirect('/profile/settings/profile');
  }

  // Server action ile member details verilerini çek
  const detailsResult = await getMemberDetails();

  const memberDetails: MemberDetails | null =
    detailsResult.success && detailsResult.data ? detailsResult.data : null;

  // Hata durumunda konsola log
  if (!detailsResult.success) {
    console.warn('Member details not found:', detailsResult.error);
  }

  return (
    <div className="flex-1 space-y-6 p-6 lg:p-8">
      {/* Sayfa başlığı */}
      <div className="space-y-2">
        <h1 className="text-2xl font-semibold tracking-tight">
          Fiziksel Bilgiler
        </h1>
        <p className="text-muted-foreground">
          Fiziksel özelliklerinizi ve fitness hedeflerinizi belirleyin
        </p>
      </div>

      {/* Fiziksel bilgiler formu */}
      <div className="space-y-6">
        <PhysicalInformation memberDetails={memberDetails} />
      </div>
    </div>
  );
}
