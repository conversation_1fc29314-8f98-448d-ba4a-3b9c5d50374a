'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { EnhancedInput } from '@/components/ui/enhanced-input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { EnhancedTextarea } from '@/components/ui/enhanced-textarea';
import { Dumbbell } from 'lucide-react';
import { INPUT_RULES } from '@/lib/utils/form-validation';

interface TrainerData {
  specialization: string;
  certification_level: string;
  experience_years: string;
  bio: string;
}

interface TrainerFormProps {
  data: TrainerData;
  onChange: (data: TrainerData) => void;
}

export function TrainerForm({ data, onChange }: TrainerFormProps) {
  const handleChange = (field: keyof TrainerData, value: string) => {
    onChange({ ...data, [field]: value });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Dumbbell className="mr-2 h-5 w-5" />
          Antrenör Bilgileri
        </CardTitle>
        <CardDescription>
          Antrenörlük deneyiminiz ve uzmanlık alanınız
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <Label htmlFor="specialization">Uzmanlık Alanı *</Label>
            <Select
              value={data.specialization}
              onValueChange={value => handleChange('specialization', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Uzmanlık alanı seçin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="weight_training">
                  Ağırlık Antrenmanı
                </SelectItem>
                <SelectItem value="cardio">Kardiyovasküler</SelectItem>
                <SelectItem value="yoga">Yoga</SelectItem>
                <SelectItem value="pilates">Pilates</SelectItem>
                <SelectItem value="crossfit">CrossFit</SelectItem>
                <SelectItem value="boxing">Boks</SelectItem>
                <SelectItem value="swimming">Yüzme</SelectItem>
                <SelectItem value="general">Genel Fitness</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="certification">Sertifika Seviyesi *</Label>
            <Select
              value={data.certification_level}
              onValueChange={value =>
                handleChange('certification_level', value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Sertifika seviyesi" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Sertifika Yok</SelectItem>
                <SelectItem value="basic">Temel Sertifika</SelectItem>
                <SelectItem value="advanced">İleri Seviye</SelectItem>
                <SelectItem value="expert">Uzman</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="experience">Deneyim (Yıl)</Label>
          <EnhancedInput
            id="experience"
            type="text"
            placeholder="Kaç yıldır antrenörlük yapıyorsunuz?"
            value={data.experience_years}
            onChange={value => handleChange('experience_years', value)}
            formatter={INPUT_RULES.EXPERIENCE.formatter}
            validator={value => {
              if (!value) return true; // Optional field
              const num = parseInt(value);
              return (
                !isNaN(num) &&
                num >= INPUT_RULES.EXPERIENCE.min &&
                num <= INPUT_RULES.EXPERIENCE.max
              );
            }}
            validationMessage={`Deneyim ${INPUT_RULES.EXPERIENCE.min}-${INPUT_RULES.EXPERIENCE.max} yıl arasında olmalıdır`}
          />
        </div>

        <div>
          <Label htmlFor="bio">Kısa Biyografi</Label>
          <EnhancedTextarea
            id="bio"
            placeholder="Kendinizi kısaca tanıtın..."
            value={data.bio}
            onChange={value => handleChange('bio', value)}
            formatter={INPUT_RULES.BIO.formatter}
            maxLength={INPUT_RULES.BIO.maxLength}
            characterCount={true}
            rows={3}
          />
        </div>
      </CardContent>
    </Card>
  );
}
