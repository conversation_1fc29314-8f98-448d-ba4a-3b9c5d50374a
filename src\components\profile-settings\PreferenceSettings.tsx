'use client';

import { useState, useTransition } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Eye, Users, Lock } from 'lucide-react';

import { UserSettings } from '@/types/database/tables';
import { ProfileVisibility } from '@/lib/supabase/types';
import { toast } from 'sonner';
import { upSertUserSettings } from '@/lib/actions/user/user-settings-actions';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface PreferenceSettingsProps {
  settings: UserSettings;
}

const profileVisibilityOptions = [
  {
    value: 'public' as ProfileVisibility,
    label: 'Herkese Açık',
    description: 'Profiliniz herkese görünür',
    icon: Users,
  },
  {
    value: 'friends' as ProfileVisibility,
    label: 'Sadece Arkadaşlar',
    description: 'Sadece arkadaşlarınız görebilir',
    icon: Eye,
  },
  {
    value: 'private' as ProfileVisibility,
    label: 'G<PERSON><PERSON>',
    description: 'Profiliniz sadece size görünür',
    icon: Lock,
  },
];

export function PreferenceSettings({ settings }: PreferenceSettingsProps) {
  const [localSettings, setLocalSettings] = useState({
    profile_visibility: settings.profile_visibility || 'public',
  });

  const [isPending, startTransition] = useTransition();

  const handleVisibilityChange = (value: ProfileVisibility) => {
    const newSettings = { ...localSettings, profile_visibility: value };
    setLocalSettings(newSettings);

    startTransition(async () => {
      try {
        const result = await upSertUserSettings(newSettings);
        if (result.success) {
          toast.success('Gizlilik ayarları güncellendi');
        } else {
          toast.error(result.error || 'Ayarlar güncellenirken hata oluştu');
          // Hata durumunda eski ayarlara geri dön
          setLocalSettings({
            profile_visibility: settings.profile_visibility || 'public',
          });
        }
      } catch (error) {
        toast.error('Beklenmeyen bir hata oluştu');
        console.error('Privacy settings update error:', error);
      }
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          Gizlilik Ayarları
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">Profil Görünürlüğü</Label>
          <Select
            value={localSettings.profile_visibility}
            onValueChange={(value: ProfileVisibility) =>
              handleVisibilityChange(value)
            }
            disabled={isPending}
          >
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {profileVisibilityOptions.map(({ value, label, icon: Icon }) => (
                <SelectItem key={value} value={value}>
                  <div className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    {label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
}
