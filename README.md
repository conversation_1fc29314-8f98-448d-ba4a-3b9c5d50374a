# Sportiva - Comprehensive Gym Management System

Sportiva is a modern, feature-rich SaaS platform designed to streamline gym management for owners, trainers, and members. It provides a full suite of tools for managing memberships, schedules, payments, and member engagement, all in one centralized dashboard.

## ✨ Key Features

- **Role-Based Dashboards:** Separate, tailored dashboards for Gym Owners/Managers, Trainers, and Members.
- **Member Management:** Track member profiles, attendance, and progress.
- **Trainer Tools:** Trainers can manage their schedules, view assigned members, and track client progress.
- **Equipment Management:** Complete equipment tracking with maintenance scheduling, condition monitoring, and usage analytics.
- **Inventory Management:** Stock tracking, low stock alerts, expiry date monitoring, and supplier management.
- **Gym Discovery:** A public-facing page for potential customers to find and learn about your gym.
- **Secure Authentication:** Robust and secure user authentication powered by Supabase Auth.
- **Onboarding Flow:** A smooth and guided setup process for new gyms.
- **Profile & Settings:** Users can manage their profiles, notification preferences, and account settings.
- **Modern UI/UX:** Built with the latest web technologies for a fast, responsive, and user-friendly experience.

## 🛠️ Tech Stack

- **Framework:** [Next.js](https://nextjs.org/) (with App Router & Turbopack)
- **Language:** [TypeScript](https://www.typescriptlang.org/)
- **Backend & Auth:** [Supabase](https://supabase.io/)
- **Styling:** [Tailwind CSS](https://tailwindcss.com/)
- **UI Components:** [shadcn/ui](https://ui.shadcn.com/), Radix UI
- **Forms:** [React Hook Form](https://react-hook-form.com/) with [Zod](https://zod.dev/) for validation
- **Package Manager:** [pnpm](https://pnpm.io/)
- **E2E Testing:** [Playwright](https://playwright.dev/)

## 🚀 Getting Started

Follow these instructions to get the project up and running on your local machine.

### Prerequisites

- [Node.js](https://nodejs.org/) (v18 or later recommended)
- [pnpm](https://pnpm.io/installation)

### Installation

1.  **Clone the repository:**

    ```bash
    git clone https://github.com/your-username/sportiva.git
    cd sportiva
    ```

2.  **Install dependencies:**
    ```bash
    pnpm install
    ```

### Environment Variables

This project uses Supabase for its backend. You will need to create a Supabase project and obtain the necessary credentials.

1.  Create a `.env.local` file in the root of the project by copying the example file:

    ```bash
    cp .env.example .env.local
    ```

2.  Add the following environment variables to your `.env.local` file. You can find these in your Supabase project settings.

    ```env
    # The public URL of your application
    NEXT_PUBLIC_APP_URL=http://localhost:3000

    # Supabase credentials
    NEXT_PUBLIC_SUPABASE_URL=YOUR_NEXT_PUBLIC_SUPABASE_URL
    NEXT_PUBLIC_SUPABASE_ANON_KEY=YOUR_NEXT_PUBLIC_SUPABASE_ANON_KEY
    SUPABASE_SERVICE_ROLE_KEY=YOUR_SUPABASE_SERVICE_ROLE_KEY
    ```

    > **Note:** The `SUPABASE_SERVICE_ROLE_KEY` is optional for some client-side operations but required for server-side administrative tasks.

### Running the Application

Once the dependencies are installed and the environment variables are set, you can run the development server:

```bash
pnpm dev
```

The application should now be running at [http://localhost:3000](http://localhost:3000).

## 📜 Available Scripts

Here are some of the most common scripts you might use:

| Script             | Description                                             |
| ------------------ | ------------------------------------------------------- |
| `pnpm dev`         | Starts the development server with Turbopack.           |
| `pnpm build`       | Creates a production-ready build of the application.    |
| `pnpm start`       | Starts the production server.                           |
| `pnpm lint`        | Lints the codebase for potential errors.                |
| `pnpm format`      | Formats all files using Prettier.                       |
| `pnpm type-check`  | Runs the TypeScript compiler to check for type errors.  |
| `pnpm test:e2e`    | Runs the end-to-end tests using Playwright.             |
| `pnpm test:e2e:ui` | Opens the Playwright UI for interactive test debugging. |

## 📂 Project Structure

The project follows a standard Next.js App Router structure:

```
.
├── public/              # Static assets
├── src/
│   ├── app/             # Application routes and pages
│   ├── components/      # Reusable React components
│   ├── hooks/           # Custom React hooks
│   ├── lib/             # Core logic, utilities, and Supabase client
│   ├── types/           # TypeScript type definitions
│   └── middleware.ts    # Next.js middleware for authentication
├── package.json         # Project dependencies and scripts
└── tsconfig.json        # TypeScript configuration
```
