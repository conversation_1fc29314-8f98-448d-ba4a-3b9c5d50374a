'use client';

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { User, Package, Calendar, CheckCircle, Search } from 'lucide-react';
import { MembershipWithMember } from '@/types/business/membership';
import { TrainerWithProfile } from '@/lib/actions/dashboard/company/appointment-actions';
import { getMemberPackages } from '@/lib/actions/all-actions';

import { SmartSessionPlanner } from './smart-session-planner';
import { QuickConfirmation } from './quick-confirmation';
import { AppointmentSuccessScreen } from './appointment-success-screen';
import { SmartPackageSelection } from './smart-package-selection';
import { cn } from '@/lib/utils';

// Wizard Step Types
type WizardStep =
  | 'member'
  | 'package'
  | 'session-planner'
  | 'confirmation'
  | 'success';

interface WizardStepConfig {
  id: WizardStep;
  icon: React.ComponentType<{ className?: string }>;
}

const WIZARD_STEPS: WizardStepConfig[] = [
  {
    id: 'member',
    icon: User,
  },
  {
    id: 'package',
    icon: Package,
  },
  {
    id: 'session-planner',
    icon: Calendar,
  },
  {
    id: 'confirmation',
    icon: CheckCircle,
  },
];

interface SmartAppointmentFormProps {
  gymId: string;
  members: MembershipWithMember[];
  trainers: TrainerWithProfile[];
  preSelectedMemberId?: string;
}

interface SelectedMemberData {
  member: MembershipWithMember['member'];
  membershipId: string;
  packages: any[];
}

interface AppointmentFlow {
  type: 'join-group' | 'create-new' | 'create-bulk' | 'session-planner';
  appointmentId?: string;
  packageId?: string;
  trainerId?: string;
  date?: string;
  time?: string;
  existingParticipants?: any[];
  maxParticipants?: number;
  bulkAppointments?: Array<{ date: string; time: string }>;
  plannedSessions?: Array<{ date: string; time: string }>;
}

export function SmartAppointmentForm({
  gymId,
  members,
  trainers,
  preSelectedMemberId,
}: SmartAppointmentFormProps) {
  const [currentStep, setCurrentStep] = useState<WizardStep>('member');
  const [selectedMember, setSelectedMember] =
    useState<SelectedMemberData | null>(null);
  const [selectedTrainer, setSelectedTrainer] =
    useState<TrainerWithProfile | null>(null);
  const [appointmentFlow, setAppointmentFlow] = useState<AppointmentFlow>({
    type: 'create-new',
  });
  const [loading, setLoading] = useState(false);
  const [createdAppointmentId, setCreatedAppointmentId] = useState<
    string | null
  >(null);

  const handleMemberSelect = useCallback(
    async (member: MembershipWithMember) => {
      setLoading(true);
      try {
        const packagesResult = await getMemberPackages(
          gymId,
          member.member?.id || ''
        );

        if (packagesResult.success && packagesResult.data) {
          setSelectedMember({
            member: member.member,
            membershipId: member.id,
            packages: packagesResult.data,
          });
          // Auto-advance to next step
          setCurrentStep('package');
        } else {
          toast.error(packagesResult.error || 'Üye paketleri yüklenemedi');
        }
      } catch (error) {
        toast.error(error instanceof Error ? error.message : 'Bir hata oluştu');
      } finally {
        setLoading(false);
      }
    },
    [gymId]
  );

  // Auto-select member if preSelectedMemberId is provided
  useEffect(() => {
    if (preSelectedMemberId && members.length > 0) {
      const member = members.find(m => m.member?.id === preSelectedMemberId);
      if (member) {
        handleMemberSelect(member);
      }
    }
  }, [preSelectedMemberId, members, handleMemberSelect]);

  const handlePackageSelect = (packageId: string) => {
    // For session planner, use the first trainer as default
    if (trainers.length > 0) {
      setSelectedTrainer(trainers[0]);
    }

    setAppointmentFlow({
      type: 'session-planner',
      packageId,
    });
    setCurrentStep('session-planner');
  };

  const handleSessionsConfirm = (
    sessions: Array<{ date: string; time: string }>
  ) => {
    setAppointmentFlow(prev => ({
      ...prev,
      plannedSessions: sessions,
    }));
    setCurrentStep('confirmation');
  };

  const handleAppointmentSuccess = (appointmentId: string) => {
    setCreatedAppointmentId(appointmentId);
    setCurrentStep('success');
  };

  const handleBack = () => {
    const stepOrder: WizardStep[] = [
      'member',
      'package',
      'session-planner',
      'confirmation',
    ];
    const currentIndex = stepOrder.indexOf(currentStep);
    if (currentIndex > 0) {
      setCurrentStep(stepOrder[currentIndex - 1]);
    }
  };

  const getStepProgress = () => {
    const stepOrder: WizardStep[] = [
      'member',
      'package',
      'session-planner',
      'confirmation',
    ];
    const currentIndex = stepOrder.indexOf(currentStep);
    return ((currentIndex + 1) / stepOrder.length) * 100;
  };

  if (currentStep === 'success' && createdAppointmentId) {
    return (
      <AppointmentSuccessScreen
        gymId={gymId}
        appointmentCount={1}
        onContinue={() => {
          setCurrentStep('member');
          setSelectedMember(null);
          setSelectedTrainer(null);
          setAppointmentFlow({ type: 'create-new' });
          setCreatedAppointmentId(null);
        }}
      />
    );
  }

  return (
    <div className="container mx-auto max-w-7xl">
      {/* Sticky Progress Header */}
      <div className="bg-background/95 sticky -top-8 z-10 mb-6 border-b py-2 backdrop-blur-sm">
        <div className="relative mx-auto flex justify-evenly px-8">
          {/* Progress Line */}
          <div className="bg-muted absolute top-4 right-0 left-0 -z-10 h-0.5">
            <div
              className="bg-primary h-full transition-all duration-300 ease-out"
              style={{ width: `${getStepProgress()}%` }}
            />
          </div>

          {WIZARD_STEPS.map((step, index) => {
            const isActive = step.id === currentStep;
            const isCompleted =
              WIZARD_STEPS.findIndex(s => s.id === currentStep) > index;
            const Icon = step.icon;

            return (
              <div
                key={step.id}
                className="relative z-10 flex flex-col items-center"
              >
                <Button
                  variant={
                    isActive ? 'default' : isCompleted ? 'link' : 'outline'
                  }
                  size="icon"
                  className={cn(
                    'bg-background h-8 w-8 cursor-default rounded-full border-2',
                    isActive || isCompleted ? 'border-primary' : 'border-border'
                  )}
                  disabled={!isCompleted && !isActive}
                >
                  <Icon className="h-3 w-3" />
                </Button>
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <div className="min-h-[400px]">
        {currentStep === 'member' && (
          <MemberSelectionStep
            members={members}
            onMemberSelect={handleMemberSelect}
            loading={loading}
            selectedMember={selectedMember}
          />
        )}

        {currentStep === 'package' &&
          selectedMember &&
          selectedMember.member && (
            <SmartPackageSelection
              packages={selectedMember.packages}
              gymId={gymId}
              member={{
                id: selectedMember.member.id,
                full_name: selectedMember.member.full_name,
              }}
              onPackageSelect={handlePackageSelect}
              selectedPackageId={appointmentFlow.packageId}
            />
          )}

        {currentStep === 'session-planner' &&
          selectedMember &&
          selectedTrainer && (
            <SmartSessionPlanner
              totalSessions={
                selectedMember.packages.find(
                  p => p.id === appointmentFlow.packageId
                )?.remaining_sessions || 1
              }
              trainerId={selectedTrainer.id}
              gymId={gymId}
              packageData={
                selectedMember.packages.find(
                  p => p.id === appointmentFlow.packageId
                ) || {
                  gym_package: {
                    session_duration_minutes: 60,
                    package_type: 'appointment_standard',
                  },
                }
              }
              onSessionsConfirm={handleSessionsConfirm}
            />
          )}

        {currentStep === 'confirmation' &&
          selectedMember &&
          selectedMember.member &&
          selectedTrainer && (
            <QuickConfirmation
              member={{
                id: selectedMember.member.id,
                full_name: selectedMember.member.full_name,
              }}
              package={
                selectedMember.packages.find(
                  p => p.id === appointmentFlow.packageId
                )!
              }
              trainer={{
                id: selectedTrainer.id,
                full_name: selectedTrainer.full_name || '',
              }}
              appointment={{
                id: appointmentFlow.appointmentId,
                date: appointmentFlow.date,
                time: appointmentFlow.time,
                type: appointmentFlow.type,
                existing_participants: appointmentFlow.existingParticipants,
                max_participants: appointmentFlow.maxParticipants,
                bulk_appointments: appointmentFlow.bulkAppointments,
                planned_sessions: appointmentFlow.plannedSessions,
              }}
              gymId={gymId}
              onSuccess={handleAppointmentSuccess}
              onBack={handleBack}
            />
          )}
      </div>

      {/* Navigation */}
      {currentStep !== 'confirmation' && currentStep !== 'success' && (
        <div className="flex justify-between">
          {currentStep !== 'member' && (
            <Button variant="outline" onClick={handleBack} disabled={loading}>
              Önceki Adım
            </Button>
          )}
        </div>
      )}
    </div>
  );
}

// Member Selection Step Component
interface MemberSelectionStepProps {
  members: MembershipWithMember[];
  onMemberSelect: (member: MembershipWithMember) => void;
  loading: boolean;
  selectedMember: SelectedMemberData | null;
}

function MemberSelectionStep({
  members,
  onMemberSelect,
  loading,
  selectedMember,
}: MemberSelectionStepProps) {
  const [searchQuery, setSearchQuery] = useState('');

  // Filtreleme fonksiyonu
  const filteredMembers = members
    .filter(member => member.member)
    .filter(member => {
      if (!searchQuery) return true;

      const query = searchQuery.toLowerCase();
      const fullName = member.member!.full_name.toLowerCase();

      return fullName.includes(query);
    });

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Üye Seçimi</h2>
        <p className="text-muted-foreground">Randevu alacak üyeyi seçin</p>
      </div>

      {/* Arama Kutusu */}
      <div className="relative">
        <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
        <Input
          placeholder="Üye adı veya e-posta ile ara..."
          value={searchQuery}
          onChange={e => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Üye Listesi */}
      <div className="grid max-h-96 gap-4 overflow-y-auto">
        {filteredMembers.length === 0 ? (
          <div className="text-muted-foreground py-8 text-center">
            {searchQuery
              ? 'Arama kriterlerine uygun üye bulunamadı'
              : 'Aktif üye bulunamadı'}
          </div>
        ) : (
          filteredMembers.map(member => (
            <Button
              key={member.id}
              variant={
                selectedMember?.membershipId === member.id
                  ? 'default'
                  : 'outline'
              }
              className="h-auto justify-start p-4"
              onClick={() => onMemberSelect(member)}
              disabled={loading}
            >
              <div className="flex items-center gap-3">
                <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">
                  {member.member!.full_name.slice(0, 2).toUpperCase()}
                </div>
                <div className="text-left">
                  <div className="font-medium">{member.member!.full_name}</div>
                  <div className="text-muted-foreground text-sm">
                    {member.member!.email}
                  </div>
                </div>
              </div>
            </Button>
          ))
        )}
      </div>
    </div>
  );
}
