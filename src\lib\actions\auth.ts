'use server';

import { getUserRoles, getAuthenticatedUser } from '@/lib/auth/server-auth';
import { getSupabaseAdmin } from '@/lib/supabase/admin';

/**
 * Yönetici tipini belirle: şirket yöneticisi mi salon yöneticisi mi?
 */
async function getManagerType(): Promise<'company' | 'gym' | null> {
  const user = await getAuthenticatedUser();
  if (!user) return null;

  const adminClient = getSupabaseAdmin();

  // Şirket yöneticisi mi kontrol et
  const { data: company } = await adminClient
    .from('companies')
    .select('id')
    .eq('manager_profile_id', user.id)
    .maybeSingle();

  if (company) return 'company';

  // Salon yöneticisi mi kontrol et
  const { data: gym } = await adminClient
    .from('gyms')
    .select('id')
    .eq('manager_profile_id', user.id)
    .maybeSingle();

  if (gym) return 'gym';

  return null;
}

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n rollerine göre uygun dashboard yolunu döndürür.
 * @returns {Promise<string>} Yönlendirilecek yol.
 */
export async function getDashboardPath(): Promise<string> {
  try {
    const roles = await getUserRoles();

    if (roles.includes('manager')) {
      // Manager rolü varsa, hangi tip yönetici olduğunu belirle
      const managerType = await getManagerType();

      if (managerType === 'company') {
        return '/dashboard/manager';
      } else if (managerType === 'gym') {
        return '/dashboard/gym-manager';
      }

      // Fallback: varsayılan manager sayfası
      return '/dashboard/manager';
    }

    if (roles.includes('trainer')) return '/dashboard/trainer';
    if (roles.includes('member')) return '/dashboard/member';

    // Eğer hiçbir rolü yoksa ama giriş yapmışsa, onboarding'e yönlendir
    if (roles.length === 0) return '/onboarding';

    // Beklenmedik bir durum için varsayılan yol
    return '/onboarding';
  } catch (error) {
    // Kimlik doğrulama hatası veya başka bir hata durumunda giriş sayfasına yönlendir
    console.error('Dashboard path alınırken hata:', error);
    return '/auth/login';
  }
}
