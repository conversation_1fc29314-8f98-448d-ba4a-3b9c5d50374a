'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { updateMemberStatus } from '@/lib/actions/all-actions';

interface MemberStatusSelectProps {
  membershipId: string;
  gymId: string;
  initialStatus: string | null;
}

export function MemberStatusSelect({
  membershipId,
  gymId,
  initialStatus,
}: MemberStatusSelectProps) {
  const [updatingStatus, setUpdatingStatus] = useState<boolean>(false);

  const handleStatusChange = async (newStatus: 'active' | 'passive') => {
    setUpdatingStatus(true);
    try {
      const result = await updateMemberStatus(gymId, {
        membershipId,
        status: newStatus,
      });
      if (result.success) {
        toast.success('Üyelik durumu başarıyla güncellendi');
      } else {
        toast.error(result.error || 'Durum güncellenirken hata oluştu');
      }
    } catch {
      toast.error('Durum güncellenirken hata oluştu');
    } finally {
      setUpdatingStatus(false);
    }
  };

  return (
    <Select
      value={initialStatus || 'active'}
      onValueChange={value => handleStatusChange(value as 'active' | 'passive')}
      disabled={updatingStatus}
    >
      <SelectTrigger className="w-32">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="active">
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 rounded-full bg-green-500"></div>
            Aktif
          </div>
        </SelectItem>
        <SelectItem value="passive">
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 rounded-full bg-gray-500"></div>
            Pasif
          </div>
        </SelectItem>
      </SelectContent>
    </Select>
  );
}
