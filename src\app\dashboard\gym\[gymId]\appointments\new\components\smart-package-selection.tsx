'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface MembershipPackage {
  id: string;
  remaining_sessions: number;
  gym_package: {
    id: string;
    name: string;
    package_type: string;
    max_participants: number;
    session_duration_minutes: number;
  };
}

interface SmartPackageSelectionProps {
  packages: MembershipPackage[];
  gymId: string;
  member: { id: string; full_name: string };
  onPackageSelect: (packageId: string) => void;
  selectedPackageId?: string;
}

export function SmartPackageSelection({
  packages,
  onPackageSelect,
  selectedPackageId,
}: SmartPackageSelectionProps) {
  const handlePackageSelect = (packageId: string) => {
    onPackageSelect(packageId);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Paket Seçimi</h2>
        <p className="text-muted-foreground">
          <PERSON><PERSON><PERSON> oluşturmak için bir paket seçin
        </p>
      </div>

      <div className="space-y-4">
        {packages.map(pkg => (
          <Card
            key={pkg.id}
            className={`cursor-pointer transition-all duration-200 ${
              selectedPackageId === pkg.id
                ? 'ring-primary border-primary ring-2'
                : 'hover:shadow-md'
            }`}
            onClick={() => handlePackageSelect(pkg.id)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">
                    {pkg.gym_package.name}
                  </CardTitle>
                  <p className="text-muted-foreground text-sm">
                    {pkg.remaining_sessions} seans kaldı •{' '}
                    {pkg.gym_package.session_duration_minutes} dakika
                  </p>
                </div>
                <Badge variant="outline">
                  {pkg.gym_package.package_type === 'personal'
                    ? 'Kişisel'
                    : 'Grup'}
                </Badge>
              </div>
            </CardHeader>

            <CardContent>
              <Button
                className="w-full"
                variant={selectedPackageId === pkg.id ? 'default' : 'outline'}
              >
                Bu Paketi Seç
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
