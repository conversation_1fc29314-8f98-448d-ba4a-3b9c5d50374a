/**
 * Gym Manager Invitation Types
 * Single-use invite codes for gym manager assignments
 */

import type { GymManagerInvitationStatus } from './database/enums';
import {
  Companies,
  GymManagerInvitations,
  Gyms,
  Profiles,
} from './database/tables';

export interface GymManagerInvitation {
  id: string;
  company_id: string;
  invite_code: string;
  gym_id: string;
  status: GymManagerInvitationStatus;
  used_by: string | null;
  used_at: string | null;
  expires_at: string;
  created_at: string | null;
  updated_at: string | null;
}

export interface GymManagerInvitationWithDetails extends GymManagerInvitations {
  company: Companies;
  gym: Pick<Gyms, 'id' | 'name'>;
  user: Pick<Profiles, 'id' | 'full_name' | 'email'> | null;
}

export interface GymManagerInvitationValidation {
  isValid: boolean;
  isExpired: boolean;
  isUsed: boolean;
  invitation?: GymManagerInvitationWithDetails;
  error?: string;
}
