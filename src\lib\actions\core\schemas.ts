import { GYM_TYPES } from '@/lib/constants';
import { z } from 'zod';

// Company schemas (güncellenmiş - slug, description, website kaldırıldı)
export const companySetupSchema = z.object({
  name: z
    .string()
    .min(1, 'Şirket adı gereklidir')
    .max(100, 'Şirket adı en fazla 100 karakter olabilir'),
  phone: z
    .string()
    .optional()
    .refine(
      val => !val || val === '' || (val.length >= 10 && val.length <= 15),
      {
        message: 'Telefon numarası 10-15 karakter arasında olmalıdır',
      }
    ),
  email: z
    .string()
    .optional()
    .refine(
      val => !val || val === '' || z.string().email().safeParse(val).success,
      {
        message: 'Geçerli bir email adresi giriniz',
      }
    )
    .refine(val => !val || val.length <= 255, {
      message: 'Email adresi çok uzun',
    }),
  // Onboarding sırasında opsiyonel logo alanı
  logo_url: z
    .string()
    .optional()
    .refine(
      val => !val || val === '' || z.string().url().safeParse(val).success,
      {
        message: 'Geçerli bir URL giriniz',
      }
    ),
});

// Company settings schema - tüm alanları içerir
export const companySettingsSchema = z.object({
  name: z
    .string()
    .min(1, 'Şirket adı gereklidir')
    .max(100, 'Şirket adı en fazla 100 karakter olabilir'),
  phone: z
    .string()
    .optional()
    .refine(
      val => !val || val === '' || (val.length >= 10 && val.length <= 15),
      {
        message: 'Telefon numarası 10-15 karakter arasında olmalıdır',
      }
    ),
  email: z
    .string()
    .optional()
    .refine(
      val => !val || val === '' || z.string().email().safeParse(val).success,
      {
        message: 'Geçerli bir email adresi giriniz',
      }
    )
    .refine(val => !val || val.length <= 255, {
      message: 'Email adresi çok uzun',
    }),
  status: z
    .string()
    .optional()
    .refine(val => !val || ['active', 'passive', 'suspended'].includes(val), {
      message: 'Geçersiz şirket durumu',
    }),
  logo_url: z.string().optional(),

  platform_package_id: z.string().optional(),
  subscription_start_date: z.string().optional(),
  subscription_end_date: z.string().optional(),
});

// Gym settings form için daha kapsamlı schema
export const gymSettingsSchema = z.object({
  gymId: z.string().min(1, "Salon ID'si gereklidir"),
  name: z
    .string()
    .min(1, 'Salon adı gereklidir')
    .max(100, 'Salon adı en fazla 100 karakter olabilir'),
  description: z
    .string()
    .max(500, 'Açıklama en fazla 500 karakter olabilir')
    .optional()
    .or(z.literal('')),
  gym_phone: z
    .string()
    .optional()
    .refine(
      val => !val || val === '' || (val.length >= 10 && val.length <= 15),
      {
        message: 'Telefon numarası 10-15 karakter arasında olmalıdır',
      }
    ),
  email: z
    .string()
    .optional()
    .refine(
      val => !val || val === '' || z.string().email().safeParse(val).success,
      {
        message: 'Geçerli bir email adresi giriniz',
      }
    )
    .refine(val => !val || val.length <= 255, {
      message: 'Email adresi çok uzun',
    }),
  address: z.string().min(1, 'Adres gereklidir').max(255, 'Adres çok uzun'),
  city: z.string().min(1, 'Şehir gereklidir').max(100, 'Şehir adı çok uzun'),
  district: z.string().min(1, 'İlçe gereklidir').max(100, 'İlçe adı çok uzun'),
  gym_type: z.enum(
    GYM_TYPES.map((type: { value: string }) => type.value) as [
      string,
      ...string[],
    ],
    'Geçerli bir salon türü seçiniz'
  ),
  features: z.array(z.string()).optional(),
  // Salon açılış ve kapanış saatleri (opsiyonel). Biçim HH:MM olmalıdır (ör. "08:00")
  opening_time: z
    .string()
    .optional()
    .nullable()
    .transform(val => {
      // Boş, null veya undefined değerleri null'a çevir
      if (!val || val === '') return null;
      // HH:MM formatını HH:MM:SS formatına çevir (veritabanı için)
      if (/^(?:[01]\d|2[0-3]):[0-5][0-9]$/.test(val)) {
        return val + ':00';
      }
      return val;
    }),
  closing_time: z
    .string()
    .optional()
    .nullable()
    .transform(val => {
      // Boş, null veya undefined değerleri null'a çevir
      if (!val || val === '') return null;
      // HH:MM formatını HH:MM:SS formatına çevir (veritabanı için)
      if (/^(?:[01]\d|2[0-3]):[0-5][0-9]$/.test(val)) {
        return val + ':00';
      }
      return val;
    }),
  // Randevu saatleri listesi
  time_slots: z.array(z.string()).optional(),
  // Salon slug'ı
  slug: z
    .string()
    .min(3, 'Slug en az 3 karakter olmalıdır')
    .max(100, 'Slug en fazla 100 karakter olabilir')
    .regex(/^[a-z0-9-]+$/, {
      message: 'Slug sadece küçük harf, rakam ve tire içerebilir',
    })
    .optional()
    .or(z.literal('')),
  // Salon durumu
  status: z
    .enum(['active', 'passive', 'deleted'], 'Geçerli bir durum seçiniz')
    .optional(),
  // Logo URL'i
  logo_url: z
    .string()
    .optional()
    .refine(
      val => !val || val === '' || z.string().url().safeParse(val).success,
      {
        message: 'Geçerli bir URL giriniz',
      }
    ),
  // Kapak görseli URL'i
  cover_image_url: z
    .string()
    .optional()
    .refine(
      val => !val || val === '' || z.string().url().safeParse(val).success,
      {
        message: 'Geçerli bir URL giriniz',
      }
    ),
  // Maksimum kapasite
  max_capacity: z
    .string()
    .optional()
    .refine(
      val => !val || val === '' || (!isNaN(Number(val)) && Number(val) > 0),
      {
        message: 'Kapasite pozitif bir sayı olmalıdır',
      }
    ),
});

// Gym setup form için schema (company_id eklendi)
export const gymSetupSchema = z.object({
  company_id: z.string().min(1, 'Şirket ID gereklidir'),
  name: z
    .string()
    .max(100, 'Salon adı en fazla 100 karakter olabilir')
    .optional()
    .or(z.literal('')),
  description: z
    .string()
    .max(500, 'Açıklama en fazla 500 karakter olabilir')
    .optional()
    .or(z.literal('')),
  gym_phone: z
    .string()
    .optional()
    .refine(
      val => !val || val === '' || (val.length >= 10 && val.length <= 15),
      {
        message: 'Telefon numarası 10-15 karakter arasında olmalıdır',
      }
    ),
  address: z.string().min(1, 'Adres gereklidir').max(255, 'Adres çok uzun'),
  city: z.string().min(1, 'Şehir gereklidir').max(100, 'Şehir adı çok uzun'),
  district: z.string().min(1, 'İlçe gereklidir').max(100, 'İlçe adı çok uzun'),
  gym_type: z.enum(
    GYM_TYPES.map((type: { value: string }) => type.value) as [
      string,
      ...string[],
    ],
    'Geçerli bir salon türü seçiniz'
  ),
  opening_time: z
    .string()
    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
      message: 'Geçerli bir saat formatı giriniz (HH:MM)',
    })
    .optional()
    .or(z.literal('')),
  closing_time: z
    .string()
    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
      message: 'Geçerli bir saat formatı giriniz (HH:MM)',
    })
    .optional()
    .or(z.literal('')),
  // Salon özellikleri listesi
  features: z.array(z.string()).optional(),
  // Randevu saatleri listesi
  time_slots: z.array(z.string()).optional(),
  // Maksimum kapasite
  max_capacity: z
    .string()
    .optional()
    .refine(
      val => !val || val === '' || (!isNaN(Number(val)) && Number(val) > 0),
      {
        message: 'Kapasite pozitif bir sayı olmalıdır',
      }
    ),
  // Kapak görseli URL'i
  cover_image_url: z
    .string()
    .optional()
    .refine(
      val => !val || val === '' || z.string().url().safeParse(val).success,
      {
        message: 'Geçerli bir URL giriniz',
      }
    ),
});

export const gymSearchSchema = z.object({
  query: z.string().optional(),
  city: z.string().optional(),
  features: z.array(z.string()).optional(),
});

// Staff management schemas
export const createStaffSchema = z.object({
  name: z
    .string()
    .min(1, 'Ad gereklidir')
    .max(50, 'Ad en fazla 50 karakter olabilir'),
  surname: z
    .string()
    .min(1, 'Soyad gereklidir')
    .max(50, 'Soyad en fazla 50 karakter olabilir'),
  hire_date: z
    .string()
    .min(1, 'İşe başlama tarihi gereklidir')
    .refine(date => !isNaN(Date.parse(date)), {
      message: 'Geçerli bir tarih giriniz',
    }),
  salary_amount: z
    .number()
    .min(0, 'Maaş negatif olamaz')
    .max(999999.99, 'Maaş çok yüksek')
    .optional(),
  staff_type: z.enum(
    ['cleaner', 'security', 'reception', 'maintenance', 'other'],
    'Personel tipi seçiniz'
  ),
});

// Notification schemas
export const baseNotificationSchema = z.object({
  recipientUserId: z.uuid('Geçerli kullanıcı ID gereklidir'),
  title: z.string().min(1, 'Başlık gereklidir').max(255, 'Başlık çok uzun'),
  message: z.string().min(1, 'Mesaj gereklidir').max(1000, 'Mesaj çok uzun'),
});

export const gymNotificationSchema = z.object({
  recipientId: z.uuid('Geçerli alıcı ID gereklidir'),
  action: z.enum(
    ['request', 'accept', 'reject', 'invite', 'cancel'],
    'Geçerli aksiyon seçiniz'
  ),
  direction: z.enum(
    ['to_manager', 'to_member', 'to_trainer'],
    'Geçerli yön seçiniz'
  ),
  gymName: z.string().min(1, 'Salon adı gereklidir'),
  userName: z.string().min(1, 'Kullanıcı adı gereklidir'),
  invitationId: z.uuid('Geçerli davet ID gereklidir'),
});

export const markNotificationReadSchema = z.object({
  notificationId: z.uuid('Geçerli bildirim ID gereklidir'),
  userId: z.uuid('Geçerli kullanıcı ID gereklidir'),
});

export const getUserNotificationsSchema = z.object({
  userId: z.uuid('Geçerli kullanıcı ID gereklidir'),
  limit: z.number().min(1).max(100).default(10),
});

export const deleteNotificationSchema = z.object({
  notificationId: z.uuid('Geçerli bildirim ID gereklidir'),
  userId: z.uuid('Geçerli kullanıcı ID gereklidir'),
});

export const deleteAllNotificationsSchema = z.object({
  userId: z.uuid('Geçerli kullanıcı ID gereklidir'),
});

export const updateStaffSchema = z.object({
  name: z
    .string()
    .min(1, 'Ad gereklidir')
    .max(50, 'Ad en fazla 50 karakter olabilir')
    .optional(),
  surname: z
    .string()
    .min(1, 'Soyad gereklidir')
    .max(50, 'Soyad en fazla 50 karakter olabilir')
    .optional(),
  hire_date: z
    .string()
    .min(1, 'İşe başlama tarihi gereklidir')
    .refine(date => !isNaN(Date.parse(date)), {
      message: 'Geçerli bir tarih giriniz',
    })
    .optional(),
  salary_amount: z.number().min(0, 'Maaş negatif olamaz').optional(),
  staff_type: z
    .enum(['cleaner', 'security', 'reception', 'maintenance', 'other'])
    .optional(),
});

// Profile update schema
export const updateProfileSchema = z.object({
  full_name: z
    .string()
    .min(1, 'Ad soyad gereklidir')
    .max(100, 'Ad soyad en fazla 100 karakter olabilir'),
});

// Member onboarding schema
export const updateMemberOnboardingSchema = z.object({
  full_name: z
    .string()
    .min(1, 'Ad soyad gereklidir')
    .max(100, 'Ad soyad en fazla 100 karakter olabilir')
    .optional(),
  age: z.coerce
    .number()
    .min(16, "Yaş 16'dan küçük olamaz")
    .max(120, "Yaş 120'den büyük olamaz")
    .optional(),
  gender: z.enum(['male', 'female', 'other', 'prefer_not_to_say']).optional(),
  height_cm: z.coerce
    .number()
    .min(100, "Boy 100cm'den küçük olamaz")
    .max(250, "Boy 250cm'den büyük olamaz")
    .optional(),
  weight_kg: z.coerce
    .number()
    .min(30, "Kilo 30kg'dan küçük olamaz")
    .max(300, "Kilo 300kg'dan büyük olamaz")
    .optional(),
  fitness_goal: z
    .string()
    .max(500, 'Fitness hedefi en fazla 500 karakter olabilir')
    .optional(),
});

// Member details güncelleme schema
export const upsertMemberDetailsSchema = z.object({
  age: z.coerce
    .number()
    .min(16, "Yaş 16'dan küçük olamaz")
    .max(120, "Yaş 120'den büyük olamaz")
    .optional(),
  gender: z.enum(['male', 'female', 'other', 'prefer_not_to_say']).optional(),
  height_cm: z.coerce
    .number()
    .min(100, "Boy 100cm'den küçük olamaz")
    .max(250, "Boy 250cm'den büyük olamaz")
    .optional(),
  weight_kg: z.coerce
    .number()
    .min(30, "Kilo 30kg'dan küçük olamaz")
    .max(300, "Kilo 300kg'dan büyük olamaz")
    .optional(),
  fitness_goal: z
    .string()
    .max(500, 'Fitness hedefi en fazla 500 karakter olabilir')
    .optional(),
});

// Trainer details güncelleme schema
export const updateTrainerDetailsSchema = z.object({
  specialization: z
    .string()
    .max(100, 'Uzmanlık alanı en fazla 100 karakter olabilir')
    .optional(),
  certification_level: z
    .string()
    .max(100, 'Sertifika seviyesi en fazla 100 karakter olabilir')
    .optional(),
  experience_years: z.coerce
    .number()
    .min(0, 'Deneyim yılı negatif olamaz')
    .max(50, "Deneyim yılı 50'den büyük olamaz")
    .optional(),
  bio: z
    .string()
    .max(500, 'Biyografi en fazla 500 karakter olabilir')
    .optional(),
});
