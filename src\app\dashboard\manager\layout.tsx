import { Metadata } from 'next';
import { DashboardSidebar } from '@/components/dashboard/DashboardSidebar';

export const metadata: Metadata = {
  title: 'Yönetici Paneli | Sportiva',
  description: 'Sportiva spor salonu yönetim sistemi yönetici paneli.',
  robots: {
    index: false,
    follow: false,
  },
};

export default function ManagerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Manager Sidebar */}
      <DashboardSidebar mode="manager" />
      {/* Main Content Area */}
      {children}
    </>
  );
}
