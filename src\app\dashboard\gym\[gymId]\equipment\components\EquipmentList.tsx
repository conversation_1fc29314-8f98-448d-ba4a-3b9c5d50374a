'use client';

import { useState } from 'react';
import { MoreHorizontal, Edit, Trash2, MapPin, Package } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  GymEquipment,
  EquipmentCategory,
  EQUIPMENT_CONDITIONS,
  EQUIPMENT_STATUSES,
} from '@/types/database/equipment-inventory';
import { deleteEquipment } from '@/lib/actions/dashboard/company/equipment-actions';
import { toast } from 'sonner';

interface EquipmentListProps {
  equipment: GymEquipment[];
  categories: EquipmentCategory[];
  onCreateEquipment?: () => void;
  onEditEquipment?: (equipment: GymEquipment) => void;
}

export function EquipmentList({
  equipment,
  categories,
  onCreateEquipment,
  onEditEquipment,
}: EquipmentListProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [equipmentToDelete, setEquipmentToDelete] =
    useState<GymEquipment | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const getConditionConfig = (condition: string) => {
    return (
      EQUIPMENT_CONDITIONS.find(c => c.value === condition) || {
        value: condition,
        label: condition,
        color: 'gray',
      }
    );
  };

  const getStatusConfig = (status: string) => {
    return (
      EQUIPMENT_STATUSES.find(s => s.value === status) || {
        value: status,
        label: status,
        color: 'gray',
      }
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  const handleDeleteClick = (equipment: GymEquipment) => {
    setEquipmentToDelete(equipment);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!equipmentToDelete) return;

    setIsDeleting(true);
    try {
      const result = await deleteEquipment(equipmentToDelete.id);
      if (result.success) {
        toast.success('Ekipman başarıyla silindi');
      } else {
        toast.error(result.error || 'Ekipman silinirken hata oluştu');
      }
    } catch (error) {
      toast.error('Beklenmeyen bir hata oluştu');
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setEquipmentToDelete(null);
    }
  };

  if (equipment.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Package className="text-muted-foreground mb-4 h-12 w-12" />
          <h3 className="mb-2 text-lg font-semibold">Henüz ekipman yok</h3>
          <p className="text-muted-foreground mb-4 text-center">
            İlk ekipmanınızı ekleyerek başlayın
          </p>
          <Button onClick={onCreateEquipment}>Ekipman Ekle</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {equipment.map(item => {
          const conditionConfig = getConditionConfig(item.condition);
          const statusConfig = getStatusConfig(item.status);
          const category = categories.find(c => c.id === item.category_id);

          return (
            <Card
              key={item.id}
              className="group transition-shadow hover:shadow-md"
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="min-w-0 flex-1">
                    <h3 className="truncate font-semibold">{item.name}</h3>
                    {item.brand && item.model && (
                      <p className="text-muted-foreground truncate text-sm">
                        {item.brand} {item.model}
                      </p>
                    )}
                    {category && (
                      <Badge variant="outline" className="mt-1 text-xs">
                        {category.name}
                      </Badge>
                    )}
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 opacity-0 transition-opacity group-hover:opacity-100"
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onEditEquipment?.(item)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Düzenle
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteClick(item)}
                        className="text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Sil
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Status and Condition */}
                <div className="flex gap-2">
                  <Badge
                    variant={
                      statusConfig.color === 'green' ? 'default' : 'secondary'
                    }
                    className="text-xs"
                  >
                    {statusConfig.label}
                  </Badge>
                  <Badge
                    variant="outline"
                    className={`text-xs text-${conditionConfig.color}-600`}
                  >
                    {conditionConfig.label}
                  </Badge>
                </div>

                {/* Location */}
                {item.location && (
                  <div className="text-muted-foreground flex items-center gap-2 text-sm">
                    <MapPin className="h-4 w-4" />
                    <span className="truncate">{item.location}</span>
                  </div>
                )}

                {/* Purchase Info */}
                <div className="space-y-1 text-sm">
                  {item.purchase_date && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Satın Alma:</span>
                      <span>{formatDate(item.purchase_date)}</span>
                    </div>
                  )}
                  {item.purchase_price && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Fiyat:</span>
                      <span>{formatCurrency(item.purchase_price)}</span>
                    </div>
                  )}
                  {item.warranty_expiry && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Garanti:</span>
                      <span
                        className={
                          new Date(item.warranty_expiry) < new Date()
                            ? 'text-red-600'
                            : 'text-green-600'
                        }
                      >
                        {formatDate(item.warranty_expiry)}
                      </span>
                    </div>
                  )}
                </div>

                {/* Serial Number */}
                {item.serial_number && (
                  <div className="text-muted-foreground text-xs">
                    S/N: {item.serial_number}
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Ekipmanı Sil</AlertDialogTitle>
            <AlertDialogDescription>
              `&quot;{equipmentToDelete?.name}`&quot; ekipmanını silmek
              istediğinizden emin misiniz? Bu işlem geri alınamaz ve tüm bakım
              kayıtları da silinecektir.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Siliniyor...' : 'Sil'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
