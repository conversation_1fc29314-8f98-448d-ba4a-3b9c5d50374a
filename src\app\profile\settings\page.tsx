import { ProfileInformation } from '@/components/profile-settings/ProfileInformation';
import { getProfile } from '@/lib/actions/user/profile-actions';
import { getAuthenticatedUser } from '@/lib/auth/server-auth';
import { Profiles } from '@/types/database/tables';

export default async function SettingsPage() {
  // Server action ile profil verilerini çek
  const profileResult = await getProfile();
  // Kullanıcı auth bilgilerini çek
  const user = await getAuthenticatedUser();
  const profile: Profiles | null =
    profileResult.success && profileResult.data ? profileResult.data : null;

  // Hata durumunda konsola log
  if (!profileResult.success) {
    console.error('Profile fetch error:', profileResult.error);
  }

  return (
    <div className="flex-1 space-y-6 p-6 lg:p-8">
      {/* Sayfa başlığı */}
      <div className="space-y-2">
        <h1 className="text-2xl font-semibold tracking-tight">
          Profil Bilgileri
        </h1>
        <p className="text-muted-foreground">
          Temel profil bilgilerinizi ve profil fotoğrafınızı güncelleyin
        </p>
      </div>
      {/* Profil bilgileri formu */}
      <div className="space-y-6">
        <ProfileInformation profile={profile} user={user} />
      </div>
    </div>
  );
}
