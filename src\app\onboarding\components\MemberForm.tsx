'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { EnhancedInput } from '@/components/ui/enhanced-input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { User } from 'lucide-react';
import { FITNESS_GOAL_OPTIONS } from '@/lib/constants';
import { INPUT_RULES } from '@/lib/utils/form-validation';

interface MemberData {
  age: string;
  gender: string;
  height_cm: string;
  weight_kg: string;
  fitness_goal: string;
}

interface MemberFormProps {
  data: MemberData;
  onChange: (data: MemberData) => void;
}

export function MemberForm({ data, onChange }: MemberFormProps) {
  const handleChange = (field: keyof MemberData, value: string) => {
    onChange({ ...data, [field]: value });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <User className="mr-2 h-5 w-5" />
          Üye Bilgileri
        </CardTitle>
        <CardDescription>Temel kişisel bilgilerinizi girin</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <Label htmlFor="age">Yaş *</Label>
            <EnhancedInput
              id="age"
              type="text"
              placeholder="Yaşınızı girin"
              value={data.age}
              onChange={value => handleChange('age', value)}
              validator={value => {
                const num = parseInt(value);
                return (
                  !isNaN(num) &&
                  num >= INPUT_RULES.AGE.min &&
                  num <= INPUT_RULES.AGE.max
                );
              }}
              validationMessage={`Yaş ${INPUT_RULES.AGE.min}-${INPUT_RULES.AGE.max} arasında olmalıdır`}
              required
            />
          </div>
          <div>
            <Label htmlFor="gender">Cinsiyet *</Label>
            <Select
              value={data.gender}
              onValueChange={value => handleChange('gender', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Cinsiyet seçin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="male">Erkek</SelectItem>
                <SelectItem value="female">Kadın</SelectItem>
                <SelectItem value="prefer_not_to_say">
                  Belirtmek istemiyorum
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <Label htmlFor="height">Boy (cm)</Label>
            <EnhancedInput
              id="height"
              type="text"
              placeholder="Örn: 175"
              value={data.height_cm}
              onChange={value => handleChange('height_cm', value)}
              validator={value => {
                if (!value) return true; // Optional field
                const num = parseInt(value);
                return (
                  !isNaN(num) &&
                  num >= INPUT_RULES.HEIGHT.min &&
                  num <= INPUT_RULES.HEIGHT.max
                );
              }}
              validationMessage={`Boy ${INPUT_RULES.HEIGHT.min}-${INPUT_RULES.HEIGHT.max} cm arasında olmalıdır`}
            />
          </div>
          <div>
            <Label htmlFor="weight">Kilo (kg)</Label>
            <EnhancedInput
              id="weight"
              type="text"
              placeholder="Örn: 70"
              value={data.weight_kg}
              onChange={value => handleChange('weight_kg', value)}
              validator={value => {
                if (!value) return true; // Optional field
                const num = parseInt(value);
                return (
                  !isNaN(num) &&
                  num >= INPUT_RULES.WEIGHT.min &&
                  num <= INPUT_RULES.WEIGHT.max
                );
              }}
              validationMessage={`Kilo ${INPUT_RULES.WEIGHT.min}-${INPUT_RULES.WEIGHT.max} kg arasında olmalıdır`}
            />
          </div>
        </div>

        <div>
          <Label htmlFor="fitness_goal">Fitness Hedefi</Label>
          <Select
            value={data.fitness_goal}
            onValueChange={value => handleChange('fitness_goal', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Hedef seçin" />
            </SelectTrigger>
            <SelectContent>
              {FITNESS_GOAL_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.emoji} {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
}
