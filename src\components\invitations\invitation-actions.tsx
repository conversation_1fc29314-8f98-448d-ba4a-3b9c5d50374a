'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Check, X, Trash2, Loader2 } from 'lucide-react';
import { isInvitationExpired } from './types';
import type { UnifiedInvitation } from './types';

interface InvitationActionsProps {
  invitation: UnifiedInvitation;
  onAction?: (invitationId: string, action: 'accept' | 'reject' | 'cancel') => Promise<void>;
  compact?: boolean;
}

export function InvitationActions({ invitation, onAction, compact = false }: InvitationActionsProps) {
  const [loadingAction, setLoadingAction] = useState<string | null>(null);
  
  const expired = isInvitationExpired(invitation);
  const isPending = invitation.status === 'pending' && !expired;
  const isIncoming = invitation.direction === 'incoming';
  const isOutgoing = invitation.direction === 'outgoing';

  const handleAction = async (action: 'accept' | 'reject' | 'cancel') => {
    if (!onAction || loadingAction) return;
    
    setLoadingAction(action);
    try {
      await onAction(invitation.id, action);
    } finally {
      setLoadingAction(null);
    }
  };

  // No actions for non-pending or expired invitations
  if (!isPending) {
    return null;
  }

  // Incoming invitations: Accept/Reject
  if (isIncoming) {
    return (
      <div className={`flex gap-2 ${compact ? 'flex-col' : ''}`}>
        <Button
          size={compact ? 'sm' : 'default'}
          onClick={() => handleAction('accept')}
          disabled={!!loadingAction}
          className="bg-success text-success-foreground hover:bg-success/90"
        >
          {loadingAction === 'accept' ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Check className="h-4 w-4" />
          )}
          {!compact && <span className="ml-1">Kabul Et</span>}
        </Button>
        
        <Button
          size={compact ? 'sm' : 'default'}
          variant="destructive"
          onClick={() => handleAction('reject')}
          disabled={!!loadingAction}
        >
          {loadingAction === 'reject' ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <X className="h-4 w-4" />
          )}
          {!compact && <span className="ml-1">Reddet</span>}
        </Button>
      </div>
    );
  }

  // Outgoing invitations: Cancel
  if (isOutgoing) {
    return (
      <Button
        size={compact ? 'sm' : 'default'}
        variant="outline"
        onClick={() => handleAction('cancel')}
        disabled={!!loadingAction}
      >
        {loadingAction === 'cancel' ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Trash2 className="h-4 w-4" />
        )}
        {!compact && <span className="ml-1">İptal Et</span>}
      </Button>
    );
  }

  return null;
}
