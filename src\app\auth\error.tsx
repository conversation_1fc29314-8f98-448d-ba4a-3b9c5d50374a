'use client';

import { RouteError } from '@/components/errors/route-error';

export default function AuthError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <RouteError
      title="Kimlik Doğrulama Hatası"
      description="Giriş/Kayıt sayfalarında beklenmeyen bir hata oluştu."
      error={error}
      reset={reset}
      context={{ route: '/auth' }}
    />
  );
}
