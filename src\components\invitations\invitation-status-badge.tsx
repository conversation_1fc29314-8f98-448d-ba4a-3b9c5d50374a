import { Badge } from '@/components/ui/badge';
import { Alert<PERSON>riangle, CheckCircle, Clock, XCircle } from 'lucide-react';
import { isInvitationExpired } from './types';
import type { UnifiedInvitation } from './types';

interface InvitationStatusBadgeProps {
  invitation: UnifiedInvitation;
  className?: string;
}

export function InvitationStatusBadge({ invitation, className }: InvitationStatusBadgeProps) {
  const expired = isInvitationExpired(invitation);
  const status = expired ? 'expired' : invitation.status;
  
  const getStatusConfig = () => {
    switch (status) {
      case 'pending':
        return {
          variant: 'default' as const,
          icon: Clock,
          label: 'Bekliyor',
          className: 'bg-warning text-warning-foreground',
        };
      case 'accepted':
        return {
          variant: 'default' as const,
          icon: CheckCircle,
          label: 'Kabul Edildi',
          className: 'bg-success text-success-foreground',
        };
      case 'rejected':
        return {
          variant: 'destructive' as const,
          icon: XCircle,
          label: 'Reddedildi',
          className: 'bg-error text-error-foreground',
        };
      case 'expired':
        return {
          variant: 'destructive' as const,
          icon: AlertTriangle,
          label: '<PERSON><PERSON><PERSON><PERSON>',
          className: 'bg-error text-error-foreground',
        };
      default:
        return {
          variant: 'secondary' as const,
          icon: Clock,
          label: status,
          className: '',
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <Badge 
      variant={config.variant} 
      className={`${config.className} ${className}`}
    >
      <Icon className="mr-1 h-3 w-3" />
      {config.label}
    </Badge>
  );
}
