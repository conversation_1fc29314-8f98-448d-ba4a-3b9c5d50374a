'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Companies } from '@/types/database/tables';
import { Button } from '@/components/ui/button';
import { EnhancedInput } from '@/components/ui/enhanced-input';
import { Label } from '@/components/ui/label';
import { SocialLinksForm } from '@/components/forms/SocialLinksForm';
import { INPUT_RULES } from '@/lib/utils/form-validation';
import { ImageUpload } from '@/components/ui/image-upload';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Loader2,
  Building2,
  Phone,
  Save,
  ArrowLeft,
  ImagePlus,
} from 'lucide-react';
import {
  updateManagerCompanySettings,
  uploadCompanyLogo,
} from '@/lib/actions/all-actions';
import { toast } from 'sonner';
import Link from 'next/link';

interface CompanySettingsFormProps {
  company: Companies;
}

export function CompanySettingsForm({ company }: CompanySettingsFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [logoUploading, setLogoUploading] = useState(false);

  const [socialLinks, setSocialLinks] = useState(
    (company.social_links as Record<string, string>) || {}
  );
  const [formData, setFormData] = useState({
    name: company.name,
    phone: company.phone || '',
    email: company.email || '',
    status: company.status || 'active',
    logo_url: company.logo_url || '',
  });

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleLogoUpload = async (file: File) => {
    setLogoUploading(true);
    try {
      const result = await uploadCompanyLogo(file);
      if (result.success && result.url) {
        setFormData(prev => ({ ...prev, logo_url: result.url! }));
        toast.success('Logo başarıyla yüklendi!');
      } else {
        toast.error(result.error || 'Logo yüklenirken hata oluştu');
      }
    } catch (error) {
      console.error('Logo upload error:', error);
      toast.error('Logo yüklenirken beklenmeyen hata oluştu');
    } finally {
      setLogoUploading(false);
    }
  };

  async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();
    setIsLoading(true);

    try {
      const submitFormData = new FormData();
      submitFormData.append('name', formData.name);
      submitFormData.append('phone', formData.phone);
      submitFormData.append('email', formData.email);
      submitFormData.append('status', formData.status);
      submitFormData.append('logo_url', formData.logo_url);

      // Sosyal medya linklerini JSON string olarak ekle
      submitFormData.append('social_links', JSON.stringify(socialLinks));

      const result = await updateManagerCompanySettings(submitFormData);

      if (result.success) {
        toast.success('Şirket bilgileri başarıyla güncellendi!');
        router.refresh();
      } else {
        toast.error(result.error || 'Güncelleme sırasında bir hata oluştu');
      }
    } catch (error) {
      console.error('Company update error:', error);
      toast.error('Beklenmeyen bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Visual Assets */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImagePlus className="text-primary h-5 w-5" />
            Görsel Öğeler
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex justify-center">
            {/* Logo Upload */}
            <div className="w-full max-w-sm space-y-2">
              <Label className="block text-center">Şirket Logosu</Label>
              <ImageUpload
                currentImageUrl={formData.logo_url}
                onImageUpload={handleLogoUpload}
                isUploading={logoUploading}
                disabled={isLoading || logoUploading}
                aspectRatio="square"
                maxSizeMB={2}
                acceptedTypes={['image/jpeg', 'image/png', 'image/webp']}
                uploadText="Logo Yükle"
                className="w-full"
              />
              <p className="text-muted-foreground text-center text-xs">
                Önerilen boyut: 200x200px, Max: 2MB
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="text-primary h-5 w-5" />
            Temel Bilgiler
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label htmlFor="name">Şirket Adı *</Label>
              <EnhancedInput
                id="name"
                name="name"
                value={formData.name}
                onChange={value => handleInputChange('name', value)}
                formatter={INPUT_RULES.COMPANY_NAME.formatter}
                validator={INPUT_RULES.COMPANY_NAME.validator}
                maxLength={INPUT_RULES.COMPANY_NAME.maxLength}
                validationMessage="Şirket adı en az 2 karakter olmalıdır"
                required
                disabled={isLoading}
              />
            </div>

            <div>
              <Label htmlFor="status">Şirket Durumu</Label>
              <Select
                value={formData.status}
                onValueChange={value => handleInputChange('status', value)}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Durum seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Aktif</SelectItem>
                  <SelectItem value="passive">Pasif</SelectItem>
                  <SelectItem value="suspended">Askıya Alınmış</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="text-primary h-5 w-5" />
            İletişim Bilgileri
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label htmlFor="phone">Telefon</Label>
              <EnhancedInput
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={value => handleInputChange('phone', value)}
                placeholder="5551234567"
                formatter={INPUT_RULES.PHONE.formatter}
                validator={INPUT_RULES.PHONE.validator}
                maxLength={INPUT_RULES.PHONE.maxLength}
                validationMessage="Geçerli bir telefon numarası giriniz (10-11 haneli)"
                disabled={isLoading}
              />
            </div>

            <div>
              <Label htmlFor="email">E-posta</Label>
              <EnhancedInput
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={value => handleInputChange('email', value)}
                placeholder="<EMAIL>"
                validator={INPUT_RULES.EMAIL.validator}
                maxLength={INPUT_RULES.EMAIL.maxLength}
                validationMessage="Geçerli bir e-posta adresi giriniz"
                disabled={isLoading}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Social Links */}
      <SocialLinksForm
        initialData={socialLinks}
        onChange={setSocialLinks}
        disabled={isLoading}
      />

      <Separator />

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Link href="/dashboard/manager">
          <Button type="button" variant="outline" disabled={isLoading}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Geri Dön
          </Button>
        </Link>

        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Kaydediliyor...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Değişiklikleri Kaydet
            </>
          )}
        </Button>
      </div>
    </form>
  );
}
